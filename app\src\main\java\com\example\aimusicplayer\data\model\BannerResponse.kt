package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 轮播图响应
 * 使用MVVM架构
 */
data class BannerResponse(
    @SerializedName("code")
    val code: Int = 0,
    
    @SerializedName("message")
    val message: String? = null,
    
    @SerializedName("data")
    val data: Data? = null
) {
    data class Data(
        @SerializedName("banners")
        val banners: List<Banner>? = null
    )
    
    /**
     * 从Java版本的BannerResponse创建
     */
    companion object {
        @JvmStatic
        fun fromJavaBannerResponse(response: BannerResponse): BannerResponse {
            val data = response.data?.let { javaData ->
                Data(
                    banners = javaData.banners?.map { Banner.fromJavaBannerResponse(it) }
                )
            }
            
            return BannerResponse(
                code = response.code,
                message = response.message,
                data = data
            )
        }
    }
}
