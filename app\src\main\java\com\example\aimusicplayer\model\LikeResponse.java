package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

/**
 * 收藏/取消收藏歌曲响应
 */
public class LikeResponse {
    
    @SerializedName("code")
    private int code;
    
    @SerializedName("message")
    private String message;
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    @Override
    public String toString() {
        return "LikeResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
