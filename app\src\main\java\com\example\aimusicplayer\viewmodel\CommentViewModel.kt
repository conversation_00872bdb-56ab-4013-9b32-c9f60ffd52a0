package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.CommentResponse
import com.example.aimusicplayer.data.repository.CommentRepository
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 评论ViewModel
 * 使用Flow管理UI状态
 */
@HiltViewModel
class CommentViewModel @Inject constructor(
    application: Application,
    private val musicRepository: MusicRepository,
    private val commentRepository: CommentRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "CommentViewModel"

        // 评论类型
        const val TYPE_SONG = 0
        const val TYPE_MV = 1
        const val TYPE_PLAYLIST = 2
        const val TYPE_ALBUM = 3
        const val TYPE_RADIO = 4
        const val TYPE_VIDEO = 5
        const val TYPE_DYNAMIC = 6
    }

    // 评论列表
    private val _commentsFlow = MutableStateFlow<List<Comment>>(emptyList())
    val commentsFlow: StateFlow<List<Comment>> = _commentsFlow.asStateFlow()
    val comments: LiveData<List<Comment>> = commentsFlow.asLiveData() // 兼容LiveData

    // 热门评论
    private val _hotCommentsFlow = MutableStateFlow<List<Comment>>(emptyList())
    val hotCommentsFlow: StateFlow<List<Comment>> = _hotCommentsFlow.asStateFlow()
    val hotComments: LiveData<List<Comment>> = hotCommentsFlow.asLiveData() // 兼容LiveData

    // 加载状态已经在FlowViewModel中定义，这里直接使用
    override val loading: LiveData<Boolean> = loadingFlow.asLiveData() // 兼容LiveData

    // 错误信息已经在FlowViewModel中定义，这里直接使用
    override val errorMessage: LiveData<String> = errorMessageFlow.asLiveData() // 兼容LiveData

    // 评论发送成功状态
    private val _commentSentFlow = MutableStateFlow<Boolean>(false)
    val commentSentFlow: StateFlow<Boolean> = _commentSentFlow.asStateFlow()
    val commentSent: LiveData<Boolean> = commentSentFlow.asLiveData() // 兼容LiveData

    // 是否有更多数据
    private val _hasMoreDataFlow = MutableStateFlow<Boolean>(true)
    val hasMoreDataFlow: StateFlow<Boolean> = _hasMoreDataFlow.asStateFlow()
    val hasMoreData: LiveData<Boolean> = hasMoreDataFlow.asLiveData() // 兼容LiveData

    // 是否正在加载更多
    private val _isLoadingMoreFlow = MutableStateFlow<Boolean>(false)
    val isLoadingMoreFlow: StateFlow<Boolean> = _isLoadingMoreFlow.asStateFlow()
    val isLoadingMore: LiveData<Boolean> = isLoadingMoreFlow.asLiveData() // 兼容LiveData

    // 评论总数
    private val _commentCountFlow = MutableStateFlow<Int>(0)
    val commentCountFlow: StateFlow<Int> = _commentCountFlow.asStateFlow()
    val commentCount: LiveData<Int> = commentCountFlow.asLiveData() // 兼容LiveData

    // 当前资源ID
    private val _resourceIdFlow = MutableStateFlow<Long>(0)
    val resourceIdFlow: StateFlow<Long> = _resourceIdFlow.asStateFlow()

    // 当前资源类型
    private val _resourceTypeFlow = MutableStateFlow<Int>(TYPE_SONG)
    val resourceTypeFlow: StateFlow<Int> = _resourceTypeFlow.asStateFlow()

    // 当前页码
    private var currentPage = 0

    // 每页数量
    private val pageSize = 20

    /**
     * 加载评论
     * @param songId 歌曲ID
     * @param forceRefresh 是否强制刷新
     */
    fun loadComments(songId: Long, forceRefresh: Boolean = false) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载评论失败", e)
                handleError(e, "加载评论失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 重置分页状态
                _hasMoreDataFlow.value = true
                currentPage = 0

                // 更新资源信息
                _resourceIdFlow.value = songId
                _resourceTypeFlow.value = TYPE_SONG

                // 使用旧的API（兼容）
                val commentList = musicRepository.getComments(songId, 0, pageSize)
                _commentsFlow.value = commentList

                // 检查是否有更多数据
                _hasMoreDataFlow.value = commentList.size >= pageSize
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载评论（使用新的API）
     * @param type 资源类型
     * @param id 资源ID
     * @param forceRefresh 是否强制刷新
     */
    fun loadCommentsByType(type: Int, id: Long, forceRefresh: Boolean = false) {
        if (id == 0L) return

        // 重置状态
        if (forceRefresh || id != _resourceIdFlow.value || type != _resourceTypeFlow.value) {
            _commentsFlow.value = emptyList()
            _hotCommentsFlow.value = emptyList()
            _commentCountFlow.value = 0
            _hasMoreDataFlow.value = true
            currentPage = 0
        }

        // 更新资源信息
        _resourceIdFlow.value = id
        _resourceTypeFlow.value = type

        // 加载评论
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载评论失败", e)
                handleError(e, "加载评论失败: ${e.message}")
            }
        ) {
            setLoading(true)

            try {
                when (type) {
                    TYPE_SONG -> {
                        commentRepository.getSongComments(
                            id = id,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                            forceRefresh = forceRefresh
                        ).collect { result ->
                            handleCommentResult(result, forceRefresh)
                        }
                    }
                    TYPE_ALBUM -> {
                        commentRepository.getAlbumComments(
                            id = id,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                            forceRefresh = forceRefresh
                        ).collect { result ->
                            handleCommentResult(result, forceRefresh)
                        }
                    }
                    TYPE_PLAYLIST -> {
                        commentRepository.getPlaylistComments(
                            id = id,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                            forceRefresh = forceRefresh
                        ).collect { result ->
                            handleCommentResult(result, forceRefresh)
                        }
                    }
                    else -> {
                        // 暂不支持其他类型，使用旧的API
                        if (type == TYPE_SONG) {
                            loadComments(id, forceRefresh)
                        } else {
                            setLoading(false)
                            handleError(Exception("暂不支持该类型的评论"))
                        }
                    }
                }
            } catch (e: Exception) {
                setLoading(false)
                throw e
            }
        }
    }

    /**
     * 加载更多评论
     * @param songId 歌曲ID
     * @param page 页码
     * @param pageSize 每页数量
     */
    fun loadMoreComments(songId: Long, page: Int, pageSize: Int) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载更多评论失败", e)
                handleError(e, "加载更多评论失败: ${e.message}")
            }
        ) {
            _isLoadingMoreFlow.value = true

            try {
                val offset = page * pageSize
                val newComments = musicRepository.getComments(songId, offset, pageSize)

                // 合并评论列表
                val currentList = _commentsFlow.value
                val mergedList = currentList + newComments
                _commentsFlow.value = mergedList

                // 检查是否有更多数据
                _hasMoreDataFlow.value = newComments.size >= pageSize
            } catch (e: Exception) {
                Log.e(TAG, "加载更多评论失败", e)
                throw e
            } finally {
                _isLoadingMoreFlow.value = false
            }
        }
    }

    /**
     * 加载更多评论（使用新的API）
     */
    fun loadMoreCommentsByType() {
        if (!_hasMoreDataFlow.value || _isLoadingMoreFlow.value) return

        _isLoadingMoreFlow.value = true
        currentPage++

        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载更多评论失败", e)
                handleError(e, "加载更多评论失败: ${e.message}")
                _isLoadingMoreFlow.value = false
            }
        ) {
            try {
                when (_resourceTypeFlow.value) {
                    TYPE_SONG -> {
                        commentRepository.getSongComments(
                            id = _resourceIdFlow.value,
                            limit = pageSize,
                            offset = currentPage * pageSize
                        ).collect { result ->
                            handleMoreCommentResult(result)
                        }
                    }
                    TYPE_ALBUM -> {
                        commentRepository.getAlbumComments(
                            id = _resourceIdFlow.value,
                            limit = pageSize,
                            offset = currentPage * pageSize
                        ).collect { result ->
                            handleMoreCommentResult(result)
                        }
                    }
                    TYPE_PLAYLIST -> {
                        commentRepository.getPlaylistComments(
                            id = _resourceIdFlow.value,
                            limit = pageSize,
                            offset = currentPage * pageSize
                        ).collect { result ->
                            handleMoreCommentResult(result)
                        }
                    }
                    else -> {
                        // 暂不支持其他类型，使用旧的API
                        if (_resourceTypeFlow.value == TYPE_SONG) {
                            loadMoreComments(_resourceIdFlow.value, currentPage, pageSize)
                        } else {
                            handleError(Exception("暂不支持该类型的评论"))
                        }
                    }
                }
            } catch (e: Exception) {
                throw e
            } finally {
                _isLoadingMoreFlow.value = false
            }
        }
    }

    /**
     * 发送评论
     * @param songId 歌曲ID
     * @param content 评论内容
     */
    fun sendComment(songId: Long, content: String) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "发送评论失败", e)
                handleError(e, "发送评论失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                val success = musicRepository.sendComment(songId, content)
                if (success) {
                    // 设置评论发送成功状态
                    _commentSentFlow.value = true
                    // 重新加载评论列表
                    loadComments(songId, true)
                } else {
                    handleError(Exception("发送评论失败"), "发送评论失败")
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 发送评论（使用新的API）
     * @param content 评论内容
     * @param commentId 回复的评论ID（如果是回复评论）
     */
    fun sendCommentByType(content: String, commentId: Long? = null) {
        if (content.isBlank()) {
            handleError(Exception("评论内容不能为空"), "评论内容不能为空")
            return
        }

        launchSafely(
            onError = { e ->
                Log.e(TAG, "发送评论失败", e)
                handleError(e, "发送评论失败: ${e.message}")
            }
        ) {
            setLoading(true)

            try {
                commentRepository.sendComment(
                    type = _resourceTypeFlow.value,
                    id = _resourceIdFlow.value,
                    content = content,
                    commentId = commentId
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            // 设置评论发送成功状态
                            _commentSentFlow.value = true
                            // 重新加载评论列表
                            loadCommentsByType(_resourceTypeFlow.value, _resourceIdFlow.value, true)
                        }
                        is NetworkResult.Error<*> -> {
                            setLoading(false)
                            handleError(Exception(result.message), result.message)
                        }
                        is NetworkResult.Loading<*> -> {
                            // 已在外层处理
                        }
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 重置评论发送状态
     */
    fun resetCommentSentState() {
        _commentSentFlow.value = false
    }

    /**
     * 点赞评论
     * @param commentId 评论ID
     */
    fun likeComment(commentId: Long) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "点赞评论失败", e)
                handleError(e, "点赞评论失败: ${e.message}")
            }
        ) {
            try {
                val success = musicRepository.likeComment(commentId)
                if (success) {
                    // 更新评论列表中的点赞状态
                    val currentList = _commentsFlow.value.toMutableList()
                    val updatedList = currentList.map { comment ->
                        if (comment.commentId == commentId) {
                            comment.copy(
                                liked = !comment.liked,
                                likeCount = if (comment.liked) comment.likeCount - 1 else comment.likeCount + 1
                            )
                        } else {
                            comment
                        }
                    }
                    _commentsFlow.value = updatedList
                }
            } catch (e: Exception) {
                Log.e(TAG, "点赞评论失败", e)
                throw e
            }
        }
    }

    /**
     * 点赞评论（使用新的API）
     * @param commentId 评论ID
     * @param like 是否点赞
     */
    fun likeCommentByType(commentId: Long, like: Boolean) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "点赞评论失败", e)
                handleError(e, "点赞评论失败: ${e.message}")
            }
        ) {
            try {
                commentRepository.likeComment(
                    type = _resourceTypeFlow.value,
                    id = _resourceIdFlow.value,
                    commentId = commentId,
                    like = like
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            // 更新评论列表中的点赞状态
                            updateCommentLikeStatus(commentId, like)
                        }
                        is NetworkResult.Error<*> -> {
                            handleError(Exception(result.message), result.message)
                        }
                        is NetworkResult.Loading<*> -> {
                            // 不显示加载状态
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "点赞评论失败", e)
                throw e
            }
        }
    }

    /**
     * 处理评论结果
     */
    private fun handleCommentResult(result: NetworkResult<CommentResponse>, forceRefresh: Boolean) {
        when (result) {
            is NetworkResult.Success -> {
                val response = result.data

                // 更新热门评论
                if (forceRefresh) {
                    _hotCommentsFlow.value = response.hotComments?.map { it.toComment() } ?: emptyList()
                }

                // 更新评论列表
                if (forceRefresh) {
                    _commentsFlow.value = response.comments?.map { it.toComment() } ?: emptyList()
                } else {
                    _commentsFlow.value = _commentsFlow.value + (response.comments?.map { it.toComment() } ?: emptyList())
                }

                // 更新评论总数
                _commentCountFlow.value = response.total

                // 更新是否还有更多
                _hasMoreDataFlow.value = response.hasMore

                setLoading(false)
            }
            is NetworkResult.Error<*> -> {
                setLoading(false)
                handleError(Exception(result.message), result.message)
            }
            is NetworkResult.Loading<*> -> {
                // 已在外层处理
            }
        }
    }

    /**
     * 处理加载更多评论结果
     */
    private fun handleMoreCommentResult(result: NetworkResult<CommentResponse>) {
        when (result) {
            is NetworkResult.Success -> {
                val response = result.data

                // 更新评论列表
                _commentsFlow.value = _commentsFlow.value + (response.comments?.map { it.toComment() } ?: emptyList())

                // 更新是否还有更多
                _hasMoreDataFlow.value = response.hasMore

                _isLoadingMoreFlow.value = false
            }
            is NetworkResult.Error<*> -> {
                _isLoadingMoreFlow.value = false
                handleError(Exception(result.message), result.message)
            }
            is NetworkResult.Loading<*> -> {
                // 已在外层处理
            }
        }
    }

    /**
     * 更新评论点赞状态
     */
    private fun updateCommentLikeStatus(commentId: Long, like: Boolean) {
        // 更新热门评论
        val updatedHotComments = _hotCommentsFlow.value.map { comment ->
            if (comment.commentId == commentId) {
                comment.copy(
                    liked = like,
                    likeCount = if (like) comment.likeCount + 1 else comment.likeCount - 1
                )
            } else {
                comment
            }
        }
        _hotCommentsFlow.value = updatedHotComments

        // 更新普通评论
        val updatedComments = _commentsFlow.value.map { comment ->
            if (comment.commentId == commentId) {
                comment.copy(
                    liked = like,
                    likeCount = if (like) comment.likeCount + 1 else comment.likeCount - 1
                )
            } else {
                comment
            }
        }
        _commentsFlow.value = updatedComments
    }
}
