package com.example.aimusicplayer.api;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.HttpException;
import retrofit2.Response;

/**
 * 统一的API响应处理类
 * 用于封装API响应，提供统一的错误处理
 */
public class ApiResponse<T> {
    private static final String TAG = "ApiResponse";

    // 响应状态
    public enum Status {
        SUCCESS,    // 成功
        ERROR,      // 错误
        LOADING     // 加载中
    }

    private Status status;
    private T data;
    private String message;
    private int code;
    private boolean isNetworkError;
    private boolean isApiError;
    private Throwable error;

    /**
     * 受保护的构造函数，允许子类继承
     */
    protected ApiResponse() {
    }

    /**
     * 成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.status = Status.SUCCESS;
        response.data = data;
        response.code = 200;
        return response;
    }

    /**
     * 错误响应
     * @param message 错误信息
     * @param code 错误码
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(String message, int code) {
        ApiResponse<T> response = new ApiResponse<>();
        response.status = Status.ERROR;
        response.message = message;
        response.code = code;
        response.isApiError = true;
        return response;
    }

    /**
     * 网络错误响应
     * @param throwable 错误
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> networkError(Throwable throwable) {
        ApiResponse<T> response = new ApiResponse<>();
        response.status = Status.ERROR;
        response.error = throwable;
        response.isNetworkError = true;
        response.message = getNetworkErrorMessage(throwable);
        response.code = -1;
        return response;
    }

    /**
     * 加载中响应
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> loading() {
        ApiResponse<T> response = new ApiResponse<>();
        response.status = Status.LOADING;
        return response;
    }

    /**
     * 从Retrofit响应创建API响应
     * @param response Retrofit响应
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> create(Response<T> response) {
        if (response.isSuccessful()) {
            T body = response.body();
            if (body != null) {
                return success(body);
            } else {
                return error("响应成功但数据为空", response.code());
            }
        } else {
            // 尝试解析错误响应
            ResponseBody errorBody = response.errorBody();
            if (errorBody != null) {
                try {
                    String errorJson = errorBody.string();
                    try {
                        // 尝试解析为JSON
                        JsonObject jsonObject = JsonParser.parseString(errorJson).getAsJsonObject();

                        int code = response.code();
                        String message = "未知错误";

                        if (jsonObject.has("code")) {
                            code = jsonObject.get("code").getAsInt();
                        }

                        if (jsonObject.has("message")) {
                            message = jsonObject.get("message").getAsString();
                        } else if (jsonObject.has("msg")) {
                            message = jsonObject.get("msg").getAsString();
                        }

                        // 特殊处理301错误（需要登录）
                        if (code == 301) {
                            message = "需要登录，请先登录后再尝试此操作";
                            Log.w(TAG, "API需要登录: " + response.raw().request().url());
                        }

                        return error(message, code);
                    } catch (JsonSyntaxException e) {
                        // 不是JSON格式，直接返回错误信息
                        return error(errorJson, response.code());
                    }
                } catch (IOException e) {
                    return error("读取错误响应失败: " + e.getMessage(), response.code());
                }
            } else {
                return error("服务器响应错误: " + response.code() + " " + response.message(), response.code());
            }
        }
    }

    /**
     * 获取状态
     * @return 状态
     */
    public Status getStatus() {
        return status;
    }

    /**
     * 获取数据
     * @return 数据
     */
    public T getData() {
        return data;
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取错误码
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取错误
     * @return 错误
     */
    public Throwable getError() {
        return error;
    }

    /**
     * 是否为网络错误
     * @return 是否为网络错误
     */
    public boolean isNetworkError() {
        return isNetworkError;
    }

    /**
     * 是否为API错误
     * @return 是否为API错误
     */
    public boolean isApiError() {
        return isApiError;
    }

    /**
     * 是否成功
     * @return 是否成功
     */
    public boolean isSuccessful() {
        return status == Status.SUCCESS && data != null;
    }

    /**
     * 是否正在加载
     * @return 是否正在加载
     */
    public boolean isLoading() {
        return status == Status.LOADING;
    }

    /**
     * 是否出错
     * @return 是否出错
     */
    public boolean isError() {
        return status == Status.ERROR;
    }

    /**
     * 统一处理API错误
     * @param throwable 错误
     * @param errorMessage 错误信息LiveData
     */
    public static void handleError(Throwable throwable, MutableLiveData<String> errorMessage) {
        String errorMsg = getNetworkErrorMessage(throwable);
        errorMessage.postValue(errorMsg);
    }

    /**
     * 获取网络错误消息
     * @param throwable 错误
     * @return 错误消息
     */
    public static String getNetworkErrorMessage(Throwable throwable) {
        String errorMsg;
        if (throwable instanceof IOException) {
            if (throwable instanceof SocketTimeoutException) {
                errorMsg = "连接超时，请检查网络设置";
            } else if (throwable instanceof UnknownHostException) {
                errorMsg = "无法连接到服务器，请检查网络设置";
            } else {
                errorMsg = "网络连接错误，请检查网络设置";
            }
        } else if (throwable instanceof HttpException) {
            HttpException httpException = (HttpException) throwable;
            switch (httpException.code()) {
                case 401:
                    errorMsg = "未授权，请重新登录";
                    break;
                case 403:
                    errorMsg = "无权限访问";
                    break;
                case 404:
                    errorMsg = "请求的资源不存在";
                    break;
                case 500:
                    errorMsg = "服务器内部错误";
                    break;
                default:
                    errorMsg = "服务器响应错误 (" + httpException.code() + ")";
                    break;
            }
        } else {
            errorMsg = "未知错误: " + throwable.getMessage();
        }
        return errorMsg;
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "status=" + status +
                ", code=" + code +
                ", message='" + message + '\'' +
                ", isNetworkError=" + isNetworkError +
                ", isApiError=" + isApiError +
                '}';
    }
}
