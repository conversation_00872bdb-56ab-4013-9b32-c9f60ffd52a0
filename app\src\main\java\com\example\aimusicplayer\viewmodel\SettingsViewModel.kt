package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 设置的ViewModel
 * 负责处理设置的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    application: Application,
    private val settingsRepository: SettingsRepository
) : FlowViewModel(application) {

    companion object {
        private const val TAG = "SettingsViewModel"
    }

    // 自动播放设置的StateFlow
    private val _isAutoPlayEnabledFlow = MutableStateFlow<Boolean>(false)
    val isAutoPlayEnabledFlow: StateFlow<Boolean> = _isAutoPlayEnabledFlow.asStateFlow()
    val isAutoPlayEnabled: LiveData<Boolean> = isAutoPlayEnabledFlow.asLiveData() // 兼容LiveData

    // 夜间模式设置的StateFlow
    private val _isNightModeEnabledFlow = MutableStateFlow<Boolean>(false)
    val isNightModeEnabledFlow: StateFlow<Boolean> = _isNightModeEnabledFlow.asStateFlow()
    val isNightModeEnabled: LiveData<Boolean> = isNightModeEnabledFlow.asLiveData() // 兼容LiveData

    // 驾驶模式自动语音设置的StateFlow
    private val _isAutoVoiceInDrivingEnabledFlow = MutableStateFlow<Boolean>(false)
    val isAutoVoiceInDrivingEnabledFlow: StateFlow<Boolean> = _isAutoVoiceInDrivingEnabledFlow.asStateFlow()
    val isAutoVoiceInDrivingEnabled: LiveData<Boolean> = isAutoVoiceInDrivingEnabledFlow.asLiveData() // 兼容LiveData

    init {
        // 加载设置
        loadSettings()
    }

    /**
     * 加载设置
     */
    private fun loadSettings() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载设置失败", e)
                handleError(e, "加载设置失败: ${e.message}")
            }
        ) {
            try {
                // 从SettingsRepository加载设置
                _isAutoPlayEnabledFlow.value = settingsRepository.isAutoPlayEnabled()
                _isNightModeEnabledFlow.value = settingsRepository.isNightModeEnabled()
                _isAutoVoiceInDrivingEnabledFlow.value = settingsRepository.isAutoVoiceInDrivingEnabled()
            } catch (e: Exception) {
                Log.e(TAG, "加载设置失败", e)
                throw e
            }
        }
    }

    /**
     * 设置自动播放
     * @param enabled 是否启用
     */
    fun setAutoPlayEnabled(enabled: Boolean) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "设置自动播放失败", e)
                handleError(e, "设置自动播放失败: ${e.message}")
            }
        ) {
            try {
                // 保存设置到SettingsRepository
                settingsRepository.setAutoPlayEnabled(enabled)
                _isAutoPlayEnabledFlow.value = enabled
            } catch (e: Exception) {
                Log.e(TAG, "设置自动播放失败", e)
                throw e
            }
        }
    }

    /**
     * 设置夜间模式
     * @param enabled 是否启用
     */
    fun setNightModeEnabled(enabled: Boolean) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "设置夜间模式失败", e)
                handleError(e, "设置夜间模式失败: ${e.message}")
            }
        ) {
            try {
                // 保存设置到SettingsRepository
                settingsRepository.setNightModeEnabled(enabled)
                _isNightModeEnabledFlow.value = enabled

                // 应用夜间模式
                val nightMode = if (enabled) {
                    AppCompatDelegate.MODE_NIGHT_YES
                } else {
                    AppCompatDelegate.MODE_NIGHT_NO
                }
                AppCompatDelegate.setDefaultNightMode(nightMode)
            } catch (e: Exception) {
                Log.e(TAG, "设置夜间模式失败", e)
                throw e
            }
        }
    }

    /**
     * 设置驾驶模式自动语音
     * @param enabled 是否启用
     */
    fun setAutoVoiceInDrivingEnabled(enabled: Boolean) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "设置驾驶模式自动语音失败", e)
                handleError(e, "设置驾驶模式自动语音失败: ${e.message}")
            }
        ) {
            try {
                // 保存设置到SettingsRepository
                settingsRepository.setAutoVoiceInDrivingEnabled(enabled)
                _isAutoVoiceInDrivingEnabledFlow.value = enabled
            } catch (e: Exception) {
                Log.e(TAG, "设置驾驶模式自动语音失败", e)
                throw e
            }
        }
    }

    /**
     * 重置所有设置
     */
    fun resetAllSettings() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "重置所有设置失败", e)
                handleError(e, "重置所有设置失败: ${e.message}")
            }
        ) {
            try {
                // 重置所有设置
                settingsRepository.resetAllSettings()

                // 重新加载设置
                loadSettings()

                // 应用夜间模式
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            } catch (e: Exception) {
                Log.e(TAG, "重置所有设置失败", e)
                throw e
            }
        }
    }
}
