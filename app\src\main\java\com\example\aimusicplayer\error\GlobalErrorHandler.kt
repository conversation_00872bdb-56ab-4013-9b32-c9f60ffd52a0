package com.example.aimusicplayer.error

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import com.example.aimusicplayer.R
import com.google.android.material.snackbar.Snackbar
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 全局错误处理器
 * 用于统一处理应用中的错误
 */
@Singleton
class GlobalErrorHandler @Inject constructor(
    private val context: Context
) {
    companion object {
        private const val TAG = "GlobalErrorHandler"
        
        // 错误类型
        const val ERROR_TYPE_NETWORK = "NETWORK"
        const val ERROR_TYPE_API = "API"
        const val ERROR_TYPE_TIMEOUT = "TIMEOUT"
        const val ERROR_TYPE_AUTH = "AUTH"
        const val ERROR_TYPE_SERVER = "SERVER"
        const val ERROR_TYPE_CLIENT = "CLIENT"
        const val ERROR_TYPE_UNKNOWN = "UNKNOWN"
        
        // 错误码
        const val ERROR_CODE_NO_NETWORK = 1001
        const val ERROR_CODE_TIMEOUT = 1002
        const val ERROR_CODE_SERVER_ERROR = 1003
        const val ERROR_CODE_CLIENT_ERROR = 1004
        const val ERROR_CODE_UNKNOWN = 1005
        const val ERROR_CODE_AUTH_FAILED = 1006
    }
    
    // 错误消息流
    val errorFlow = MutableSharedFlow<ErrorInfo>()
    
    // 错误消息LiveData（兼容旧版本）
    val errorLiveData = MutableLiveData<String>()
    
    // 网络状态流
    val networkStatusFlow = MutableStateFlow(isNetworkAvailable())
    
    /**
     * 处理错误
     * @param throwable 错误
     * @param showToast 是否显示Toast
     * @return 错误信息
     */
    suspend fun handleError(throwable: Throwable, showToast: Boolean = true): ErrorInfo {
        Log.e(TAG, "处理错误", throwable)
        
        val errorInfo = when (throwable) {
            is IOException -> {
                when (throwable) {
                    is SocketTimeoutException -> {
                        ErrorInfo(
                            message = context.getString(R.string.error_timeout),
                            type = ERROR_TYPE_TIMEOUT,
                            code = ERROR_CODE_TIMEOUT,
                            throwable = throwable
                        )
                    }
                    is UnknownHostException -> {
                        ErrorInfo(
                            message = context.getString(R.string.error_no_network),
                            type = ERROR_TYPE_NETWORK,
                            code = ERROR_CODE_NO_NETWORK,
                            throwable = throwable
                        )
                    }
                    else -> {
                        ErrorInfo(
                            message = context.getString(R.string.error_network),
                            type = ERROR_TYPE_NETWORK,
                            code = ERROR_CODE_NO_NETWORK,
                            throwable = throwable
                        )
                    }
                }
            }
            is HttpException -> {
                when (throwable.code()) {
                    in 400..499 -> {
                        if (throwable.code() == 401 || throwable.code() == 403) {
                            ErrorInfo(
                                message = context.getString(R.string.error_auth),
                                type = ERROR_TYPE_AUTH,
                                code = ERROR_CODE_AUTH_FAILED,
                                throwable = throwable
                            )
                        } else {
                            ErrorInfo(
                                message = context.getString(R.string.error_client, throwable.code()),
                                type = ERROR_TYPE_CLIENT,
                                code = ERROR_CODE_CLIENT_ERROR,
                                throwable = throwable
                            )
                        }
                    }
                    in 500..599 -> {
                        ErrorInfo(
                            message = context.getString(R.string.error_server, throwable.code()),
                            type = ERROR_TYPE_SERVER,
                            code = ERROR_CODE_SERVER_ERROR,
                            throwable = throwable
                        )
                    }
                    else -> {
                        ErrorInfo(
                            message = context.getString(R.string.error_unknown),
                            type = ERROR_TYPE_UNKNOWN,
                            code = ERROR_CODE_UNKNOWN,
                            throwable = throwable
                        )
                    }
                }
            }
            else -> {
                ErrorInfo(
                    message = throwable.message ?: context.getString(R.string.error_unknown),
                    type = ERROR_TYPE_UNKNOWN,
                    code = ERROR_CODE_UNKNOWN,
                    throwable = throwable
                )
            }
        }
        
        // 发送错误消息
        errorFlow.emit(errorInfo)
        
        // 兼容旧版本
        errorLiveData.postValue(errorInfo.message)
        
        // 显示Toast
        if (showToast) {
            Toast.makeText(context, errorInfo.message, Toast.LENGTH_SHORT).show()
        }
        
        return errorInfo
    }
    
    /**
     * 检查网络是否可用
     */
    fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo != null && networkInfo.isConnected
        }
    }
    
    /**
     * 显示Snackbar
     * @param view 视图
     * @param message 消息
     * @param duration 显示时长
     */
    fun showSnackbar(view: android.view.View, message: String, duration: Int = Snackbar.LENGTH_SHORT) {
        Snackbar.make(view, message, duration).show()
    }
}
