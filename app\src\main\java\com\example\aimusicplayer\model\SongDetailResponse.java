package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SongDetailResponse {
    
    private int code;
    private List<Song> songs;
    private List<Privilege> privileges;
    
    public int getCode() {
        return code;
    }
    
    public List<Song> getSongs() {
        return songs;
    }
    
    public List<Privilege> getPrivileges() {
        return privileges;
    }
    
    public static class Song {
        private long id;
        private String name;
        private List<Artist> ar;
        private Album al;
        private long dt; // 歌曲时长，单位毫秒
        private int fee; // 收费类型，0=免费，1=VIP可听，4=购买专辑可听
        private int vd; // 音频质量
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public List<Artist> getArtists() {
            return ar;
        }
        
        public Album getAlbum() {
            return al;
        }
        
        // 兼容性方法 - 获取歌手列表
        public List<Artist> getAr() {
            return ar;
        }
        
        // 兼容性方法 - 获取专辑信息
        public Album getAl() {
            return al;
        }
        
        public long getDuration() {
            return dt;
        }
        
        public int getFee() {
            return fee;
        }
        
        public boolean isVipSong() {
            return fee == 1 || fee == 4;
        }
    }
    
    public static class Artist {
        private long id;
        private String name;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
    }
    
    public static class Album {
        private long id;
        private String name;
        @SerializedName("picUrl")
        private String picUrl;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public String getPicUrl() {
            return picUrl;
        }
    }
    
    public static class Privilege {
        private long id; // 歌曲ID
        private int fee; // 收费类型
        private int payed; // 是否已购买
        private int st; // 是否可播放，-200=不可播放
        private int pl; // 音质等级
        private int dl; // 是否可下载
        
        public long getId() {
            return id;
        }
        
        public int getFee() {
            return fee;
        }
        
        public boolean isPlayable() {
            return st >= 0;
        }
        
        public boolean isVip() {
            return fee == 1 || fee == 4;
        }
    }
} 