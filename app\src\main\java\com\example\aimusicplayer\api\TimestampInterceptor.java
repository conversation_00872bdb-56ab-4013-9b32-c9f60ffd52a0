package com.example.aimusicplayer.api;

import java.io.IOException;

import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 时间戳拦截器
 * 为特定请求添加时间戳参数，防止缓存
 * 根据api.txt文档说明：某些请求需要添加时间戳，使每次请求url不一样，不然请求会被缓存
 */
public class TimestampInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originalRequest = chain.request();

        // 获取原始URL
        HttpUrl originalUrl = originalRequest.url();

        // 检查是否需要添加时间戳的请求
        if (shouldAddTimestamp(originalUrl.toString())) {
            // 添加时间戳参数
            HttpUrl newUrl = originalUrl.newBuilder()
                    .addQueryParameter("timestamp", String.valueOf(System.currentTimeMillis()))
                    .build();

            // 创建新的请求
            Request newRequest = originalRequest.newBuilder()
                    .url(newUrl)
                    .build();

            return chain.proceed(newRequest);
        }

        // 对于其他请求，直接处理
        return chain.proceed(originalRequest);
    }

    /**
     * 判断是否需要添加时间戳
     * 某些特定的API需要添加时间戳，防止缓存
     * 根据api.txt文档说明：301错误可能是由于缓存导致的，添加时间戳可以解决
     * @param url 请求URL
     * @return 是否需要添加时间戳
     */
    private boolean shouldAddTimestamp(String url) {
        // 登录相关API
        if (url.contains("login/") ||
            url.contains("logout") ||
            url.contains("register/")) {
            return true;
        }

        // 二维码相关API - 特别确保这些API有时间戳
        if (url.contains("login/qr/key") ||
            url.contains("login/qr/create") ||
            url.contains("login/qr/check")) {
            return true;
        }

        // 用户相关API
        if (url.contains("user/") ||
            url.contains("likelist") ||
            url.contains("like")) {
            return true;
        }

        // 歌曲相关API
        if (url.contains("song/") ||
            url.contains("playlist/") ||
            url.contains("album/") ||
            url.contains("artist/")) {
            return true;
        }

        // 搜索相关API
        if (url.contains("search") && !url.contains("search/hot/detail")) {
            return true;
        }

        // 推荐相关API
        if (url.contains("recommend/") ||
            url.contains("personalized/") ||
            url.contains("top/")) {
            return true;
        }

        // 评论相关API
        if (url.contains("comment/")) {
            return true;
        }

        // 电台相关API
        if (url.contains("dj/")) {
            return true;
        }

        // 视频相关API
        if (url.contains("video/") ||
            url.contains("mv/")) {
            return true;
        }

        // 其他可能需要时间戳的API
        if (url.contains("daily_signin") ||
            url.contains("captcha/") ||
            url.contains("banner")) {
            return true;
        }

        // 默认不添加时间戳
        return false;
    }
}
