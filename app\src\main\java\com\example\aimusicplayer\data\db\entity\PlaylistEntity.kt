package com.example.aimusicplayer.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 歌单实体类
 * 用于数据库存储歌单信息
 */
@Entity(tableName = "playlists")
data class PlaylistEntity(
    @PrimaryKey
    @ColumnInfo("playlist_id")
    val playlistId: Long,

    // 歌单名称
    @ColumnInfo("name")
    val name: String,

    // 歌单封面
    @ColumnInfo("cover_url")
    val coverUrl: String,

    // 歌单描述
    @ColumnInfo("description")
    val description: String = "",

    // 创建者ID
    @ColumnInfo("creator_id")
    val creatorId: Long = 0,

    // 创建者名称
    @ColumnInfo("creator_name")
    val creatorName: String = "",

    // 歌曲数量
    @ColumnInfo("song_count")
    val songCount: Int = 0,

    // 播放次数
    @ColumnInfo("play_count")
    val playCount: Int = 0,

    // 是否已收藏
    @ColumnInfo("is_subscribed")
    val isSubscribed: Boolean = false,

    // 创建时间
    @ColumnInfo("create_time")
    val createTime: Long = System.currentTimeMillis(),

    // 更新时间
    @ColumnInfo("update_time")
    val updateTime: Long = System.currentTimeMillis(),

    // 是否为本地歌单
    @ColumnInfo("is_local")
    val isLocal: Boolean = false
)
