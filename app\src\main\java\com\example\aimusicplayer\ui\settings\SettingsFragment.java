package com.example.aimusicplayer.ui.settings;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.viewmodel.SettingsViewModel;

/**
 * 设置视图，提供应用设置选项
 * 使用MVVM架构
 */
public class SettingsFragment extends Fragment {
    
    private static final String TAG = "SettingsFragment";
    
    // ViewModel
    private SettingsViewModel viewModel;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 暂时返回一个简单的布局
        View view = inflater.inflate(R.layout.fragment_placeholder, container, false);
        TextView textView = view.findViewById(R.id.text_placeholder);
        textView.setText("设置 - 功能开发中");
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化ViewModel
        viewModel = new ViewModelProvider(requireActivity()).get(SettingsViewModel.class);
        
        // 设置观察者
        setupObservers();
    }
    
    /**
     * 设置观察者
     */
    private void setupObservers() {
        // TODO: 观察ViewModel中的LiveData
    }
}
