package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 用户歌单响应
 * 对应API: /user/playlist
 */
public class UserPlaylistResponse {
    private int code;
    
    @SerializedName("playlist")
    private List<Playlist> playlists;
    
    @SerializedName("more")
    private boolean hasMore;
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public List<Playlist> getPlaylists() {
        return playlists;
    }
    
    public void setPlaylists(List<Playlist> playlists) {
        this.playlists = playlists;
    }
    
    public boolean isHasMore() {
        return hasMore;
    }
    
    public void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }
    
    /**
     * 歌单信息
     */
    public static class Playlist {
        @SerializedName("id")
        private String id;
        
        @SerializedName("name")
        private String name;
        
        @SerializedName("coverImgUrl")
        private String coverImgUrl;
        
        @SerializedName("trackCount")
        private int trackCount;
        
        @SerializedName("playCount")
        private long playCount;
        
        @SerializedName("description")
        private String description;
        
        @SerializedName("creator")
        private Creator creator;
        
        @SerializedName("subscribed")
        private boolean subscribed;
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getCoverImgUrl() {
            return coverImgUrl;
        }
        
        public void setCoverImgUrl(String coverImgUrl) {
            this.coverImgUrl = coverImgUrl;
        }
        
        public int getTrackCount() {
            return trackCount;
        }
        
        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }
        
        public long getPlayCount() {
            return playCount;
        }
        
        public void setPlayCount(long playCount) {
            this.playCount = playCount;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public Creator getCreator() {
            return creator;
        }
        
        public void setCreator(Creator creator) {
            this.creator = creator;
        }
        
        public boolean isSubscribed() {
            return subscribed;
        }
        
        public void setSubscribed(boolean subscribed) {
            this.subscribed = subscribed;
        }
    }
    
    /**
     * 创建者信息
     */
    public static class Creator {
        @SerializedName("userId")
        private String userId;
        
        @SerializedName("nickname")
        private String nickname;
        
        @SerializedName("avatarUrl")
        private String avatarUrl;
        
        public String getUserId() {
            return userId;
        }
        
        public void setUserId(String userId) {
            this.userId = userId;
        }
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
    }
}
