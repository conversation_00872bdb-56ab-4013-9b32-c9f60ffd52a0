package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

/**
 * 歌曲可用性检查响应
 */
public class MusicAvailabilityResponse {
    
    @SerializedName("success")
    private boolean success;
    
    @SerializedName("message")
    private String message;
    
    @SerializedName("code")
    private int code;
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    @Override
    public String toString() {
        return "MusicAvailabilityResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", code=" + code +
                '}';
    }
}
