package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 用户信息统计响应
 * 对应/user/subcount接口返回的数据
 */
data class UserSubCountResponse(
    val code: Int,
    val message: String?,
    @SerializedName("programCount")
    val programCount: Int,         // 节目数
    @SerializedName("djRadioCount")
    val djRadioCount: Int,         // 电台数
    @SerializedName("mvCount")
    val mvCount: Int,              // MV数
    @SerializedName("artistCount")
    val artistCount: Int,          // 收藏的歌手数
    @SerializedName("newProgramCount")
    val newProgramCount: Int,      // 新节目数
    @SerializedName("createDjRadioCount")
    val createDjRadioCount: Int,   // 创建的电台数
    @SerializedName("createdPlaylistCount")
    val createdPlaylistCount: Int, // 创建的歌单数
    @SerializedName("subPlaylistCount")
    val subPlaylistCount: Int,     // 收藏的歌单数
    @SerializedName("likedSongCount")
    val likedSongCount: Int        // 喜欢的歌曲数
)
