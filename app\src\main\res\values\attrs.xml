<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 自定义主题属性 -->
    <attr name="colorBackground" format="color|reference" />
    <attr name="textColorPrimary" format="color|reference" />
    <attr name="textColorSecondary" format="color|reference" />

    <!-- LottieLoadingView自定义属性 -->
    <declare-styleable name="LottieLoadingView">
        <!-- 动画资源 -->
        <attr name="lottieAnimationAsset" format="string" />
        <!-- 加载消息 -->
        <attr name="loadingMessage" format="string" />
        <!-- 自动播放 -->
        <attr name="autoPlay" format="boolean" />
        <!-- 循环播放 -->
        <attr name="loop" format="boolean" />
    </declare-styleable>
</resources>
