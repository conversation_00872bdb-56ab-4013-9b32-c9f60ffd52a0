package com.example.aimusicplayer.utils

import androidx.annotation.NonNull
import androidx.recyclerview.widget.DiffUtil

/**
 * 通用的差异比较回调基类
 * 用于优化RecyclerView列表的更新，减少不必要的重绘
 * @param oldItems 旧的列表项
 * @param newItems 新的列表项
 */
abstract class BaseDiffCallback<T>(
    protected val oldItems: List<T>?,
    protected val newItems: List<T>?
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int {
        return oldItems?.size ?: 0
    }

    override fun getNewListSize(): Int {
        return newItems?.size ?: 0
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 检查列表是否为空
        if (oldItems == null || newItems == null) {
            return false
        }
        
        // 获取列表项
        val oldItem = oldItems[oldItemPosition]
        val newItem = newItems[newItemPosition]
        
        // 检查列表项是否为空
        if (oldItem == null || newItem == null) {
            return false
        }
        
        // 调用子类实现的方法比较列表项ID
        return areItemsTheSameImpl(oldItem, newItem)
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 检查列表是否为空
        if (oldItems == null || newItems == null) {
            return false
        }
        
        // 获取列表项
        val oldItem = oldItems[oldItemPosition]
        val newItem = newItems[newItemPosition]
        
        // 检查列表项是否为空
        if (oldItem == null || newItem == null) {
            return false
        }
        
        // 调用子类实现的方法比较列表项内容
        return areContentsTheSameImpl(oldItem, newItem)
    }

    /**
     * 比较两个列表项的ID是否相同
     * 子类必须实现此方法
     * @param oldItem 旧的列表项
     * @param newItem 新的列表项
     * @return 如果两个列表项的ID相同，则返回true，否则返回false
     */
    protected abstract fun areItemsTheSameImpl(@NonNull oldItem: T, @NonNull newItem: T): Boolean

    /**
     * 比较两个列表项的内容是否相同
     * 子类必须实现此方法
     * @param oldItem 旧的列表项
     * @param newItem 新的列表项
     * @return 如果两个列表项的内容相同，则返回true，否则返回false
     */
    protected abstract fun areContentsTheSameImpl(@NonNull oldItem: T, @NonNull newItem: T): Boolean
}
