package com.example.aimusicplayer.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.SongModel
import com.example.aimusicplayer.databinding.ItemSongBinding

/**
 * 歌曲适配器
 * 用于显示歌曲列表
 */
class SongAdapter(
    private val onItemClick: (SongModel) -> Unit
) : ListAdapter<SongModel, SongAdapter.SongViewHolder>(SongDiffCallback()) {

    // 最后一个动画位置，用于控制列表项动画
    private var lastAnimatedPosition = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongViewHolder {
        val binding = ItemSongBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SongViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SongViewHolder, position: Int) {
        val song = getItem(position)
        holder.bind(song)

        // 应用列表项动画
        if (position > lastAnimatedPosition) {
            val animation = AnimationUtils.loadAnimation(
                holder.itemView.context,
                R.anim.item_animation_from_bottom
            )
            animation.startOffset = (position * 50L)
            holder.itemView.startAnimation(animation)
            lastAnimatedPosition = position
        }
    }

    inner class SongViewHolder(
        private val binding: ItemSongBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(song: SongModel) {
            binding.textSongTitle.text = song.title
            binding.textSongArtist.text = song.artist
            binding.textSongDuration.text = formatDuration(song.duration)

            // 加载专辑封面
            Glide.with(binding.imageSongCover.context)
                .load(song.albumCover)
                .apply(RequestOptions()
                    .placeholder(R.drawable.default_album_art)
                    .error(R.drawable.default_album_art)
                    .transform(RoundedCorners(8))
                )
                .into(binding.imageSongCover)

            // 设置VIP标签可见性
            binding.textVipTag.visibility = if (song.isVip) View.VISIBLE else View.GONE
        }

        /**
         * 格式化时长
         * @param duration 时长（毫秒）
         * @return 格式化后的时长字符串（mm:ss）
         */
        private fun formatDuration(duration: Long): String {
            val totalSeconds = duration / 1000
            val minutes = totalSeconds / 60
            val seconds = totalSeconds % 60
            return String.format("%02d:%02d", minutes, seconds)
        }
    }

    /**
     * 歌曲差异比较回调
     */
    class SongDiffCallback : DiffUtil.ItemCallback<SongModel>() {
        override fun areItemsTheSame(oldItem: SongModel, newItem: SongModel): Boolean {
            return oldItem.songId == newItem.songId
        }

        override fun areContentsTheSame(oldItem: SongModel, newItem: SongModel): Boolean {
            return oldItem == newItem
        }
    }
}
