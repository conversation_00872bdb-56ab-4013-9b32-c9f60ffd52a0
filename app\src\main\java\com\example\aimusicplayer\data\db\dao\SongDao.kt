package com.example.aimusicplayer.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.aimusicplayer.data.db.entity.SongEntity
import kotlinx.coroutines.flow.Flow

/**
 * 歌曲DAO接口
 */
@Dao
interface SongDao {
    /**
     * 插入单首歌曲
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(song: SongEntity)
    
    /**
     * 批量插入歌曲
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(songs: List<SongEntity>)
    
    /**
     * 更新歌曲
     */
    @Update
    suspend fun update(song: SongEntity)
    
    /**
     * 删除歌曲
     */
    @Delete
    suspend fun delete(song: SongEntity)
    
    /**
     * 根据唯一ID查询歌曲
     */
    @Query("SELECT * FROM songs WHERE unique_id = :uniqueId")
    suspend fun getSongByUniqueId(uniqueId: String): SongEntity?
    
    /**
     * 根据歌曲ID查询歌曲
     */
    @Query("SELECT * FROM songs WHERE song_id = :songId AND type = :type")
    suspend fun getSongById(songId: Long, type: Int): SongEntity?
    
    /**
     * 查询所有歌曲
     */
    @Query("SELECT * FROM songs")
    fun getAllSongs(): Flow<List<SongEntity>>
    
    /**
     * 查询所有本地歌曲
     */
    @Query("SELECT * FROM songs WHERE type = 0")
    fun getAllLocalSongs(): Flow<List<SongEntity>>
    
    /**
     * 查询所有在线歌曲
     */
    @Query("SELECT * FROM songs WHERE type = 1")
    fun getAllOnlineSongs(): Flow<List<SongEntity>>
    
    /**
     * 查询所有收藏歌曲
     */
    @Query("SELECT * FROM songs WHERE is_favorite = 1")
    fun getAllFavoriteSongs(): Flow<List<SongEntity>>
    
    /**
     * 根据标题或艺术家搜索歌曲
     */
    @Query("SELECT * FROM songs WHERE title LIKE '%' || :keyword || '%' OR artist LIKE '%' || :keyword || '%'")
    fun searchSongs(keyword: String): Flow<List<SongEntity>>
    
    /**
     * 清空所有歌曲
     */
    @Query("DELETE FROM songs")
    suspend fun deleteAll()
}
