package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.renderscript.Allocation
import android.renderscript.Element
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import android.util.LruCache
import androidx.core.graphics.drawable.toBitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 专辑封面模糊效果缓存
 * 用于缓存专辑封面的模糊效果，避免重复计算
 */
@Singleton
class AlbumArtBlurCache @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "AlbumArtBlurCache"
        private const val CACHE_SIZE = 20 // 缓存大小
        private const val BLUR_RADIUS = 25f // 模糊半径
    }
    
    // 内存缓存
    private val memoryCache = LruCache<String, Bitmap>(CACHE_SIZE)
    
    // RenderScript实例
    private val renderScript by lazy { RenderScript.create(context) }
    
    /**
     * 获取模糊效果的专辑封面
     * @param url 专辑封面URL
     * @param callback 回调函数
     */
    fun getBlurredAlbumArt(url: String?, callback: (Bitmap?) -> Unit) {
        if (url.isNullOrEmpty()) {
            callback(null)
            return
        }
        
        // 尝试从缓存中获取
        val cachedBitmap = memoryCache.get(url)
        if (cachedBitmap != null) {
            callback(cachedBitmap)
            return
        }
        
        // 加载原始图片
        Glide.with(context)
            .asBitmap()
            .load(url)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 在后台线程中进行模糊处理
                    Thread {
                        val blurredBitmap = applyBlur(resource)
                        // 缓存结果
                        memoryCache.put(url, blurredBitmap)
                        // 回调结果
                        callback(blurredBitmap)
                    }.start()
                }
                
                override fun onLoadCleared(placeholder: Drawable?) {
                    // 不需要处理
                }
                
                override fun onLoadFailed(errorDrawable: Drawable?) {
                    callback(null)
                }
            })
    }
    
    /**
     * 获取模糊效果的专辑封面（协程版本）
     * @param url 专辑封面URL
     * @return 模糊效果的专辑封面
     */
    suspend fun getBlurredAlbumArtSuspend(url: String?): Bitmap? = withContext(Dispatchers.IO) {
        if (url.isNullOrEmpty()) {
            return@withContext null
        }
        
        // 尝试从缓存中获取
        val cachedBitmap = memoryCache.get(url)
        if (cachedBitmap != null) {
            return@withContext cachedBitmap
        }
        
        try {
            // 加载原始图片
            val originalBitmap = Glide.with(context)
                .asBitmap()
                .load(url)
                .submit()
                .get()
            
            // 应用模糊效果
            val blurredBitmap = applyBlur(originalBitmap)
            
            // 缓存结果
            memoryCache.put(url, blurredBitmap)
            
            return@withContext blurredBitmap
        } catch (e: Exception) {
            e.printStackTrace()
            return@withContext null
        }
    }
    
    /**
     * 应用模糊效果
     * @param original 原始图片
     * @return 模糊效果的图片
     */
    private fun applyBlur(original: Bitmap): Bitmap {
        // 创建输出位图
        val outputBitmap = Bitmap.createBitmap(original.width, original.height, Bitmap.Config.ARGB_8888)
        
        // 创建Allocation
        val inputAllocation = Allocation.createFromBitmap(renderScript, original)
        val outputAllocation = Allocation.createFromBitmap(renderScript, outputBitmap)
        
        // 创建模糊脚本
        val blurScript = ScriptIntrinsicBlur.create(renderScript, Element.U8_4(renderScript))
        blurScript.setRadius(BLUR_RADIUS)
        blurScript.setInput(inputAllocation)
        blurScript.forEach(outputAllocation)
        
        // 复制结果到输出位图
        outputAllocation.copyTo(outputBitmap)
        
        // 释放资源
        inputAllocation.destroy()
        outputAllocation.destroy()
        blurScript.destroy()
        
        return outputBitmap
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        memoryCache.evictAll()
    }
    
    /**
     * 释放资源
     */
    fun release() {
        clearCache()
        renderScript.destroy()
    }
}
