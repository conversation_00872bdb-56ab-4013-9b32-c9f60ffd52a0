<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="16dp">

    <!-- Lottie动画视图 -->
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottie_animation_view"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_fileName="music_loading.json" />

    <!-- 加载消息 -->
    <TextView
        android:id="@+id/loading_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="加载中..."
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:shadowColor="#80000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2" />

</LinearLayout>
