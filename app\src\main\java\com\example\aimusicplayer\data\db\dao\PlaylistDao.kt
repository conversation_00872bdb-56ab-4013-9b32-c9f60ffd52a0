package com.example.aimusicplayer.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.example.aimusicplayer.data.db.entity.PlaylistEntity
import com.example.aimusicplayer.data.db.entity.PlaylistSongCrossRef
import com.example.aimusicplayer.data.db.entity.SongEntity
import kotlinx.coroutines.flow.Flow

/**
 * 歌单DAO接口
 */
@Dao
interface PlaylistDao {
    /**
     * 插入歌单
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(playlist: PlaylistEntity)
    
    /**
     * 批量插入歌单
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(playlists: List<PlaylistEntity>)
    
    /**
     * 更新歌单
     */
    @Update
    suspend fun update(playlist: PlaylistEntity)
    
    /**
     * 删除歌单
     */
    @Delete
    suspend fun delete(playlist: PlaylistEntity)
    
    /**
     * 根据ID查询歌单
     */
    @Query("SELECT * FROM playlists WHERE playlist_id = :playlistId")
    suspend fun getPlaylistById(playlistId: Long): PlaylistEntity?
    
    /**
     * 查询所有歌单
     */
    @Query("SELECT * FROM playlists")
    fun getAllPlaylists(): Flow<List<PlaylistEntity>>
    
    /**
     * 查询所有本地歌单
     */
    @Query("SELECT * FROM playlists WHERE is_local = 1")
    fun getAllLocalPlaylists(): Flow<List<PlaylistEntity>>
    
    /**
     * 查询所有在线歌单
     */
    @Query("SELECT * FROM playlists WHERE is_local = 0")
    fun getAllOnlinePlaylists(): Flow<List<PlaylistEntity>>
    
    /**
     * 查询所有收藏歌单
     */
    @Query("SELECT * FROM playlists WHERE is_subscribed = 1")
    fun getAllSubscribedPlaylists(): Flow<List<PlaylistEntity>>
    
    /**
     * 添加歌曲到歌单
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addSongToPlaylist(crossRef: PlaylistSongCrossRef)
    
    /**
     * 批量添加歌曲到歌单
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addSongsToPlaylist(crossRefs: List<PlaylistSongCrossRef>)
    
    /**
     * 从歌单中移除歌曲
     */
    @Query("DELETE FROM playlist_song_cross_ref WHERE playlist_id = :playlistId AND song_unique_id = :songUniqueId")
    suspend fun removeSongFromPlaylist(playlistId: Long, songUniqueId: String)
    
    /**
     * 获取歌单中的所有歌曲
     */
    @Transaction
    @Query("SELECT s.* FROM songs s INNER JOIN playlist_song_cross_ref ref ON s.unique_id = ref.song_unique_id WHERE ref.playlist_id = :playlistId ORDER BY ref.sort_order")
    fun getPlaylistSongs(playlistId: Long): Flow<List<SongEntity>>
    
    /**
     * 清空歌单中的所有歌曲
     */
    @Query("DELETE FROM playlist_song_cross_ref WHERE playlist_id = :playlistId")
    suspend fun clearPlaylist(playlistId: Long)
    
    /**
     * 更新歌单中歌曲的排序
     */
    @Update
    suspend fun updateSongOrder(crossRef: PlaylistSongCrossRef)
    
    /**
     * 批量更新歌单中歌曲的排序
     */
    @Update
    suspend fun updateSongOrders(crossRefs: List<PlaylistSongCrossRef>)
    
    /**
     * 清空所有歌单
     */
    @Query("DELETE FROM playlists")
    suspend fun deleteAll()
}
