package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

public class PlaylistDetailResponse {
    private int code;
    private PlaylistDetail playlist;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public PlaylistDetail getPlaylist() {
        return playlist;
    }

    public void setPlaylist(PlaylistDetail playlist) {
        this.playlist = playlist;
    }

    public static class PlaylistDetail {
        private long id;
        private String name;
        private String description;
        @SerializedName("coverImgUrl")
        private String coverImgUrl;
        private List<Track> tracks;
        private List<Privilege> privileges;
        private Creator creator;
        @SerializedName("trackCount")
        private int trackCount;
        @SerializedName("playCount")
        private int playCount;
        @SerializedName("subscribed")
        private boolean subscribed;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getCoverImgUrl() {
            return coverImgUrl;
        }

        public void setCoverImgUrl(String coverImgUrl) {
            this.coverImgUrl = coverImgUrl;
        }

        public List<Track> getTracks() {
            return tracks;
        }

        public void setTracks(List<Track> tracks) {
            this.tracks = tracks;
        }

        public List<Privilege> getPrivileges() {
            return privileges;
        }

        public void setPrivileges(List<Privilege> privileges) {
            this.privileges = privileges;
        }

        public Creator getCreator() {
            return creator;
        }

        public void setCreator(Creator creator) {
            this.creator = creator;
        }

        public int getTrackCount() {
            return trackCount;
        }

        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }

        public int getPlayCount() {
            return playCount;
        }

        public void setPlayCount(int playCount) {
            this.playCount = playCount;
        }

        public boolean isSubscribed() {
            return subscribed;
        }

        public void setSubscribed(boolean subscribed) {
            this.subscribed = subscribed;
        }
    }

    public static class Creator {
        @SerializedName("userId")
        private long userId;
        @SerializedName("nickname")
        private String nickname;

        public long getUserId() {
            return userId;
        }

        public void setUserId(long userId) {
            this.userId = userId;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
    }

    public static class Track {
        private long id;
        private String name;
        private long duration;
        private Album album;
        private List<Artist> artists;
        @SerializedName("ar")
        private List<Artist> ar;
        @SerializedName("al")
        private Album al;
        @SerializedName("dt")
        private long dt;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }

        public Album getAlbum() {
            return album;
        }

        public void setAlbum(Album album) {
            this.album = album;
        }

        public List<Artist> getArtists() {
            return artists;
        }

        public void setArtists(List<Artist> artists) {
            this.artists = artists;
        }

        // 新增方法，用于兼容API响应中的ar字段
        public List<Artist> getAr() {
            return ar != null ? ar : artists;
        }

        public void setAr(List<Artist> ar) {
            this.ar = ar;
        }

        // 新增方法，用于兼容API响应中的al字段
        public Album getAl() {
            return al != null ? al : album;
        }

        public void setAl(Album al) {
            this.al = al;
        }

        // 新增方法，用于兼容API响应中的dt字段
        public long getDt() {
            return dt > 0 ? dt : duration;
        }

        public void setDt(long dt) {
            this.dt = dt;
        }
    }

    public static class Album {
        private long id;
        private String name;
        @SerializedName("picUrl")
        private String picUrl;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }
    }

    public static class Artist {
        private long id;
        private String name;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Privilege {
        private long id;
        private int fee; // 收费类型，0=免费，1=VIP可听，4=购买专辑可听
        private int payed; // 是否已付费
        private int st; // 播放状态，-200=不可播放
        private int pl; // 音质
        private int dl; // 下载状态
        private int subp; // 是否有版权

        public long getId() {
            return id;
        }

        public int getFee() {
            return fee;
        }

        public int getSt() {
            return st;
        }

        public boolean isPlayable() {
            return st >= 0;
        }

        public boolean isVip() {
            return fee == 1 || fee == 4;
        }
    }
}