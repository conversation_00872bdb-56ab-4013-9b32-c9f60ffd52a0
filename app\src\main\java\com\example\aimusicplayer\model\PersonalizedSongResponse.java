package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

public class PersonalizedSongResponse {
    private int code;
    private List<SongResult> result;
    
    public int getCode() {
        return code;
    }
    
    public List<SongResult> getResult() {
        return result;
    }
    
    public static class SongResult {
        private long id;
        private String name;
        private long type;
        @SerializedName("picUrl")
        private String picUrl;
        @SerializedName("canDislike")
        private boolean canDislike;
        @SerializedName("trackNumberUpdateTime")
        private long trackNumberUpdateTime;
        @SerializedName("song")
        private SongDetail songDetail;
        private String alg;
        
        // 兼容性方法 - 获取歌手列表
        public List<Artist> getAr() {
            return songDetail != null ? songDetail.getArtists() : null;
        }
        
        // 兼容性方法 - 获取专辑信息
        public Album getAl() {
            return songDetail != null ? songDetail.getAlbum() : null;
        }
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public String getPicUrl() {
            return picUrl;
        }
        
        public SongDetail getSongDetail() {
            return songDetail;
        }
    }
    
    public static class SongDetail {
        private long id;
        private String name;
        private List<Artist> artists;
        private Album album;
        private long duration;
        @SerializedName("copyrightId")
        private long copyrightId;
        private long status;
        @SerializedName("mvid")
        private long mvId;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public List<Artist> getArtists() {
            return artists;
        }
        
        public Album getAlbum() {
            return album;
        }
        
        public long getDuration() {
            return duration;
        }
    }
    
    public static class Artist {
        private long id;
        private String name;
        @SerializedName("tns")
        private List<String> translations;
        @SerializedName("alias")
        private List<String> aliases;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
    }
    
    public static class Album {
        private long id;
        private String name;
        @SerializedName("picUrl")
        private String picUrl;
        @SerializedName("tns")
        private List<String> translations;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public String getPicUrl() {
            return picUrl;
        }
    }
} 