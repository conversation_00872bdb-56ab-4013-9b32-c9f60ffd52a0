package com.example.aimusicplayer.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.aimusicplayer.data.db.entity.UserEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户DAO接口
 */
@Dao
interface UserDao {
    /**
     * 插入用户
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(user: UserEntity)
    
    /**
     * 更新用户
     */
    @Update
    suspend fun update(user: UserEntity)
    
    /**
     * 删除用户
     */
    @Delete
    suspend fun delete(user: UserEntity)
    
    /**
     * 根据ID查询用户
     */
    @Query("SELECT * FROM users WHERE user_id = :userId")
    suspend fun getUserById(userId: Long): UserEntity?
    
    /**
     * 获取当前登录用户
     */
    @Query("SELECT * FROM users ORDER BY last_login_time DESC LIMIT 1")
    fun getCurrentUser(): Flow<UserEntity?>
    
    /**
     * 获取所有用户
     */
    @Query("SELECT * FROM users")
    fun getAllUsers(): Flow<List<UserEntity>>
    
    /**
     * 清空所有用户
     */
    @Query("DELETE FROM users")
    suspend fun deleteAll()
    
    /**
     * 更新用户Cookie
     */
    @Query("UPDATE users SET cookie = :cookie, last_login_time = :lastLoginTime WHERE user_id = :userId")
    suspend fun updateUserCookie(userId: Long, cookie: String, lastLoginTime: Long = System.currentTimeMillis())
}
