<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- 设置圆角 -->
    <corners android:radius="16dp" />
    
    <!-- 设置渐变背景 -->
    <gradient
        android:startColor="@color/sakura_background"
        android:endColor="#FFFFFF"
        android:angle="225" />
    
    <!-- 设置边框 -->
    <stroke
        android:width="1dp"
        android:color="@color/sakura_divider" />
    
    <!-- 设置内边距 -->
    <padding
        android:left="12dp"
        android:top="12dp"
        android:right="12dp"
        android:bottom="12dp" />
</shape> 