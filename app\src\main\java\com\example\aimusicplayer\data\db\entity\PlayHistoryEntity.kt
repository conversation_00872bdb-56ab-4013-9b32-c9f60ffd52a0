package com.example.aimusicplayer.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 播放历史实体类
 * 用于记录用户的播放历史
 */
@Entity(
    tableName = "play_history",
    indices = [
        Index("song_unique_id"),
        Index("play_time")
    ]
)
data class PlayHistoryEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo("id")
    val id: Long = 0,

    @ColumnInfo("song_unique_id")
    val songUniqueId: String,

    // 播放时间
    @ColumnInfo("play_time")
    val playTime: Long = System.currentTimeMillis(),

    // 播放进度（毫秒）
    @ColumnInfo("play_position")
    val playPosition: Long = 0,

    // 播放时长（毫秒）
    @ColumnInfo("play_duration")
    val playDuration: Long = 0,

    // 是否完成播放
    @ColumnInfo("is_completed")
    val isCompleted: Boolean = false,

    // 播放来源
    @ColumnInfo("play_source")
    val playSource: String = "",

    // 用户ID
    @ColumnInfo("user_id")
    val userId: String = ""
)
