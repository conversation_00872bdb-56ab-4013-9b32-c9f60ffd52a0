package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 收藏歌曲列表响应
 */
public class LikeListResponse {
    
    @SerializedName("code")
    private int code;
    
    @SerializedName("ids")
    private List<Long> ids;
    
    @SerializedName("checkPoint")
    private long checkPoint;
    
    public int getCode() {
        return code;
    }
    
    public List<Long> getIds() {
        return ids;
    }
    
    public long getCheckPoint() {
        return checkPoint;
    }
    
    @Override
    public String toString() {
        return "LikeListResponse{" +
                "code=" + code +
                ", ids=" + ids +
                ", checkPoint=" + checkPoint +
                '}';
    }
}
