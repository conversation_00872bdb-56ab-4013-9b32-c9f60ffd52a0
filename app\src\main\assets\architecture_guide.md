# 轻聆音乐播放器 - MVVM架构指南

## 架构概述

轻聆音乐播放器采用MVVM (Model-View-ViewModel) 架构模式，这种架构模式将应用程序分为三个主要部分：

1. **Model (模型)**: 数据层，负责数据的获取和存储
2. **View (视图)**: UI层，负责界面的展示和用户交互
3. **ViewModel (视图模型)**: 业务逻辑层，连接View和Model，处理业务逻辑

## 目录结构

```
app/src/main/java/com/example/aimusicplayer/
├── api/                  # API相关类
│   ├── ApiClient.java    # API客户端
│   ├── ApiService.java   # API服务接口
│   └── ...
├── model/                # 数据模型类
│   ├── BannerResponse.java
│   ├── LyricResponse.java
│   ├── SongDetailResponse.java
│   └── ...
├── repository/           # 数据仓库类
│   ├── MusicRepository.java
│   ├── UserRepository.java
│   └── ...
├── service/              # 服务类
│   ├── PlaybackService.java  # 统一播放服务
│   └── ...
├── ui/                   # UI相关类
│   ├── discovery/        # 发现页面
│   ├── driving/          # 驾驶模式
│   ├── library/          # 音乐库
│   ├── login/            # 登录页面
│   ├── main/             # 主页面
│   ├── player/           # 播放器页面
│   ├── settings/         # 设置页面
│   └── splash/           # 启动页面
├── utils/                # 工具类
│   ├── Constants.java
│   ├── PreferenceUtils.java
│   └── ...
├── viewmodel/            # ViewModel类
│   ├── DiscoveryViewModel.java
│   ├── LoginViewModel.java
│   ├── PlayerViewModel.java
│   └── ...
└── MusicApplication.java # 应用程序入口
```

## 组件职责

### Model

- 定义数据结构
- 与API交互获取数据
- 处理数据持久化

### View (Activity/Fragment)

- 展示UI界面
- 捕获用户输入
- 观察ViewModel的LiveData更新UI
- 不包含业务逻辑

### ViewModel

- 处理业务逻辑
- 通过Repository获取数据
- 使用LiveData向View提供数据
- 处理View的事件

### Repository

- 作为数据源的抽象层
- 决定从网络或本地获取数据
- 提供统一的数据访问接口

## 数据流

1. View观察ViewModel的LiveData
2. 用户与View交互
3. View调用ViewModel的方法
4. ViewModel通过Repository获取/更新数据
5. Repository从API或本地数据源获取/更新数据
6. 数据变化通过LiveData传递给View
7. View更新UI

## 统一播放服务 (PlaybackService)

PlaybackService是应用的核心组件，负责音乐播放的所有功能：

- 管理播放队列
- 控制播放状态
- 处理播放模式
- 管理音频焦点
- 提供媒体通知栏

ViewModel通过绑定服务与PlaybackService通信，UI组件通过观察ViewModel的LiveData获取播放状态。

## 代码示例

### ViewModel示例

```java
public class PlayerViewModel extends ViewModel {
    private final MutableLiveData<String> currentSongTitle = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isPlaying = new MutableLiveData<>(false);
    
    // 获取当前歌曲标题
    public LiveData<String> getCurrentSongTitle() {
        return currentSongTitle;
    }
    
    // 获取播放状态
    public LiveData<Boolean> getIsPlaying() {
        return isPlaying;
    }
    
    // 播放/暂停
    public void togglePlayPause() {
        // 调用PlaybackService的方法
        // 更新LiveData
    }
}
```

### View示例

```java
public class PlayerFragment extends Fragment {
    private PlayerViewModel viewModel;
    
    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化ViewModel
        viewModel = new ViewModelProvider(this).get(PlayerViewModel.class);
        
        // 观察LiveData
        viewModel.getCurrentSongTitle().observe(getViewLifecycleOwner(), title -> {
            songTitleTextView.setText(title);
        });
        
        viewModel.getIsPlaying().observe(getViewLifecycleOwner(), isPlaying -> {
            playPauseButton.setImageResource(isPlaying ? 
                R.drawable.ic_pause : R.drawable.ic_play);
        });
        
        // 设置点击事件
        playPauseButton.setOnClickListener(v -> {
            viewModel.togglePlayPause();
        });
    }
}
```

## 最佳实践

1. **关注点分离**: 每个组件只负责自己的职责
2. **单向数据流**: 数据从ViewModel流向View，事件从View流向ViewModel
3. **无状态View**: View不存储状态，所有状态都在ViewModel中
4. **可测试性**: ViewModel和Repository应该易于测试
5. **错误处理**: 统一的错误处理机制

## 迁移指南

如果需要添加新功能或修改现有功能，请遵循以下步骤：

1. 定义数据模型 (Model)
2. 创建或更新Repository
3. 实现ViewModel中的业务逻辑
4. 创建或更新View组件
5. 连接View和ViewModel

## 注意事项

- 不要在View中直接访问Repository
- 不要在ViewModel中引用View
- 使用LiveData而不是回调
- 避免在View中存储状态
