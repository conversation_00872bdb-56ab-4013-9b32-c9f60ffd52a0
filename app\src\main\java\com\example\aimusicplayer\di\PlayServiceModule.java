package com.example.aimusicplayer.di;

import android.content.Context;

import com.example.aimusicplayer.service.PlayerController;
import com.example.aimusicplayer.service.PlayerControllerImpl;

import javax.inject.Singleton;

import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;

/**
 * 播放服务模块
 * 提供播放控制器的依赖注入
 */
@Module
@InstallIn(SingletonComponent.class)
public class PlayServiceModule {
    
    /**
     * 提供播放控制器
     * @param context 应用上下文
     * @return 播放控制器实例
     */
    @Provides
    @Singleton
    public PlayerController providePlayerController(@ApplicationContext Context context) {
        return new PlayerControllerImpl(context);
    }
}
