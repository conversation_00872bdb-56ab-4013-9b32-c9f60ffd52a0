package com.example.aimusicplayer.model;

/**
 * 歌词条目
 * 表示一行歌词及其对应的时间和翻译
 * 用于兼容旧代码
 */
public class LyricEntry {
    private long time;
    private String text;
    private String translatedText;

    public LyricEntry(long time, String text, String translatedText) {
        this.time = time;
        this.text = text;
        this.translatedText = translatedText;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTranslatedText() {
        return translatedText;
    }

    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
    }

    /**
     * 是否有翻译歌词
     * @return 是否有翻译歌词
     */
    public boolean hasTranslation() {
        return translatedText != null && !translatedText.isEmpty();
    }

    /**
     * 获取完整歌词文本（包括翻译）
     * @return 完整歌词文本
     */
    public String getFullText() {
        if (hasTranslation()) {
            return text + "\n" + translatedText;
        } else {
            return text;
        }
    }
}
