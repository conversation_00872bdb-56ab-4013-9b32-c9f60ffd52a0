package com.example.aimusicplayer.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.example.aimusicplayer.data.db.converter.DateConverter

/**
 * API缓存实体类
 * 用于缓存API响应数据
 */
@Entity(tableName = "api_cache")
@TypeConverters(DateConverter::class)
data class ApiCacheEntity(
    @PrimaryKey
    @ColumnInfo(name = "cache_key")
    val cacheKey: String,
    
    // 缓存数据
    @ColumnInfo(name = "data")
    val data: String,
    
    // 缓存时间
    @ColumnInfo(name = "cache_time")
    val cacheTime: Long = System.currentTimeMillis(),
    
    // 过期时间（毫秒）
    @ColumnInfo(name = "expiration_time")
    val expirationTime: Long = 5 * 60 * 1000, // 默认5分钟
    
    // 缓存类型
    @ColumnInfo(name = "cache_type")
    val cacheType: String = "DEFAULT"
)
