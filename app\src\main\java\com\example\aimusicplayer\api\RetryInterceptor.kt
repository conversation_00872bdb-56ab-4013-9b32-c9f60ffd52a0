package com.example.aimusicplayer.api

import android.util.Log
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * OkHttp重试拦截器
 * 用于在网络请求失败时自动重试
 * @param maxRetries 最大重试次数
 * @param retryDelayMillis 重试延迟（毫秒）
 */
class RetryInterceptor(
    private val maxRetries: Int = 3,
    private val retryDelayMillis: Long = 1000
) : Interceptor {
    
    companion object {
        private const val TAG = "RetryInterceptor"
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response: Response? = null
        var exception: IOException? = null
        
        var retryCount = 0
        while (retryCount < maxRetries) {
            try {
                // 如果是重试，且上一次有响应，但响应不成功
                if (retryCount > 0 && response != null && !response.isSuccessful) {
                    // 只对服务器错误（5xx）或请求过于频繁（429）进行重试
                    val code = response.code
                    if (code < 500 && code != 429) {
                        return response
                    }
                }
                
                // 尝试请求
                response = chain.proceed(request)
                
                // 如果请求成功，直接返回
                if (response.isSuccessful) {
                    return response
                }
                
                // 如果是服务器错误（5xx）或请求过于频繁（429），进行重试
                val code = response.code
                if (code >= 500 || code == 429) {
                    // 关闭响应体，避免资源泄漏
                    response.close()
                    
                    // 重试
                    retryCount++
                    Log.w(TAG, "服务器错误 ($code)，正在进行第 $retryCount 次重试")
                    
                    // 延迟一段时间再重试
                    Thread.sleep(retryDelayMillis)
                } else {
                    // 客户端错误，不进行重试
                    return response
                }
            } catch (e: IOException) {
                // 关闭响应体，避免资源泄漏
                response?.close()
                
                // 记录异常
                exception = e
                
                // 重试
                retryCount++
                Log.w(TAG, "网络错误，正在进行第 $retryCount 次重试", e)
                
                // 延迟一段时间再重试
                Thread.sleep(retryDelayMillis)
            }
        }
        
        // 如果所有重试都失败，抛出最后一个异常或返回最后一个响应
        if (exception != null) {
            throw exception
        }
        
        return response!!
    }
}
