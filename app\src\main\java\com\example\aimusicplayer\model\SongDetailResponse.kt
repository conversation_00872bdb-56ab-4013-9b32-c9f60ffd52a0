package com.example.aimusicplayer.model

/**
 * 歌曲详情响应
 * 用于兼容旧版本代码
 */
class SongDetailResponse {
    var code: Int = 0
    var songs: List<Song> = emptyList()
    
    class Song {
        var id: Long = 0
        var name: String? = null
        var duration: Long = 0
        
        private var ar: List<Artist>? = null
        private var al: Album? = null
        
        fun getAr(): List<Artist>? = ar
        fun getAl(): Album? = al
        
        class Artist {
            var id: Long = 0
            var name: String? = null
        }
        
        class Album {
            var id: Long = 0
            var name: String? = null
            var picUrl: String? = null
        }
    }
} 