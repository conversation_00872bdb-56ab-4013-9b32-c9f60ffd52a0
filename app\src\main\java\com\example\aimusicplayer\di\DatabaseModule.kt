package com.example.aimusicplayer.di

import android.content.Context
import androidx.room.Room
import com.example.aimusicplayer.data.db.AppDatabase
import com.example.aimusicplayer.data.db.dao.ApiCacheDao
import com.example.aimusicplayer.data.db.dao.PlayHistoryDao
import com.example.aimusicplayer.data.db.dao.PlaylistDao
import com.example.aimusicplayer.data.db.dao.SongDao
import com.example.aimusicplayer.data.db.dao.UserDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库模块
 * 提供数据库和DAO的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    /**
     * 提供AppDatabase实例
     */
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "music_player_db"
        )
        .addMigrations(AppDatabase.MIGRATION_1_2) // 添加数据库迁移
        .fallbackToDestructiveMigration() // 如果数据库版本变更且没有对应的迁移策略，重建数据库
        .build()
    }

    /**
     * 提供SongDao实例
     */
    @Provides
    @Singleton
    fun provideSongDao(database: AppDatabase): SongDao {
        return database.songDao()
    }

    /**
     * 提供PlaylistDao实例
     */
    @Provides
    @Singleton
    fun providePlaylistDao(database: AppDatabase): PlaylistDao {
        return database.playlistDao()
    }

    /**
     * 提供PlayHistoryDao实例
     */
    @Provides
    @Singleton
    fun providePlayHistoryDao(database: AppDatabase): PlayHistoryDao {
        return database.playHistoryDao()
    }

    /**
     * 提供UserDao实例
     */
    @Provides
    @Singleton
    fun provideUserDao(database: AppDatabase): UserDao {
        return database.userDao()
    }

    /**
     * 提供ApiCacheDao实例
     */
    @Provides
    @Singleton
    fun provideApiCacheDao(database: AppDatabase): ApiCacheDao {
        return database.apiCacheDao()
    }
}
