package com.example.aimusicplayer.service

import android.content.Context
import androidx.media3.common.Player
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 播放服务模块
 * 提供播放控制器的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object PlayServiceModule {
    private var player: Player? = null

    fun setPlayer(player: Player) {
        PlayServiceModule.player = player
    }

    fun getPlayer(): Player? {
        return player
    }

    @Provides
    @Singleton
    fun providePlayerController(@ApplicationContext context: Context): PlayerController {
        return PlayerControllerImpl(context)
    }
}
