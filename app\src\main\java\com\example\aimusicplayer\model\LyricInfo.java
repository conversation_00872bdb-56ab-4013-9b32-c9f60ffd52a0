package com.example.aimusicplayer.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 歌词信息
 * 包含歌曲信息和歌词条目列表
 * 用于兼容旧代码
 */
public class LyricInfo {
    private String title;
    private String artist;
    private String album;
    private String lyricist;
    private String composer;
    private List<LyricEntry> entries;
    private boolean hasTranslation;

    public LyricInfo() {
        entries = new ArrayList<>();
        hasTranslation = false;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public String getAlbum() {
        return album;
    }

    public void setAlbum(String album) {
        this.album = album;
    }

    public String getLyricist() {
        return lyricist;
    }

    public void setLyricist(String lyricist) {
        this.lyricist = lyricist;
    }

    public String getComposer() {
        return composer;
    }

    public void setComposer(String composer) {
        this.composer = composer;
    }

    public List<LyricEntry> getEntries() {
        return entries;
    }

    public void setEntries(List<LyricEntry> entries) {
        this.entries = entries;
        
        // 检查是否有翻译歌词
        for (LyricEntry entry : entries) {
            if (entry.hasTranslation()) {
                hasTranslation = true;
                break;
            }
        }
    }

    public void addEntry(LyricEntry entry) {
        entries.add(entry);
        
        // 检查是否有翻译歌词
        if (entry.hasTranslation()) {
            hasTranslation = true;
        }
    }

    public int size() {
        return entries.size();
    }

    public boolean isEmpty() {
        return entries.isEmpty();
    }

    public boolean hasTranslation() {
        return hasTranslation;
    }

    /**
     * 根据时间查找歌词行索引
     * @param time 时间（毫秒）
     * @return 歌词行索引，如果没有找到则返回-1
     */
    public int findLineIndexByTime(long time) {
        if (entries.isEmpty()) return -1;
        
        // 如果当前位置小于第一行时间，返回-1
        if (time < entries.get(0).getTime()) return -1;
        
        // 如果当前位置大于最后一行时间，返回最后一行
        if (time >= entries.get(entries.size() - 1).getTime()) return entries.size() - 1;
        
        // 二分查找当前行
        int left = 0;
        int right = entries.size() - 1;
        
        while (left <= right) {
            int mid = (left + right) / 2;
            
            if (mid + 1 < entries.size() && time >= entries.get(mid).getTime() && time < entries.get(mid + 1).getTime()) {
                return mid;
            } else if (time < entries.get(mid).getTime()) {
                right = mid - 1;
            } else {
                left = mid + 1;
            }
        }
        
        return left - 1;
    }
}
