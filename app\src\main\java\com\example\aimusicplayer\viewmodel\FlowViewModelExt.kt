package com.example.aimusicplayer.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.asFlow
import com.example.aimusicplayer.api.ApiCallback
import com.example.aimusicplayer.api.ApiManager
import com.example.aimusicplayer.api.ApiResponse
import com.example.aimusicplayer.utils.NetworkResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import retrofit2.Call

/**
 * FlowViewModel扩展函数
 * 提供更多实用的扩展函数，增强Flow的功能
 */

/**
 * 将Flow<NetworkResult<T>>转换为LiveData<T>
 * 自动处理加载状态和错误
 */
fun <T> FlowViewModel.flowResultAsLiveData(
    flow: Flow<NetworkResult<T>>,
    defaultValue: T
): LiveData<T> {
    val liveData = MutableLiveData(defaultValue)
    viewModelScope.launch {
        flow.collectLatest { result ->
            when (result) {
                is NetworkResult.Success -> liveData.postValue(result.data)
                is NetworkResult.Loading -> { /* 加载中状态已在Flow中处理 */ }
                is NetworkResult.Error -> { /* 错误已在Flow中处理 */ }
            }
        }
    }
    return liveData
}

/**
 * 将Flow<NetworkResult<T>>转换为StateFlow<T>
 * 自动处理加载状态和错误
 */
fun <T> FlowViewModel.flowResultAsStateFlow(
    flow: Flow<NetworkResult<T>>,
    defaultValue: T
): StateFlow<T> {
    val stateFlow = MutableStateFlow(defaultValue)
    viewModelScope.launch {
        flow.collectLatest { result ->
            when (result) {
                is NetworkResult.Success -> stateFlow.value = result.data
                is NetworkResult.Loading -> { /* 加载中状态已在Flow中处理 */ }
                is NetworkResult.Error -> { /* 错误已在Flow中处理 */ }
            }
        }
    }
    return stateFlow
}

/**
 * 将Flow<T>映射为Flow<NetworkResult<R>>
 * 自动处理转换和错误
 */
fun <T, R> Flow<T>.mapToResult(transform: (T) -> R): Flow<NetworkResult<R>> {
    return this.map { value ->
        try {
            NetworkResult.Success(transform(value))
        } catch (e: Exception) {
            NetworkResult.Error(e.message ?: "转换错误")
        }
    }
}

/**
 * 执行Retrofit调用并返回LiveData
 * 兼容旧的API调用方式
 */
fun <T> FlowViewModel.executeApiCall(
    apiManager: ApiManager,
    call: Call<T>,
    defaultValue: T? = null
): LiveData<T?> {
    val responseLiveData = MutableLiveData<ApiResponse<T>>()
    val resultLiveData = MutableLiveData<T?>(defaultValue)

    apiManager.execute(call, responseLiveData, errorMessage as MutableLiveData<String>)

    viewModelScope.launch {
        responseLiveData.asFlow().collectLatest { response ->
            if (response.isSuccessful()) {
                resultLiveData.postValue(response.getData())
            }
        }
    }

    return resultLiveData
}

/**
 * 执行Retrofit调用并返回Flow
 * 兼容旧的API调用方式
 */
fun <T> FlowViewModel.executeApiCallAsFlow(
    apiManager: ApiManager,
    call: Call<T>
): Flow<NetworkResult<T>> {
    val responseLiveData = MutableLiveData<ApiResponse<T>>()

    apiManager.execute(call, responseLiveData, errorMessage as MutableLiveData<String>)

    return responseLiveData.asFlow().map { response ->
        if (response.isSuccessful() && response.getData() != null) {
            NetworkResult.Success(response.getData()!!)
        } else {
            NetworkResult.Error(response.getMessage() ?: "未知错误")
        }
    }
}

/**
 * 执行Retrofit调用并使用回调
 * 兼容旧的API调用方式
 */
fun <T> FlowViewModel.executeApiCallWithCallback(
    apiManager: ApiManager,
    call: Call<T>,
    onSuccess: (T) -> Unit,
    onError: (String) -> Unit = { publicHandleError(Exception(it), it) }
) {
    apiManager.execute(call, object : ApiCallback<T>(null, null) { // Pass null for LiveData as they are not used in this context
        override fun onSuccess(response: T) {
            onSuccess(response)
        }

        override fun onError(t: Throwable) {
            onError(t.message ?: "未知错误")
        }
    })
}

/**
 * 合并多个Flow<NetworkResult<T>>，只有当所有Flow都成功时才返回成功结果
 * 如果任何一个Flow失败，则返回失败结果
 */
fun <T1, T2, R> FlowViewModel.combineResults(
    flow1: Flow<NetworkResult<T1>>,
    flow2: Flow<NetworkResult<T2>>,
    transform: (T1, T2) -> R
): Flow<NetworkResult<R>> {
    return flow1.combine(flow2) { result1, result2 ->
        when {
            result1 is NetworkResult.Loading || result2 is NetworkResult.Loading ->
                NetworkResult.Loading()
            result1 is NetworkResult.Error ->
                NetworkResult.Error(result1.message)
            result2 is NetworkResult.Error ->
                NetworkResult.Error(result2.message)
            result1 is NetworkResult.Success && result2 is NetworkResult.Success ->
                NetworkResult.Success(transform(result1.data, result2.data))
            else ->
                NetworkResult.Error("未知错误")
        }
    }
}

/**
 * 合并三个Flow<NetworkResult<T>>，只有当所有Flow都成功时才返回成功结果
 */
fun <T1, T2, T3, R> FlowViewModel.combineResults(
    flow1: Flow<NetworkResult<T1>>,
    flow2: Flow<NetworkResult<T2>>,
    flow3: Flow<NetworkResult<T3>>,
    transform: (T1, T2, T3) -> R
): Flow<NetworkResult<R>> {
    return combine(flow1, flow2, flow3) { result1, result2, result3 ->
        when {
            result1 is NetworkResult.Loading || result2 is NetworkResult.Loading || result3 is NetworkResult.Loading ->
                NetworkResult.Loading()
            result1 is NetworkResult.Error ->
                NetworkResult.Error(result1.message)
            result2 is NetworkResult.Error ->
                NetworkResult.Error(result2.message)
            result3 is NetworkResult.Error ->
                NetworkResult.Error(result3.message)
            result1 is NetworkResult.Success && result2 is NetworkResult.Success && result3 is NetworkResult.Success ->
                NetworkResult.Success(transform(result1.data, result2.data, result3.data))
            else ->
                NetworkResult.Error("未知错误")
        }
    }
}

/**
 * 添加加载和完成回调到Flow
 */
fun <T> Flow<T>.withLoadingAndCompletion(
    viewModel: FlowViewModel,
    onStart: () -> Unit = { viewModel.publicSetLoading(true) },
    onCompletion: () -> Unit = { viewModel.publicSetLoading(false) }
): Flow<T> {
    return this
        .onStart { onStart() }
        .onCompletion { onCompletion() }
}

/**
 * 添加错误处理到Flow
 */
fun <T> Flow<T>.withErrorHandling(
    viewModel: FlowViewModel,
    onError: (Throwable) -> Unit = { viewModel.publicHandleError(it) }
): Flow<T> {
    return this.catch { e ->
        onError(e)
        throw e
    }
}
