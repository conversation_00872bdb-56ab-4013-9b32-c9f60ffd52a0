package com.example.aimusicplayer.utils

import android.content.Context
import android.widget.Toast
import androidx.annotation.StringRes

/**
 * 显示短时间Toast
 * @param message 消息文本
 */
fun Context.showToast(message: String) {
    Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
}

/**
 * 显示短时间Toast
 * @param messageResId 消息资源ID
 */
fun Context.showToast(@StringRes messageResId: Int) {
    Toast.makeText(this, messageResId, Toast.LENGTH_SHORT).show()
}

/**
 * 显示长时间Toast
 * @param message 消息文本
 */
fun Context.showLongToast(message: String) {
    Toast.makeText(this, message, Toast.LENGTH_LONG).show()
}

/**
 * 显示长时间Toast
 * @param messageResId 消息资源ID
 */
fun Context.showLongToast(@StringRes messageResId: Int) {
    Toast.makeText(this, messageResId, Toast.LENGTH_LONG).show()
}
