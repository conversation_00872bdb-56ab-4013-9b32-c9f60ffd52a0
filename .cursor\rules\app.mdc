本项目旨在**基于现有代码进行重构与优化**，使用 Android 原生开发 (主要使用 **Java**)、MVVM 架构、异步处理方案 (例如 RxJava, AsyncTask/Executors, 或 Java 中可用的 Coroutines/Flow 实现) 及网易云音乐第三方 API，开发一款功能完善的安卓音乐播放器。**UI设计与布局应优先复用原有实现**。核心要求是实现所有模块的预期功能，并对**每一次API调用都进行严格的可用性判断和处理，在API不可用时向用户提供清晰、具体、且友好的UI反馈与指引。** **所有API均返回JSON格式数据**，客户端需进行相应解析和健壮的错误处理。

**全局实施要求（贯穿整个应用开发）：**

1.  **API客户端配置与核心错误处理逻辑：**
    *   配置全局唯一的API客户端实例（推荐 Retrofit + OkHttp）。
    *   实现并配置持久化的 `CookieJar`，用以在App会话间有效管理用户登录后获取的 `MUSIC_U` 等关键Cookie。
    *   对所有适用的GET请求，自动添加 `timestamp={new Date().getTime()}` 参数，以防止不必要的缓存问题。
    *   **Repository/UseCase层设计：** 针对每一次API调用，此层必须返回一个结构化的 `Result` 对象（例如，自定义的包含成功数据或错误详情的 `Sealed Class` 或 `data class`，在Java中可使用类似模式或自定义结果类）。此 `Result` 对象应能明确区分API调用成功获取数据的情况与各种失败类型。失败情况应使用一个统一的 `ApiErrorType` 枚举（详细定义见下文“通用API可用性处理框架”）进行精确分类，并携带如HTTP状态码、错误消息、原始异常等上下文信息。
2.  **ViewModel与UI状态管理：**
    *   为应用的每一个Fragment或复杂UI组件配备专属的ViewModel，负责处理业务逻辑和数据转换。
    *   ViewModel通过 `LiveData` (或在Java中适用的其他响应式数据流如RxJava的 `Observable`/`Flowable`) 暴露UI状态。UI状态对象应设计为能够清晰反映以下几种情况：
        *   `加载中 (Loading)`: 数据正在请求，UI应显示适当的加载指示（如骨架屏）。
        *   `成功 (Success(data))`: 数据成功获取，`data`为具体内容。
        *   各种 `错误 (Error(type: ApiErrorType, message: String, retryAction: (() -> Unit)?, affectedFeatureIdentifier: String?))`: API调用失败，包含错误类型、用户可读的错误消息、可选的重试操作和受影响的功能标识。
        *   `功能不可用 (FeatureUnavailable(featureIdentifier: String))`: 由于依赖的API持续不可用（如404），导致某个功能被禁用或隐藏。
        *   `空状态 (Empty(message: String))`: API调用成功，但返回的数据为空（例如，用户的播放列表为空）。
3.  **权限处理：**
    *   在合适时机（如应用启动或首次访问相关功能时）请求必要的运行时权限：
        *   `READ_MEDIA_AUDIO` (或适配旧版安卓的 `READ_EXTERNAL_STORAGE`)：用于扫描本地音乐。
        *   `RECORD_AUDIO`：用于驾驶模式等场景下的语音指令。
        *   `POST_NOTIFICATIONS` (Android 13+)：用于显示媒体播放通知。
    *   对权限请求结果进行处理，若用户拒绝，应给出合理解释并引导用户。
4.  **导航：**
    *   推荐使用 Android Jetpack Navigation Component 实现Fragment间的导航。

---

**通用API可用性处理框架 (必须严格应用于每一次API调用)**

此框架定义了应用如何识别并响应不同类型的API可用性问题。ViewModel负责将Repository层返回的 `Result.failure(ApiErrorType, ...)` 转换为用户可见的UI状态和提示。

*   **`ApiErrorType.NETWORK_ERROR` (例如 `IOException`, 设备无网络连接):**
    *   **UI指示：** 显示与当前操作或功能相关的网络错误信息。
        *   全局性影响 (如启动时无法连接): 应用顶部横幅/Snackbar提示 "当前网络不可用，请检查您的网络设置。"
        *   特定功能模块: "[功能名称]加载失败，请检查网络连接后重试。" (例如: "歌单详情同步失败，请检查网络。")
    *   **操作：** 提供明确的 "重试" 按钮或手势（如下拉刷新）。在适当情况下，临时禁用依赖于此调用的UI交互元素。
*   **`ApiErrorType.SERVER_ERROR` (HTTP 5xx 系列错误):**
    *   **UI指示：** 显示 "[功能名称]服务暂时遇到问题，请稍后重试。" (例如: "排行榜服务当前繁忙，请稍后刷新。")
    *   **操作：** 提供 "重试" 选项（可考虑实现指数退避策略）。如果特定接口在多次重试后依然持续发生5xx错误，应将详细错误信息（包含接口路径、参数、错误码、发生时间等）记录到您的外部错误监控服务 (如Sentry, Firebase Crashlytics)，并标记为 "持续服务器错误"。
*   **`ApiErrorType.ENDPOINT_NOT_FOUND` (HTTP 404 错误):**
    *   **UI指示：** 显示 "[功能名称]当前无法使用。" 或 "[具体功能]的相关服务未找到，可能已下线。"
    *   **操作：**
        1.  **关键步骤：立即在UI层面禁用或彻底隐藏依赖于此缺失接口的整个功能模块或相关的UI元素。**
        2.  不应为此特定错误类型提供 "重试" 选项，因为接口本身不存在。
        3.  **日志记录：** **在首次确认特定接口持续返回404时，必须立即向错误监控服务报告此情况为 "接口缺失: [接口路径]"。**
*   **`ApiErrorType.UNAUTHENTICATED` (HTTP 401 错误):**
    *   **UI指示：** 显示 "您尚未登录，请登录后继续操作。" 或 "登录状态已过期，请重新登录。"
    *   **操作：** 立即清除应用本地存储的所有用户会话数据和认证凭证。将用户强制导航至应用的登录界面。
*   **`ApiErrorType.UNAUTHORIZED` (HTTP 403 错误):**
    *   **UI指示：** 显示 "抱歉，您没有足够的权限访问此[内容/功能]。"
    *   **操作：** 阻止当前操作。通常不提供重试选项，除非应用的业务逻辑允许通过其他途径获取权限（例如，用户升级账户等级）。
*   **`ApiErrorType.RATE_LIMITED` (HTTP 429 错误):**
    *   **UI指示：** 显示 "您的操作过于频繁，请稍作等待后再尝试。"
    *   **操作：** 临时禁用触发此错误的交互元素。根据API的限流策略，可在客户端实现带指数退避的自动重试逻辑（需谨慎，避免加剧限流）。
*   **`ApiErrorType.HTTP_ERROR` (其他未被上述类型覆盖的客户端/服务器HTTP错误，例如 400 Bad Request, 405 Method Not Allowed):**
    *   **UI指示：** 显示一个通用但包含错误代码的提示: "请求[功能名称]时发生错误 (代码: [httpCode])，请检查后重试。"
    *   **操作：** 提供 "重试" 选项。
*   **`ApiErrorType.API_BUSINESS_ERROR` (例如，API的HTTP状态码为2xx/3xx，但其JSON响应体中的业务状态码 `code` 指示错误，如 `code != 200`):**
    *   **UI指示：** 优先使用API在其JSON错误响应中提供的 `message` 字段（如果该消息对用户友好且易于理解）。否则，使用通用的 "[功能名称]处理失败，详情请稍后重试或联系客服。"
    *   **操作：** 具体行为取决于业务错误的性质。某些错误可能允许用户修正输入后重试，其他则可能表示操作无法完成。
*   **`ApiErrorType.UNKNOWN_ERROR` (例如，JSON解析错误、在处理API响应过程中的意外客户端异常):**
    *   **UI指示：** 显示 "应用发生未知错误，请稍后重试。"
    *   **操作：** 提供 "重试" 选项。务必将这类错误连同详细的堆栈跟踪信息记录到错误监控服务，以便开发者排查。

**API不可用时的UI指示通用原则：**
*   **清晰性与特异性：** 错误消息应尽可能具体到受影响的功能或数据项，避免使用过于笼统的提示。
*   **消息位置与形式：**
    *   **内联提示：** 对于影响UI小部分（例如用户资料中的某一项统计数据无法加载）的错误，应在对应UI元素附近内联显示错误信息和小型重试图标。
    *   **占位视图/全屏错误：** 对于影响整个屏幕区域或核心列表（例如“评论区加载失败”、“歌单详情无法打开”）的错误，应使用带有清晰错误消息和醒目 "重试" 按钮的占位视图替换原内容区域。
    *   **全局横幅/Snackbar：** 适用于应用范围的、非阻塞性的问题，如网络连接暂时中断后恢复的通知，或不影响当前主操作的次要错误提示。
*   **可操作性：** 始终为用户提供明确的前进路径或解决方案：如 "重试"、"前往登录页面"、"检查网络设置"、"联系支持"。
*   **禁用状态的明确指示：** 当某个功能因其依赖的API被确认为 `ENDPOINT_NOT_FOUND` (接口缺失) 或因其他原因持续不可用，导致该功能无法使用时，必须在UI上明确地视觉禁用相关交互元素，并阻止用户尝试使用。可考虑提供简短说明为何该功能不可用。

---

**模块1：用户认证**

**核心职责：** 管理用户通过各种方式（二维码、手机号密码）的登录、登出，并维护和校验用户会话状态。

**API接口及主要逻辑：**
*   **二维码登录流程:**
    *   `GET /login/qr/key`: 获取二维码key。
    *   `GET /login/qr/create?key={key}&qrimg=true`: 根据key生成二维码图片 (base64)。
    *   `GET /login/qr/check?key={key}&timestamp={timestamp}`: 轮询检查扫码状态 (800:过期, 801:待扫, 802:待确认, 803:成功)。成功后务必安全存储返回的Cookies。
*   **手机号密码登录:**
    *   `POST /login/cellphone?phone={phone}&password={password}` (或使用 `md5_password`): 处理登录请求，成功后安全存储Cookies。
*   **会话状态检查:**
    *   `GET /login/status?timestamp={timestamp}`: 应用启动时、执行关键需授权操作前、或从后台长时间返回前台时调用，根据响应中 `profile` 对象是否为 `null` 来判断当前登录状态的有效性。
*   **登出操作:**
    *   `GET /logout`: 清除所有本地存储的与用户相关的认证凭证和缓存数据。
*   **匿名/游客模式 (可选实现，但推荐):**
    *   `GET /register/anonimous`: 获取游客身份的Cookie，允许用户在未登录状态下浏览部分公共内容（如“音乐探索”模块的部分推荐、排行榜等）。

**可用性检查与UI反馈 (严格应用通用API可用性处理框架):**
*   对于 `GET /login/status`:
    *   **若API调用本身不可用 (如网络/服务器错误):** UI应清晰提示 "无法连接认证服务，请稍后重试..."，并可能限制用户访问需要认证的模块，同时提供全局重试机制。
*   对于所有登录尝试接口 (如 `/login/qr/check`, `/login/cellphone`):
    *   **若API调用本身不可用 (如网络/服务器错误):** 相应的登录按钮或整个登录流程应向用户反馈 "登录服务当前繁忙，请稍后再试。"
    *   **若特定登录方式的接口被识别为 `ENDPOINT_NOT_FOUND` (404):** UI应明确告知用户该登录方式 "暂不可用"，并考虑禁用该登录选项。同时，按框架要求报告此 "接口缺失"问题。

---

**模块2：主界面 (`MainActivity2.java`)**

**核心职责：** 作为应用的整体容器和核心交互界面，负责承载各个功能模块的Fragment，进行权限管理和初步的环境配置。

**布局要求：** **复用并优化现有**全屏显示、无系统标题栏的布局。包含左侧侧边导航栏 (Sidebar，例如使用`DrawerLayout`) 和右侧用于显示主要内容的 `FragmentContainerView` (id: `fragment_container_main`)。

**侧边栏UI设计与交互：**
*   UI: **复用现有**垂直排列的图标按钮列表，包括：播放器、我的音乐库、音乐探索、驾驶模式、用户中心、设置。
*   选中项高亮：当前选中的导航项需有明确的视觉高亮效果。
*   交互：支持点击图标切换 `fragment_container_main` 中显示的Fragment；支持超时自动隐藏（若设计需要）；支持从屏幕左边缘向右滑动呼出侧边栏。

**API交互与核心逻辑：**
*   在 `MainActivity` 的 `onCreate` 或其关联的ViewModel的 `init` 阶段：
    *   首先调用 `GET /login/status` 检查并确认用户当前的实际登录状态。
    *   **根据登录状态进行处理：**
        *   **未登录：** 侧边栏的“用户中心”区域应显示“点击登录”或默认的游客头像。当用户首次尝试进入需要登录才能访问的模块（例如“我的音乐库”）时，应主动将其导航至登录界面。
        *   **已登录：**
            *   可选：异步调用 `GET /user/detail?uid={userId}` (其中 `userId` 从 `/login/status` 的成功响应中获取) 来获取用户的昵称和头像，并将其展示在侧边栏的顶部区域（如果UI设计中有此规划）。
            *   可考虑预加载一些与用户相关的、应用启动后很快会用到的基础数据。
*   权限授予后，默认加载 `PlayerFragment` 到 `fragment_container_main`。

**可用性检查与UI反馈 (严格应用通用API可用性处理框架):**
*   对于 `GET /login/status` (应用启动时调用):
    *   **若API调用本身不可用:** 应用可能需要启动到一个“受限模式”或“离线模式”（如果游客浏览功能也不可用或同样依赖失败的API）。此时应向用户显示明确的提示，例如 "无法连接到服务器，请检查网络连接后重启应用。"
*   对于可选的 `/user/detail` 或 `/user/account` (用于侧边栏头部信息展示):
    *   **若API调用本身不可用:** 侧边栏头部可以显示为默认的游客状态，或提示 "用户信息加载失败"。此问题不应阻塞应用的核心功能。若此类接口持续返回404或5xx错误，应按框架要求进行报告。

---

**模块3：播放器视图 (`PlayerFragment.java`)**

**核心职责：** 显示当前正在播放歌曲的详细信息（封面、歌名、歌手、歌词、播放进度），并提供完整的播放控制功能。

**布局要求 (`fragment_player.xml`):** **复用并优化现有**布局 (推荐使用 `ConstraintLayout` 或带权重的 `LinearLayout`)：
*   **左侧 (约占40%宽度):**
    *   `ImageView` (id: `imageview_player_album_art`): 显示大尺寸、正方形的歌曲封面。
    *   `TextView` (id: `textview_player_song_title`): 显示当前歌曲名。
    *   `TextView` (id: `textview_player_artist_name`): 显示当前歌手名。
*   **右侧 (约占60%宽度):**
    *   **歌词区域:** 使用自定义的 `LyricsView` 或 `RecyclerView` (id: `recyclerview_player_lyrics`)，占据右侧大部分空间。要求支持当前播放行高亮，并能随播放进度自动平滑滚动。
    *   **控制面板 (位于歌词区域下方):** 水平排列的 `ImageButton` 组件：
        *   `button_player_playlist`: 点击后弹出当前播放队列视图 (例如，使用 `BottomSheetDialog`)。
        *   `button_player_collect`: 收藏/取消收藏当前歌曲。
        *   `button_player_prev`: 播放上一首。
        *   `button_player_play_pause`: 播放/暂停 (此按钮通常比其他控制按钮稍大)。
        *   `button_player_next`: 播放下一首。
        *   `button_player_play_mode`: 切换播放模式（如列表循环、单曲循环、随机播放）。
    *   **进度与时间显示 (位于控制面板下方或上方):**
        *   `SeekBar` (id: `seekbar_player_progress`): 显示和控制播放进度。
        *   `TextView` (id: `textview_player_current_time`): 显示当前播放时间。
        *   `TextView` (id: `textview_player_total_time`): 显示歌曲总时长。

**API交互与核心逻辑：**
*   **加载并播放歌曲数据 (当接收到要播放的 `songId` 时):**
    1.  `GET /check/music?id={songId}`: **(关键前置步骤)** 检查歌曲是否可播放。若响应中 `success: false`，则根据 `message` 字段（例如“付费歌曲”、“因版权问题无法播放”）向用户显示提示，并阻止后续获取播放链接的操作。
    2.  `GET /song/detail?ids={songId}`: 获取歌曲的元数据，包括歌名 (`name`)、歌手信息 (`ar` 数组)、专辑信息（`al` 对象，包含封面URL `al.picUrl`、专辑名 `al.name`）、歌曲时长 (`dt`)、付费信息 (`fee`, `privileges.chargeInfoList`)等。
    3.  `GET /song/url/v1?id={songId}&level={level}`: 获取实际的音频流URL。`level` 参数应基于用户在应用设置中选择的音质偏好 (`standard`, `higher`, `exhigh`, `lossless`, `hires`)。若歌曲需要付费或VIP才能收听，此接口可能不返回有效的播放URL。
    4.  `GET /lyric?id={songId}`: 获取歌词数据。响应中可能包含多种格式的歌词：
        *   `lrc.lyric`: 标准LRC格式歌词。
        *   `tlyric.lyric`: 翻译歌词。
        *   `romalrc.lyric`: 罗马音歌词。
        *   应用需能解析LRC歌词的时间戳 `[mm:ss.xx]`，并在歌词视图中实现。
*   **播放控制相关操作:**
    *   播放/暂停、上一首、下一首、拖动 `SeekBar` 调整进度：主要控制应用内集成的媒体播放器实例（如 `ExoPlayer` 或 `MediaPlayer`）。
    *   **收藏/取消收藏歌曲 (`button_player_collect`):**
        *   UI可先进行乐观更新（立即改变收藏按钮的视觉状态）。
        *   调用 `GET /like?id={songId}&like={true|false}&timestamp={timestamp}`。`like=true` 为收藏，`false` 为取消。务必携带 `timestamp` 参数。
        *   若API调用失败，UI状态应回滚到操作前的状态，并向用户提示操作失败。
        *   播放器加载歌曲时，应通过 `GET /likelist?uid={userId}` 或检查 `/song/detail` 返回的 `privileges` 信息来确定当前歌曲的初始收藏状态，并正确设置按钮。
    *   **添加到播放列表 (`button_player_playlist` 的更多操作):**
        *   先调用 `GET /user/playlist?uid={userId}&limit=1000` 获取用户（自己创建及收藏的）歌单列表，供用户选择目标歌单。
        *   `GET /playlist/tracks?op=add&pid={selectedPlaylistId}&tracks={songId1},{songId2},...`: 将一首或多首歌曲添加到用户选定的歌单中。
    *   **"心动模式" (智能推荐播放 - 若实现此功能):**
        *   `GET /playmode/intelligence/list?id={currentSongId}&pid={currentPlaylistId}`: 根据当前播放的歌曲ID和其所在的歌单ID，获取智能推荐的下一首歌曲列表。
*   **UI动态更新:**
    *   ViewModel应通过 `LiveData` 或其他响应式数据流观察播放服务的状态（当前歌曲、播放/暂停状态、缓冲状态、播放进度、时长、歌词同步信息等），并驱动UI各元素的实时更新。
    *   播放/暂停按钮、收藏按钮、播放模式按钮的图标必须根据当前状态动态切换。

**可用性检查与UI反馈 (严格应用通用API可用性处理框架，以下为PlayerFragment中的特定场景示例):**
*   **`GET /check/music?id={songId}`:**
    *   **若API调用本身不可用 (网络/服务器错误):** UI提示: "无法验证歌曲可用性，请检查网络或稍后重试。" 播放尝试可能会被阻止，或者应用可以选择乐观地尝试播放（依赖后续 `/song/url/v1` 的错误处理）。
    *   **若接口返回404 (接口缺失):** UI提示: "歌曲验证服务当前不可用。" 这是核心播放流程的严重问题。应按框架要求记录为 "接口缺失"。
*   **`GET /song/detail?ids={songId}`:**
    *   **若API调用本身不可用:** UI提示: "无法加载歌曲基本信息。" 播放器界面的歌曲名、歌手、封面等区域应显示占位符或 "信息加载失败" 提示。重试机制至关重要。若持续404/5xx，应记录。
*   **`GET /song/url/v1?id={songId}&level={level}`:**
    *   **若API调用本身不可用:** UI提示: "无法获取歌曲播放链接。" 播放器应显示 "播放失败" 或类似信息。提供重试。若持续404/5xx，应记录。
    *   (这与 `/check/music` 接口返回歌曲因版权等原因不可播放是两种不同情况；这里指的是获取播放链接的 `/song/url/v1` 服务本身出现问题)。
*   **`GET /lyric?id={songId}`:**
    *   **若API调用本身不可用 (网络/服务器错误):** 歌词视图区域应显示 "歌词加载失败，请检查网络连接" 或 "歌词服务暂时出现问题，请稍后重试"。
    *   **若接口返回404 (接口缺失):** 歌词视图区域应显示 "歌词功能当前不可用"。应考虑禁用与歌词相关的交互。按框架要求记录为 "接口缺失"。
*   **`GET /like?id={songId}&like={...}` (收藏/取消收藏操作):**
    *   **若API调用本身不可用:** 应通过Toast/Snackbar提示用户 "收藏操作失败，请稍后重试。" 若UI已进行乐观更新，此时应将其状态回滚。若持续404/5xx，应记录。
*   **`GET /playlist/tracks?op=add&...` (添加到歌单操作):**
    *   **若API调用本身不可用:** 应通过Toast/Snackbar提示用户 "添加到歌单失败，相关服务暂不可用。" 若持续404/5xx，应记录。
*   **对已登录用户的依赖:** 收藏、添加到用户歌单等操作均依赖于用户已登录。若检测到用户未登录，相应的功能按钮应被禁用，或在点击后提示用户先登录。

**PlayerFragment 可选的拓展功能:**
*   `GET /comment/music?id={songId}&limit={limit}&offset={offset}&beforeTime={timestamp}`: 在播放界面提供入口，允许用户查看和分页加载当前歌曲的热门评论或最新评论。`beforeTime` 参数可用于实现获取更早历史评论的分页加载。
*   `GET /simi/song?id={songId}&limit={limit}&offset={offset}`: 在歌曲播放结束时，或通过用户操作（如点击推荐按钮），推荐与当前歌曲相似的其他歌曲。
*   `GET /album?id={albumId}`: 若当前播放的歌曲属于某个专辑 (albumId 可从 `/song/detail` 响应的 `al.id` 获取)，可提供入口引导用户查看该专辑的完整歌曲列表及其他信息。

---

**模块4：我的音乐库视图 (`MusicLibraryFragment.java`) （待开发）**

**核心职责：** （待开发）允许用户浏览和管理其本地音乐以及在云端收藏的歌曲、专辑、歌手和歌单，并提供库内搜索功能。
**布局要求 (`fragment_music_library.xml`):** **复用并优化现有**布局。通常包含顶部搜索框、中部 `TabLayout` 与 `ViewPager2` (Tabs: "本地音乐", "歌曲", "专辑", "歌手", "歌单")，以及各Tab对应的 `RecyclerView` 列表。列表项布局也应复用。
**API交互与核心逻辑 (高级别定义)：**
*   "本地音乐": `MediaStore` API。可选在线匹配元数据 (`GET /search` 或 `/cloudsearch`)。
*   "歌曲" (我喜欢的): `GET /user/playlist` (获取“我喜欢的音乐”歌单ID) -> `GET /playlist/detail` 或 `GET /playlist/track/all`。备选: `GET /likelist` -> `GET /song/detail`。
*   "专辑": `GET /album/sublist`。
*   "歌手": `GET /artist/sublist`。
*   "歌单": `GET /user/playlist`。
*   库内搜索: 本地数据客户端筛选；云端数据可客户端筛选或 `GET /cloudsearch` 后匹配。
*   列表项交互: 点击播放；更多操作（取消收藏、添加到歌单、删除等）调用相应API。
*   分页加载: 使用API提供的分页参数。
**可用性检查与UI反馈:** 严格遵循通用框架。各Tab内容加载及操作失败时，提供占位错误视图和Toast/Snackbar提示。

---

**模块5：音乐探索视图 (`DiscoveryFragment.java`) （待开发）**

**核心职责：** （待开发）提供多种在线音乐发现渠道，如个性化推荐、各类排行榜、新歌新碟推荐等，并支持在线音乐搜索。
**布局要求 (`fragment_discovery.xml`):** **复用并优化现有**布局。通常包含顶部搜索框、内容区使用 `NestedScrollView` 或 `RecyclerView` (支持多ViewType) 构建分区块页面。区块包含标题和横向滚动的 `RecyclerView` (展示歌单/专辑卡片、歌曲列表等)。卡片和列表项布局复用。
**API交互与核心逻辑 (高级别定义)：**
*   在线搜索: `GET /cloudsearch` (主搜), `GET /search/suggest` (建议)。
*   内容区块:
    *   Banner: `GET /banner`。
    *   推荐歌单: `GET /personalized/playlist` (需登录), `GET /top/playlist/highquality`, `GET /playlist/hot` -> `GET /top/playlist`。
    *   每日推荐歌曲: `GET /recommend/songs` (需登录)。
    *   新歌/新碟: `GET /top/song`, `GET /album/newest`, `GET /album/new`。
    *   排行榜: `GET /toplist` -> `GET /playlist/detail`。
    *   推荐歌手: `GET /toplist/artist`。
    *   首页复杂布局: `GET /homepage/block/page`, `GET /homepage/dragon/ball`。
*   交互: "查看更多"导航至完整列表页；卡片/列表项点击播放或导航至详情；更多操作调用相应API。
**可用性检查与UI反馈:** 严格遵循通用框架。搜索或各区块加载失败时，显示相应错误提示和重试。接口缺失则隐藏对应区块。

---

**模块6：驾驶模式视图 (`DrivingModeFragment.java`) （待开发）**

**核心职责：** （待开发）提供针对驾驶场景优化的音乐播放界面，强调简洁易操作、大按钮、高对比度视觉以及语音交互能力。
**布局要求 (`fragment_driving_mode.xml`):** **复用并优化现有**布局。强调大元素、高对比度。包含大封面、歌曲信息、粗大进度条、加大控制按钮。右侧可设快捷操作网格（如语音、播放列表、播放顺序、音量）。
**API交互与核心逻辑 (高级别定义)：**
*   播放信息与控制: 复用 `PlayerFragment` 的数据源和API调用。
*   语音模式: `SpeechRecognizer` API -> `GET /cloudsearch` 搜索歌曲。
*   快捷播放列表: 客户端维护或 `GET /user/playlist` 获取用户预设歌单。
*   播放顺序/音量: 客户端逻辑或系统API。
**可用性检查与UI反馈:** 核心播放功能错误处理同 `PlayerFragment`。语音搜索或快捷歌单API失败时，提供简洁提示。

---

**模块7：用户中心视图 (`UserProfileFragment.java`) （待开发）**

**核心职责：** （待开发）展示用户的个人公开信息、各项统计数据、以及可选的用户动态和听歌记录等。若为当前登录用户自己的主页，还应提供编辑个人资料的入口。
**布局要求 (`fragment_user_profile.xml`):** **复用并优化现有**布局。通常包含头部（背景、头像、昵称、签名、标签）、统计数据区、账户信息区，以及可选的用户动态/听歌排行列表 (`RecyclerView`)。
**API交互与核心逻辑 (高级别定义)：**
*   基础信息与统计: `GET /user/detail`, `GET /user/account`, `GET /user/level`, `GET /user/subcount`, `GET /user/playlist` (获取喜欢歌曲数/创建歌单数)。
*   账户绑定信息: `GET /user/binding` (谨慎处理隐私)。
*   可选扩展内容: `GET /user/event` (动态), `GET /user/record` (听歌排行), `GET /user/follows` (关注), `GET /user/followeds` (粉丝)。
*   编辑资料 (当前用户): `GET /user/update`, `POST /avatar/upload`。
*   每日签到 (可选): `GET /daily_signin`。
**可用性检查与UI反馈:** 严格遵循通用框架。核心信息加载失败则全屏错误提示；统计或扩展内容加载失败则对应区域提示。编辑操作失败用Toast/Snackbar反馈。空列表显示友好空状态。

---

**模块8：应用设置视图 (`SettingsFragment.java`) （待开发）**

**核心职责：** （待开发）提供应用的核心配置选项，允许用户自定义部分应用行为。
**布局要求:** **复用并优化现有**设置界面布局，推荐使用 Android Jetpack 的 `PreferenceFragmentCompat`，设置项定义在XML中。核心设置项包括自动播放、夜间模式、驾驶模式语音助手自动开启等。
**API交互与核心逻辑 (高级别定义)：**
*   设置项状态持久化到 `SharedPreferences`。
*   夜间模式切换调用 `AppCompatDelegate.setDefaultNightMode()` 并可能 `Activity.recreate()`。
*   其他设置项由相关模块读取 `SharedPreferences` 应用。
*   通常不直接调用网易云API。
**可用性检查与UI反馈:** 主要关注应用内部逻辑正确性。

---

**通用交互逻辑与全局性建议 (再次强调以确保覆盖)**

1.  **播放器相关的控制操作 (通常由播放服务及其ViewModel协调，UI遍布各处如PlayerFragment、列表项、通知栏等):**
    *   **收藏/取消收藏歌曲 (`GET /like`):**
        *   UI可进行乐观更新。
        *   API调用成功后确认状态；失败则回滚UI并用Toast/Snackbar提示 "收藏/取消收藏失败: [API错误信息或通用提示]"。
    *   **拖动SeekBar调整播放进度:** 用户释放SeekBar后，播放服务应执行 `seekTo(newPosition)`。
    *   **添加到歌单 (`GET /user/playlist` 获取列表, `GET /playlist/tracks?op=add` 执行添加):**
        *   操作完成后，用Toast/Snackbar提示结果 ("已成功添加到歌单 '[歌单名]'" 或 "添加到歌单失败: [API错误信息或通用提示]")。

2.  **数据同步、状态管理与UI响应：**
    *   **MVVM与响应式UI:** 严格遵循MVVM设计模式。ViewModel通过 `LiveData` 或其他响应式数据流暴露UI状态和数据。Fragment/Activity观察这些数据流，并在数据变化时自动、高效地更新UI界面，避免手动操作DOM。
    *   **数据一致性保障：**
        *   当用户执行任何会改变后端数据的写操作（例如，收藏/取消收藏歌曲、创建/编辑/删除歌单、关注/取消关注用户等），并且相应的API调用成功后，必须确保应用内所有展示相关数据的UI界面都能得到及时刷新和同步。
        *   **策略一 (重新拉取)：** 对于列表型数据，最简单直接的方式是重新调用获取该列表数据的API（例如，用户在A歌单中添加了一首歌，则歌单A的详情页应重新获取其歌曲列表）。
        *   **策略二 (本地更新与事件通知)：** 对于状态变更（如歌曲的收藏状态），或对本地缓存数据有直接影响的操作，可以在API调用成功后直接更新本地的数据模型，并通过事件总线机制 (例如，使用共享的ViewModel或特定的事件类) 通知应用内其他可能正在展示该数据的UI部分进行同步更新。这种方式可以减少不必要的网络请求，提升响应速度。
        *   例如，用户在 `PlayerFragment` 中收藏了一首当前播放的歌曲，如果此时 `MusicLibraryFragment` 中的“我喜欢的音乐”列表也处于可见状态，它应该能够感知到这个变化（例如，通过观察同一个数据源或接收一个事件），并相应地更新其列表内容或特定歌曲项的收藏状态。

3.  **全局错误处理策略与统一用户提示规范 (严格执行“通用API可用性处理框架”):**
    *   **统一的错误捕获与解析：** 在应用的API请求层（例如，Retrofit的Interceptor、Repository的基类、或ViewModel的扩展函数中）应建立一套统一的机制来捕获所有API调用过程中可能发生的异常，包括但不限于：
        *   网络连接相关的异常 (如 `IOException`)。
        *   HTTP层面错误 (如 `HttpException`，包含4xx/5xx状态码)。
        *   响应数据序列化/反序列化异常。
    *   **错误类型的精确分类与传递：** 将捕获到的原始异常或错误信息，转换为之前定义的 `ApiErrorType` 枚举及其关联数据，并向上层（ViewModel）传递。
    *   **用户提示的规范化与一致性：**
        *   **Toast/Snackbar：** 用于非阻塞性的、操作反馈性质的短暂提示（例如：“已成功收藏”、“网络连接已恢复”、“评论发表成功”、“操作执行失败，请检查网络”）。
        *   **Dialog对话框：** 用于需要用户明确确认的错误（例如，执行某项重要操作前检测到关键服务不可用，询问用户是否等待或取消），或展示包含较多信息的错误详情。
        *   **页面内嵌/占位错误视图：** 对于影响整个页面或主要列表内容加载失败的情况（例如，歌单详情页打不开、评论列表加载不出来），应在原内容区域显示设计统一的错误占位视图，清晰传达错误信息，并提供醒目的“重试”按钮或下拉刷新功能。
    *   **"接口缺失" 或 "功能暂不可用" 的处理：**
        *   严格按照“通用API可用性处理框架”中的定义：若经过判断，某个API接口被确认为持续性不可用（例如，连续多次返回404 Not Found），则依赖该接口的应用功能模块或UI元素，应在用户界面上被彻底禁用或隐藏。
        *   同时，应向用户提供一个简洁明了的解释（例如，在原功能入口处提示“此功能当前维护中，暂不可用”），避免用户困惑。
        *   最重要的是，此类“接口缺失”事件必须被详细记录并上报到后台的错误监控系统，以便开发和运维团队能够及时感知并跟进处理。
    *   **强制登录引导：** 对于所有标记为需要用户登录才能执行的操作或访问的内容，如果在执行前通过 `GET /login/status` 检测到用户未登录（例如，返回的 `profile` 为 `null`，或本地没有存储有效的用户Cookie），应立即阻止后续操作，并将用户重定向到应用的登录界面。

**总结：** 本文档旨在指导AI高效、高质量地完成音乐播放器的核心功能开发与现有代码的优化整合，同时为后续模块的迭代打下坚实基础。**请务必优先复用和改进现有UI及代码逻辑，并严格遵守API可用性处理框架。**
API 基础 URL: https://zm.armoe.cn/

参考项目: 开发过程中应主动学习和借鉴根目录下的 NeteaseCloudMusic-MVVM-master 项目（仅供学习，禁止修改该项目）。
3. 核心组件：统一播放服务 (UnifiedPlaybackService)
定位: 后台 Service (lib_audio 模块)，应用播放核心。
功能需求:
管理播放队列（增、删、排序）。
使用 ExoPlayer (优先) 控制播放（播放、暂停、停止、上/下曲、Seek）。
实现播放模式逻辑（顺序、列表循环、单曲循环、随机）。
通过 LiveData 广播播放状态（当前歌曲、进度、状态、模式、错误等）。
处理音频焦点。
响应耳机插拔、蓝牙断开等系统事件。
管理媒体通知栏（需处理 FOREGROUND_SERVICE 和 POST_NOTIFICATIONS 权限）。
交互: UI 组件通过 ViewModel 与服务通信。提供 Binder 接口供 ViewModel 绑定、发送命令、注册监听。ViewModel 负责在合适的生命周期绑定/解绑服务。

主页面控制面板 : 水平排列的 ImageButton (播放列表 button_player_playlist, 收藏 button_player_collect, 上一首 button_player_prev, 播放/暂停 button_player_play_pause (稍大), 下一首 button_player_next, 播放模式 button_player_play_mode)。
进度与时间 (控制面板下方或上方): SeekBar (id: seekbar_player_progress), TextView (id: textview_player_current_time), TextView (id: textview_player_total_time)。
UI 设计:
播放/暂停按钮、收藏按钮、播放模式按钮的图标需根据状态动态切换。
歌词需支持当前行高亮，并随进度自动滚动。
交互逻辑:
通过 ViewModel 观察播放服务 LiveData 更新 UI。
控制按钮点击通过 ViewModel 调用服务方法。
SeekBar 拖动更新时间，释放后调用 seekTo()。
点击播放列表按钮弹出播放队列视图 (如 BottomSheetDialog)。
5.2. 我的音乐库视图 (MusicLibraryFragment)
功能需求: 浏览和管理本地音乐及云端收藏（歌曲、专辑、歌手、歌单）。提供库内搜索。
布局要求 (fragment_music_library.xml):
顶部: SearchView (id: searchview_library_search)。
中部: TabLayout (id: tablayout_library_categories) + ViewPager2 (id: viewpager_library_content)。Tabs: "本地音乐", "歌曲", "专辑", "歌手", "歌单"。
下部 (ViewPager2 页面): 每个 Tab 对应一个列表 Fragment (如 LocalMusicListFragment)，核心为 RecyclerView。
列表项布局 (item_song_library.xml, item_album_library.xml 等): ImageView (缩略图), TextView (主标题), TextView (副标题), ImageButton (id: button_item_more_actions, 更多操作图标)。
交互逻辑:
SearchView 进行本地筛选或触发 API 搜索。
TabLayout 点击或 ViewPager2 滑动切换页面，加载对应数据（本地扫描或 API 请求）。
RecyclerView 支持滚动、下拉刷新/上拉加载（云端数据）。
列表项点击播放内容（通过 ViewModel -> 服务）。
点击"更多操作"按钮弹出 BottomSheetDialog 或 Dialog，显示上下文菜单。
API 依赖:
云端数据列表（歌曲、专辑、歌手、歌单）获取。
库内搜索（若 API 支持）。
"更多操作"中的大部分功能（添加到云歌单、取消收藏、查看详情、关注/取消关注、删除歌单、收藏/取消收藏歌单等）。
处理: 检查 api.txt，若接口缺失，对应功能/操作不显示或置灰，并报告。
5.3. 音乐探索视图 (DiscoveryFragment)
功能需求: 提供在线音乐发现渠道（推荐、榜单、新歌、专辑等）和在线搜索。
布局要求 (fragment_discovery.xml):
顶部: SearchView (id: searchview_discovery_online_search)。
内容区: NestedScrollView + LinearLayout 或 RecyclerView (多 ViewType)。
分区结构: 头部 (标题 textview_section_title_xxx + "查看更多" button_section_see_more_xxx) + 内容 (横向 RecyclerView，网格或列表)。
卡片布局 (item_discovery_card_large.xml): ImageView (封面), TextView (标题), TextView (描述)。
列表项布局 (item_discovery_song_small.xml): ImageView (小封面), TextView (歌名), TextView (歌手)。
交互逻辑:
SearchView 提交后触发 API 搜索。
点击"查看更多"导航到完整列表页 (需 API 支持分页)。
点击卡片/列表项播放内容或进入详情页。
"更多操作"按钮弹出菜单（收藏、添加到队列、添加到歌单等）。
API 依赖: 所有内容获取（推荐、榜单、新歌、专辑、搜索结果）及大部分交互操作。若接口缺失，对应分区不显示或提示错误，并报告。
5.4. 驾驶模式视图 (DrivingModeFragment)
功能需求: 提供驾驶场景优化的播放界面，强调简洁、大按钮、高对比度和语音交互。
布局要求 (fragment_driving_mode.xml):
视觉: 深色背景，高对比度元素，字体和图标显著增大，间距拉开。
左侧 (约 60-70%): 大 ImageView (id: imageview_driving_album_art), 大 TextView (id: textview_driving_song_title, textview_driving_artist_name), 粗 SeekBar (id: seekbar_driving_progress), 大 ImageButton (上一首 button_driving_prev, 播放/暂停 button_driving_play_pause (最大), 下一首 button_driving_next)。
右侧 (约 30-40%): GridLayout (id: gridlayout_driving_quick_actions, 如 2x2)。每个网格项含大 ImageButton + TextView标签 (语音模式 button_driving_voice_mode, 播放列表 button_driving_playlist, 播放顺序 button_driving_play_order, 音量 button_driving_volume)。
交互逻辑:
onResume 时检查"自动开启语音"设置，若开启则自动激活语音。
播放控制按钮通过 ViewModel 调用服务。
UI 通过 ViewModel 同步播放状态。
快捷按钮触发相应功能（手动语音、简易列表、切换模式、音量调节）。
5.6. 应用设置视图 (SettingsFragment)
功能需求: 提供应用核心配置选项。
布局要求:
推荐使用 PreferenceFragmentCompat 并定义在 res/xml/settings_preferences.xml。
必须仅包含三项:
自动播放 (SwitchPreferenceCompat, key: setting_auto_play)
夜间模式 (SwitchPreferenceCompat, key: setting_night_mode)
驾驶模式自动语音 (SwitchPreferenceCompat, key: setting_auto_voice_in_driving)
交互逻辑:
开关状态改变时，保存值到 SharedPreferences。
夜间模式切换需调用 AppCompatDelegate.setDefaultNightMode() 并可能 recreate() Activity。
6. 性能与健壮性
内存管理: 关注内存泄漏，及时释放资源。
错误处理: 实现统一错误处理机制，对网络、API 等错误提供友好、明确的用户提示。