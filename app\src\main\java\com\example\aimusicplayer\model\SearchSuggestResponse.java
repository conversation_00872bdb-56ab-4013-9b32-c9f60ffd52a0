package com.example.aimusicplayer.model;

import com.example.aimusicplayer.api.ApiResponse;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 搜索建议响应
 * 使用MVVM架构
 */
public class SearchSuggestResponse extends ApiResponse<SearchSuggestResponse.Data> {

    public static class Data {
        @SerializedName("result")
        private Result result;

        public Result getResult() {
            return result;
        }

        public void setResult(Result result) {
            this.result = result;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "result=" + result +
                    '}';
        }
    }

    public static class Result {
        @SerializedName("songs")
        private List<SuggestItem> songs;

        @SerializedName("artists")
        private List<SuggestItem> artists;

        @SerializedName("albums")
        private List<SuggestItem> albums;

        @SerializedName("playlists")
        private List<SuggestItem> playlists;

        public List<SuggestItem> getSongs() {
            return songs;
        }

        public void setSongs(List<SuggestItem> songs) {
            this.songs = songs;
        }

        public List<SuggestItem> getArtists() {
            return artists;
        }

        public void setArtists(List<SuggestItem> artists) {
            this.artists = artists;
        }

        public List<SuggestItem> getAlbums() {
            return albums;
        }

        public void setAlbums(List<SuggestItem> albums) {
            this.albums = albums;
        }

        public List<SuggestItem> getPlaylists() {
            return playlists;
        }

        public void setPlaylists(List<SuggestItem> playlists) {
            this.playlists = playlists;
        }

        @Override
        public String toString() {
            return "Result{" +
                    "songs=" + songs +
                    ", artists=" + artists +
                    ", albums=" + albums +
                    ", playlists=" + playlists +
                    '}';
        }
    }

    public static class SuggestItem {
        @SerializedName("id")
        private String id;

        @SerializedName("name")
        private String name;

        @SerializedName("artists")
        private List<Artist> artists;

        // 可能的专辑信息
        @SerializedName("alias")
        private List<String> alias;

        @SerializedName("transNames")
        private List<String> translatedNames;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Artist> getArtists() {
            return artists;
        }

        public void setArtists(List<Artist> artists) {
            this.artists = artists;
        }

        public List<String> getAlias() {
            return alias;
        }

        public void setAlias(List<String> alias) {
            this.alias = alias;
        }

        public List<String> getTranslatedNames() {
            return translatedNames;
        }

        public void setTranslatedNames(List<String> translatedNames) {
            this.translatedNames = translatedNames;
        }

        // 获取完整的显示名称，包含别名
        public String getDisplayName() {
            StringBuilder sb = new StringBuilder(name);

            if (alias != null && !alias.isEmpty()) {
                sb.append(" (");
                for (int i = 0; i < alias.size(); i++) {
                    if (i > 0) {
                        sb.append("/");
                    }
                    sb.append(alias.get(i));
                }
                sb.append(")");
            }

            return sb.toString();
        }

        // 获取艺术家显示名称
        public String getArtistDisplay() {
            if (artists == null || artists.isEmpty()) {
                return "";
            }

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < artists.size(); i++) {
                if (i > 0) {
                    sb.append("/");
                }
                sb.append(artists.get(i).getName());
            }

            return sb.toString();
        }

        @Override
        public String toString() {
            return "SuggestItem{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", artists=" + artists +
                    ", alias=" + alias +
                    ", translatedNames=" + translatedNames +
                    '}';
        }
    }

    public static class Artist {
        @SerializedName("id")
        private String id;

        @SerializedName("name")
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "Artist{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    '}';
        }
    }
}