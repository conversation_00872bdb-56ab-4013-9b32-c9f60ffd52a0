package com.example.aimusicplayer.data.repository

import android.util.Log
import com.example.aimusicplayer.data.cache.ApiCacheManager
import com.example.aimusicplayer.utils.NetworkResult
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import retrofit2.Response
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * 所有Repository的基类
 * 提供通用的数据访问方法和缓存机制
 */
abstract class BaseRepository {

    // 注入ApiCacheManager
    @Inject
    lateinit var apiCacheManager: ApiCacheManager

    companion object {
        private const val TAG = "BaseRepository"

        // 缓存过期时间（默认5分钟）
        val CACHE_EXPIRATION_TIME = TimeUnit.MINUTES.toMillis(5)
    }

    /**
     * 安全地执行API调用并返回Flow
     * @param apiCall 挂起函数，执行API调用
     * @return Flow<NetworkResult<T>>
     */
    protected fun <T> safeApiCall(apiCall: suspend () -> T): Flow<NetworkResult<T>> = flow {
        emit(NetworkResult.Loading())
        val response = apiCall()
        emit(NetworkResult.Success(response))
    }.catch { e ->
        if (e is CancellationException) throw e
        Log.e("BaseRepository", "API调用失败", e)
        emit(NetworkResult.Error(e.message ?: "未知错误"))
    }.flowOn(Dispatchers.IO)

    /**
     * 执行API调用并返回Flow
     * 简化版的safeApiCall
     */
    protected fun <T> apiFlow(apiCall: suspend () -> T): Flow<NetworkResult<T>> = safeApiCall(apiCall)

    /**
     * 安全地执行Retrofit响应并返回Flow
     * @param apiCall 挂起函数，执行Retrofit调用
     * @return Flow<NetworkResult<T>>
     */
    protected fun <T> safeApiResponse(apiCall: suspend () -> Response<T>): Flow<NetworkResult<T>> = flow {
        emit(NetworkResult.Loading())
        val response = apiCall()
        if (response.isSuccessful) {
            val body = response.body()
            if (body != null) {
                emit(NetworkResult.Success(body))
            } else {
                emit(NetworkResult.Error("响应成功但数据为空"))
            }
        } else {
            emit(NetworkResult.Error("请求失败: ${response.code()} ${response.message()}"))
        }
    }.catch { e ->
        if (e is CancellationException) throw e
        Log.e("BaseRepository", "API调用失败", e)
        emit(NetworkResult.Error(e.message ?: "未知错误"))
    }.flowOn(Dispatchers.IO)

    /**
     * 带缓存的API调用（使用ApiCacheManager）
     * @param cacheKey 缓存键
     * @param forceRefresh 是否强制刷新
     * @param cacheExpiration 缓存过期时间
     * @param cacheType 缓存类型
     * @param apiCall 挂起函数，执行API调用
     * @return Flow<NetworkResult<T>>
     */
    protected inline fun <reified T> cachedApiCall(
        cacheKey: String,
        forceRefresh: Boolean = false,
        cacheExpiration: Long = CACHE_EXPIRATION_TIME,
        cacheType: String = ApiCacheManager.CACHE_TYPE_DEFAULT,
        crossinline apiCall: suspend () -> T
    ): Flow<NetworkResult<T>> = flow {
        emit(NetworkResult.Loading())

        // 检查是否使用内存缓存（向后兼容）
        val useMemoryCache = !::apiCacheManager.isInitialized

        if (useMemoryCache) {
            // 使用内存缓存（旧方式）
            val cachedData = getFromCache<T>(cacheKey)
            val currentTime = System.currentTimeMillis()

            if (!forceRefresh && cachedData != null && (currentTime - cachedData.second < cacheExpiration)) {
                // 使用缓存数据
                emit(NetworkResult.Success(cachedData.first))
            } else {
                // 从网络获取数据
                val response = apiCall()

                // 更新缓存
                putInCache(cacheKey, response)

                emit(NetworkResult.Success(response))
            }
        } else {
            // 使用ApiCacheManager（新方式）
            if (!forceRefresh) {
                // 尝试从缓存获取
                val cachedData = apiCacheManager.getCache(cacheKey, T::class.java)
                if (cachedData != null) {
                    // 使用缓存数据
                    emit(NetworkResult.Success(cachedData))
                    return@flow
                }
            }

            // 从网络获取数据
            val response = apiCall()

            // 更新缓存
            apiCacheManager.saveCache(cacheKey, response, cacheExpiration, cacheType)

            emit(NetworkResult.Success(response))
        }
    }.catch { e ->
        if (e is CancellationException) throw e
        Log.e("BaseRepository", "API调用失败", e)
        emit(NetworkResult.Error(e.message ?: "未知错误"))
    }.flowOn(Dispatchers.IO)

    // 内存缓存
    private val memoryCache = ConcurrentHashMap<String, Pair<Any, Long>>()

    /**
     * 从缓存获取数据
     * @param key 缓存键
     * @return 缓存数据和时间戳
     */
    @Suppress("UNCHECKED_CAST")
    protected fun <T> getFromCache(key: String): Pair<T, Long>? {
        return memoryCache[key] as? Pair<T, Long>
    }

    /**
     * 将数据放入缓存
     * @param key 缓存键
     * @param value 缓存数据
     */
    protected fun <T> putInCache(key: String, value: T) {
        memoryCache[key] = Pair(value as Any, System.currentTimeMillis())
    }

    /**
     * 清理过期缓存
     * @param expiration 过期时间
     */
    protected fun clearExpiredCache(expiration: Long = CACHE_EXPIRATION_TIME) {
        val currentTime = System.currentTimeMillis()
        memoryCache.entries.removeIf { (currentTime - it.value.second) > expiration }
    }

    /**
     * 清理所有缓存
     */
    protected fun clearAllCache() {
        memoryCache.clear()
    }

    /**
     * 清理特定键的缓存
     * @param key 缓存键
     */
    protected fun clearCache(key: String) {
        memoryCache.remove(key)
    }
}
