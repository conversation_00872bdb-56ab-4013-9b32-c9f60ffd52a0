package com.example.aimusicplayer.di

import android.content.Context
import com.example.aimusicplayer.error.GlobalErrorHandler
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 错误处理模块
 * 提供错误处理相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object ErrorHandlingModule {

    /**
     * 提供全局错误处理器
     */
    @Provides
    @Singleton
    fun provideGlobalErrorHandler(@ApplicationContext context: Context): GlobalErrorHandler {
        return GlobalErrorHandler(context)
    }
}
