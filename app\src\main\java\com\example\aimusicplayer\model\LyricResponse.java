package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

public class LyricResponse {
    private int code;
    private boolean sgc;
    private boolean sfy;
    private boolean qfy;
    private Lrc lrc;
    private Lrc klyric;
    private Lrc tlyric;
    private Lrc romalrc;
    private boolean nolyric;  // 是否无歌词
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public Lrc getLrc() {
        return lrc;
    }
    
    public void setLrc(Lrc lrc) {
        this.lrc = lrc;
    }
    
    public Lrc getTranslatedLyric() {
        return tlyric;
    }
    
    public Lrc getRomanizedLyric() {
        return romalrc;
    }
    
    public Lrc getTlyric() {
        return tlyric;
    }
    
    public Lrc getKlyric() {
        return klyric;
    }
    
    public boolean isNolyric() {
        return nolyric;
    }
    
    public static class Lrc {
        private int version;
        private String lyric;
        
        public int getVersion() {
            return version;
        }
        
        public String getLyric() {
            return lyric;
        }
        
        public void setLyric(String lyric) {
            this.lyric = lyric;
        }
    }
} 