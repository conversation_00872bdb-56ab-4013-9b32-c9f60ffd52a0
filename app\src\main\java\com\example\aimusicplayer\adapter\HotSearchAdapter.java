package com.example.aimusicplayer.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.model.HotSearchResponse;

import java.util.List;

public class HotSearchAdapter extends RecyclerView.Adapter<HotSearchAdapter.ViewHolder> {

    private List<HotSearchResponse.HotSearch> hotSearches;
    private OnHotSearchClickListener listener;

    public interface OnHotSearchClickListener {
        void onHotSearchClick(String searchWord);
    }

    public HotSearchAdapter(List<HotSearchResponse.HotSearch> hotSearches, OnHotSearchClickListener listener) {
        this.hotSearches = hotSearches;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_hot_search, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        HotSearchResponse.HotSearch hotSearch = hotSearches.get(position);
        
        // 设置排名，前三名使用特殊颜色
        holder.rankTextView.setText(String.valueOf(position + 1));
        if (position < 3) {
            holder.rankTextView.setBackgroundResource(R.drawable.circle_background_top3);
        } else {
            holder.rankTextView.setBackgroundResource(R.drawable.circle_background);
        }
        
        holder.searchWordTextView.setText(hotSearch.getSearchWord());
        holder.scoreTextView.setText(hotSearch.getScore());
        
        // 点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onHotSearchClick(hotSearch.getSearchWord());
            }
        });
    }

    @Override
    public int getItemCount() {
        return hotSearches == null ? 0 : hotSearches.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView rankTextView;
        TextView searchWordTextView;
        TextView scoreTextView;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            rankTextView = itemView.findViewById(R.id.rankTextView);
            searchWordTextView = itemView.findViewById(R.id.searchWordTextView);
            scoreTextView = itemView.findViewById(R.id.scoreTextView);
        }
    }
} 