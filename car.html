<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轻聆 - 智能车载音乐播放器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
        }
        
        .prototype {
            width: 800px;
            height: 450px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            background-color: #000;
            position: relative;
            color: white;
        }
        
        .prototype-title {
            text-align: center;
            padding: 10px;
            background-color: #2c3e50;
            color: white;
            font-weight: bold;
        }
        
        /* 欢迎界面 */
        .welcome-screen {
            height: 100%;
            background: linear-gradient(135deg, #1a1a1a, #333);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .welcome-content {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }
        
        .logo {
            font-size: 100px;
            margin-right: 30px;
            color: #3498db;
        }
        
        .welcome-text {
            text-align: left;
        }
        
        .app-name {
            font-size: 50px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .slogan {
            font-size: 22px;
            color: #999;
        }
        
        /* 主界面 */
        .main-container {
            display: flex;
            height: 100%;
        }
        
        .sidebar {
            width: 80px;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }
        
        .sidebar-icon {
            font-size: 30px;
            color: #888;
            margin-bottom: 30px;
            cursor: pointer;
        }
        
        .sidebar-icon.active {
            color: #3498db;
        }
        
        .main-content {
            flex: 1;
            display: flex;
        }
        
        .main-content.user-center-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #111;
            padding: 0;
            margin: 0;
            overflow: hidden;
            position: relative;
            height: 100%;
        }
        
        .album-container {
            width: 40%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #1a1a1a;
            padding: 20px;
        }
        
        .album-art {
            width: 200px;
            height: 200px;
            background-image: url('https://source.unsplash.com/random/200x200/?album');
            background-size: cover;
            background-position: center;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .player-container {
            width: 60%;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .song-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .artist {
            font-size: 20px;
            color: #999;
            margin-bottom: 30px;
        }
        
        .progress-container {
            width: 100%;
            height: 6px;
            background-color: #333;
            border-radius: 3px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .progress-bar {
            width: 30%;
            height: 100%;
            background-color: #3498db;
            border-radius: 3px;
        }
        
        .time-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #999;
            margin-bottom: 40px;
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .control-btn {
            font-size: 36px;
            color: #ccc;
            cursor: pointer;
        }
        
        .play-btn {
            font-size: 50px;
            color: #3498db;
            cursor: pointer;
        }
        
        /* 歌曲列表界面 */
        .music-library {
            display: flex;
            height: 100%;
            background-color: #111;
        }
        
        .library-sidebar {
            width: 80px;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }
        
        .library-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .library-header {
            height: 60px;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #333;
        }
        
        .library-title {
            font-size: 22px;
            font-weight: bold;
            margin-right: auto;
        }
        
        .search-container {
            display: flex;
            align-items: center;
            background-color: #333;
            border-radius: 20px;
            padding: 5px 15px;
            width: 300px;
        }
        
        .search-icon {
            color: #999;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .search-input {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            width: 100%;
            outline: none;
        }
        
        .category-tabs {
            display: flex;
            background-color: #1a1a1a;
            border-bottom: 1px solid #333;
        }
        
        .tab {
            padding: 15px 25px;
            text-align: center;
            font-size: 16px;
            color: #999;
            cursor: pointer;
        }
        
        .tab.active {
            color: #3498db;
            border-bottom: 2px solid #3498db;
        }
        
        .song-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 10px;
        }
        
        .song-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #333;
            cursor: pointer;
        }
        
        .song-item:hover {
            background-color: #222;
        }
        
        .song-item-thumbnail {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            background-color: #333;
            background-image: url('https://source.unsplash.com/random/50x50/?music');
            background-size: cover;
            margin-right: 15px;
        }
        
        .song-item-info {
            flex: 1;
        }
        
        .song-item-title {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .song-item-artist {
            font-size: 14px;
            color: #999;
        }
        
        .song-item-action {
            font-size: 20px;
            color: #999;
            margin-left: 10px;
        }
        
        /* 在线音乐界面 */
        .online-music {
            display: flex;
            height: 100%;
            background-color: #111;
        }
        
        .online-sidebar {
            width: 80px;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }
        
        .online-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .online-header {
            padding: 20px 20px 10px 20px;
            background-color: #1a1a1a;
        }
        
        .music-content-container {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px 20px;
        }
        
        .music-section {
            margin-bottom: 30px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: white;
        }
        
        .section-more {
            font-size: 14px;
            color: #999;
            cursor: pointer;
        }
        
        .section-more:hover {
            color: #3498db;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }
        
        .music-card {
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .music-card:hover {
            transform: translateY(-5px);
        }
        
        .music-card-cover {
            width: 100%;
            padding-bottom: 100%;
            border-radius: 8px;
            background-color: #333;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .music-card-cover img {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .music-card-title {
            font-size: 14px;
            line-height: 1.4;
            height: 40px;
            overflow: hidden;
            color: #ddd;
            margin-bottom: 5px;
        }
        
        .music-card-desc {
            font-size: 12px;
            color: #999;
        }
        
        .top-songs {
            margin-top: 10px;
        }
        
        .top-song-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            cursor: pointer;
        }
        
        .top-song-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .top-song-rank {
            width: 30px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            color: #3498db;
        }
        
        .top-song-rank.top1 {
            color: #f39c12;
        }
        
        .top-song-rank.top2 {
            color: #bdc3c7;
        }
        
        .top-song-rank.top3 {
            color: #cd7f32;
        }
        
        .top-song-info {
            flex: 1;
            margin-left: 10px;
        }
        
        .top-song-name {
            font-size: 14px;
            font-weight: bold;
            color: white;
            margin-bottom: 3px;
        }
        
        .top-song-artist {
            font-size: 12px;
            color: #999;
        }
        
        .new-songs-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        /* 设置界面 */
        .settings-container {
            display: flex;
            height: 100%;
            background-color: #111;
        }
        
        .settings-sidebar {
            width: 80px;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }
        
        .settings-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .settings-header {
            height: 60px;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #333;
        }
        
        .settings-title {
            font-size: 22px;
            font-weight: bold;
        }
        
        .settings-body {
            display: flex;
            flex: 1;
        }
        
        .settings-categories {
            width: 200px;
            background-color: #1a1a1a;
            padding: 20px 0;
        }
        
        .settings-category {
            padding: 12px 20px;
            font-size: 16px;
            color: #999;
            cursor: pointer;
        }
        
        .settings-category.active {
            color: white;
            background-color: #222;
            border-left: 3px solid #3498db;
        }
        
        .settings-options {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .settings-group {
            margin-bottom: 30px;
        }
        
        .settings-group-title {
            padding-bottom: 10px;
            font-size: 18px;
            color: #3498db;
            border-bottom: 1px solid #333;
            margin-bottom: 15px;
        }
        
        .settings-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #222;
        }
        
        .settings-item-title {
            font-size: 16px;
        }
        
        .settings-item-desc {
            font-size: 14px;
            color: #999;
            margin-top: 5px;
        }
        
        .settings-item-value {
            color: #999;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .chevron-icon {
            margin-left: 5px;
        }
        
        .switch {
            width: 50px;
            height: 26px;
            background-color: #333;
            border-radius: 13px;
            position: relative;
            cursor: pointer;
        }
        
        .switch.on {
            background-color: #3498db;
        }
        
        .switch::after {
            content: "";
            position: absolute;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
        }
        
        .switch.on::after {
            left: 26px;
        }
        
        /* 驾驶模式界面 */
        .driving-container {
            height: 100%;
            background-color: #000;
            color: white;
            display: flex;
            padding: 0;
        }
        
        .driving-content {
            flex: 1;
            display: flex;
            padding: 20px;
        }
        
        .driving-main {
            flex: 2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px 0;
        }
        
        .driving-time {
            font-size: 70px;
            font-weight: 300;
            margin-bottom: 5px;
        }
        
        .driving-date {
            font-size: 20px;
            color: #999;
            margin-bottom: 20px;
        }
        
        .driving-player {
            width: 100%;
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .driving-album {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            margin-right: 25px;
            background-color: #333;
            background-image: url('https://source.unsplash.com/random/100x100/?music');
            background-size: cover;
        }
        
        .driving-info {
            flex: 1;
            margin-right: 20px;
        }
        
        .driving-song-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .driving-artist {
            font-size: 18px;
            color: #999;
        }
        
        .driving-controls {
            display: flex;
            align-items: center;
        }
        
        .driving-btn {
            font-size: 48px;
            margin: 0 15px;
            color: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .driving-btn:hover {
            transform: scale(1.1);
            color: #3498db;
        }
        
        .driving-progress {
            width: 100%;
            height: 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            cursor: pointer;
        }
        
        .driving-progress-bar {
            height: 100%;
            width: 30%;
            background-color: #3498db;
            border-radius: 5px;
        }
        
        .driving-time-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-size: 16px;
            color: #999;
            margin-bottom: 15px;
        }
        
        .driving-sidebar {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 40px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-action:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.03);
        }
        
        .quick-action:active {
            transform: scale(0.98);
        }
        
        .quick-action-icon {
            font-size: 42px;
            margin-bottom: 12px;
            color: #3498db;
        }
        
        .quick-action-name {
            font-size: 16px;
            color: white;
            white-space: nowrap;
            text-align: center;
        }

        /* 驾驶模式播放控制器 */
        .driving-controller {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .driving-control-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            margin-bottom: 20px;
        }
        
        /* 歌词界面 */
        .lyrics-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: #1a1a1a;
        }
        
        .lyrics-header {
            margin-bottom: 20px;
        }
        
        .lyrics-title {
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .lyrics-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
        }
        
        .lyrics-line {
            font-size: 16px;
            color: #999;
            padding: 12px 0;
            transition: all 0.3s;
            text-align: center;
        }
        
        .lyrics-line.prev {
            color: #666;
            font-size: 18px;
        }
        
        .lyrics-line.active {
            color: #3498db;
            font-size: 24px;
            font-weight: bold;
        }
        
        .lyrics-line.next {
            color: #777;
            font-size: 18px;
        }
        
        .player-mini {
            width: 100%;
            padding: 15px;
            background-color: rgba(0,0,0,0.3);
            border-radius: 10px;
            margin-top: 20px;
        }
        
        /* 播放列表详情界面 */
        .playlist-content {
            display: flex;
            flex-direction: column;
            background-color: #111;
        }
        
        .playlist-header {
            padding: 20px;
            background-color: #1a1a1a;
        }
        
        .playlist-info {
            display: flex;
            align-items: center;
        }
        
        .playlist-cover {
            width: 120px;
            height: 120px;
            border-radius: 10px;
            overflow: hidden;
            margin-right: 20px;
        }
        
        .playlist-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .playlist-details {
            flex: 1;
        }
        
        .playlist-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: white;
        }
        
        .playlist-stats {
            font-size: 14px;
            color: #999;
            margin-bottom: 20px;
        }
        
        .playlist-actions {
            display: flex;
            gap: 15px;
        }
        
        .action-btn {
            padding: 10px 20px;
            border-radius: 25px;
            border: none;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn.primary {
            background-color: #3498db;
        }
        
        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .action-btn.primary:hover {
            background-color: #2980b9;
        }
        
        .playlist-tracks {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .track-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px;
        }
        
        .track {
            display: flex;
            align-items: center;
            padding: 15px 10px;
            border-bottom: 1px solid #222;
            cursor: pointer;
        }
        
        .track:hover {
            background-color: #1a1a1a;
        }
        
        .track.active {
            background-color: #1e3a5a;
        }
        
        .track-number {
            width: 40px;
            color: #999;
            font-size: 16px;
        }
        
        .track-info {
            flex: 1;
            margin-right: 20px;
        }
        
        .track-name {
            font-size: 16px;
            color: #fff;
            margin-bottom: 5px;
        }
        
        .track-artist {
            font-size: 14px;
            color: #999;
        }
        
        .track-duration {
            width: 60px;
            text-align: right;
            color: #999;
            font-size: 14px;
        }
        
        .track-actions {
            width: 40px;
            text-align: center;
            color: #999;
            font-size: 16px;
        }
        
        .now-playing-bar {
            height: 70px;
            background-color: rgba(0,0,0,0.8);
            border-top: 1px solid #333;
            padding: 0 20px;
            display: flex;
            align-items: center;
        }
        
        .mini-player {
            display: flex;
            align-items: center;
            width: 100%;
        }
        
        .mini-album-art {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            background-color: #333;
            background-image: url('https://source.unsplash.com/random/50x50/?music');
            background-size: cover;
            margin-right: 15px;
        }
        
        .mini-song-info {
            flex: 1;
        }
        
        .mini-song-title {
            font-size: 16px;
            color: white;
            margin-bottom: 3px;
        }
        
        .mini-artist {
            font-size: 14px;
            color: #999;
        }
        
        .mini-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .mini-control-btn {
            font-size: 24px;
            color: #ccc;
            cursor: pointer;
        }
        
        .mini-play-btn {
            font-size: 36px;
            color: #3498db;
            cursor: pointer;
        }

        .player-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 20px;
        }

        .song-info {
            text-align: center;
            margin-top: 15px;
        }
        
        /* 新增美化控制按钮样式 */
        .player-control-panel {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 50px;
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 30px auto;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        .player-control-panel .control-btn {
            color: rgba(255, 255, 255, 0.8);
            font-size: 22px;
            margin: 0 18px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .player-control-panel .control-btn:hover {
            color: #3498db;
            transform: scale(1.1);
        }
        
        .player-control-panel .play-btn {
            font-size: 50px;
            color: #3498db;
            margin: 0 25px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .player-control-panel .play-btn:hover {
            transform: scale(1.08);
            color: #2980b9;
        }
        
        .player-control-panel .side-btn {
            color: rgba(255, 255, 255, 0.7);
            font-size: 20px;
            margin: 0 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .player-control-panel .side-btn:hover {
            color: #3498db;
            transform: scale(1.1);
        }

        /* 登录界面样式 */
        .login-screen {
            height: 100%;
            background: linear-gradient(135deg, #1a1a1a, #333);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .login-container {
            width: 500px;
            background-color: transparent;
            border-radius: 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: -40px;
        }
        
        .login-logo {
            display: none; /* 隐藏音乐图标 */
        }
        
        .login-title {
            font-size: 32px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-buttons {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 350px;
            gap: 15px;
        }
        
        .quick-login-button {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(52, 152, 219, 0.2);
            border: 2px solid #3498db;
            border-radius: 15px;
            padding: 16px;
            font-size: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-login-button:hover {
            background-color: rgba(52, 152, 219, 0.4);
        }
        
        .quick-login-button i {
            margin-right: 15px;
            font-size: 26px;
        }
        
        .login-option {
            text-align: center;
            margin: 20px 0;
            font-size: 16px;
            color: #bbb;
        }
        
        .login-option span {
            color: #3498db;
            cursor: pointer;
            font-weight: bold;
        }
        
        .guest-login {
            color: #999;
            font-size: 16px;
            cursor: pointer;
            margin-top: 15px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 12px 20px;
            width: 100%;
            max-width: 350px;
            text-align: center;
            transition: all 0.2s;
        }
        
        .guest-login:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: #bbb;
        }

        /* 用户中心样式 */
        .user-center-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #111;
            padding: 0;
            margin: 0;
            overflow: hidden;
            position: relative;
            height: 100%;
        }
        
        .user-header {
            position: relative;
            height: 140px;
            margin: 0;
            padding: 0;
            top: 0;
        }
        
        .user-cover-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.7)), url('https://via.placeholder.com/800x200');
            background-size: cover;
            background-position: center;
        }
        
        .user-profile-large {
            position: absolute;
            bottom: -50px;
            left: 40px;
            display: flex;
            align-items: flex-end;
        }
        
        .user-avatar-large {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #3498db;
            margin-right: 20px;
            background-color: #222;
        }
        
        .user-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-info-large {
            margin-bottom: 15px;
        }
        
        .user-name-large {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: white;
        }
        
        .user-tags {
            display: flex;
            margin-bottom: 10px;
        }
        
        .user-tag {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
            margin-right: 10px;
            display: flex;
            align-items: center;
        }
        
        .vip-tag {
            background-color: rgba(241, 196, 15, 0.2);
            color: #f1c40f;
        }
        
        .vip-tag i {
            margin-right: 5px;
        }
        
        .level-tag {
            background-color: rgba(52, 152, 219, 0.2);
            color: #3498db;
        }
        
        .user-signature {
            font-size: 14px;
            color: #bbb;
        }
        
        .edit-profile-btn-large {
            display: none;
        }
        
        .user-stats {
            display: flex;
            background-color: rgba(255, 255, 255, 0.05);
            margin-top: 60px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }
        
        .stat-item {
            flex: 1;
            text-align: center;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 5px 0;
        }
        
        .stat-item:last-child {
            border-right: none;
        }
        
        .stat-value {
            font-size: 22px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #999;
        }
        
        .user-sections {
            flex: 1;
            padding: 0 20px;
            overflow-y: auto;
        }
        
        .user-section {
            margin-bottom: 30px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }
        
        .section-more {
            font-size: 14px;
            color: #999;
            cursor: pointer;
        }
        
        .account-info-list {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .account-info-item {
            display: flex;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .account-info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            width: 80px;
            font-size: 14px;
            color: #999;
        }
        
        .info-value {
            flex: 1;
            font-size: 14px;
            color: white;
        }
        
        .info-value.highlight {
            color: #f1c40f;
        }

        .side-btn.play-mode {
            position: relative;
        }
        
        .play-mode-tooltip {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 4px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .side-btn.play-mode:hover .play-mode-tooltip {
            opacity: 1;
        }
        
        .card-row {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .rank-card {
            flex: 0 0 200px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .rank-card:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
        }
        
        .rank-card-title {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        }
        
        .rank-list {
            margin-bottom: 8px;
        }
        
        .rank-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .rank-item-number {
            width: 18px;
            font-size: 14px;
            color: #999;
            margin-right: 8px;
        }
        
        .rank-item-name {
            font-size: 14px;
            color: #ddd;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 新歌速递样式 */
        .new-songs-section {
            margin-bottom: 30px;
        }
        
        .new-song-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #333;
        }
        
        .new-song-tab {
            padding: 10px 20px;
            font-size: 14px;
            color: #999;
            cursor: pointer;
            position: relative;
        }
        
        .new-song-tab.active {
            color: #3498db;
        }
        
        .new-song-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #3498db;
        }
        
        .new-songs-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .new-song-card {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .new-song-card:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
        }
        
        .new-song-info {
            display: flex;
            margin-bottom: 8px;
        }
        
        .new-song-cover {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            margin-right: 10px;
            overflow: hidden;
        }
        
        .new-song-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .new-song-detail {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .new-song-name {
            font-size: 14px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .new-song-artist {
            font-size: 12px;
            color: #999;
        }
        
        .new-song-tag {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            color: white;
            background-color: #3498db;
            display: inline-block;
            margin-right: 5px;
        }
        
        /* 排行榜样式增强 */
        .charts-section {
            margin-bottom: 30px;
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        .chart-card {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .chart-card:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            color: white;
        }
        
        .chart-more {
            font-size: 12px;
            color: #999;
        }
        
        .chart-cover-container {
            position: relative;
            margin-bottom: 15px;
        }
        
        .chart-covers {
            display: flex;
            position: relative;
            height: 120px;
        }
        
        .chart-cover {
            width: 80px;
            height: 80px;
            border-radius: 6px;
            position: absolute;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .chart-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .chart-cover:nth-child(1) {
            z-index: 3;
            transform: translateX(0) rotate(-5deg);
        }
        
        .chart-cover:nth-child(2) {
            z-index: 2;
            transform: translateX(40px) translateY(10px) rotate(0deg);
        }
        
        .chart-cover:nth-child(3) {
            z-index: 1;
            transform: translateX(80px) translateY(20px) rotate(5deg);
        }
        
        .chart-card:hover .chart-cover:nth-child(1) {
            transform: translateX(0) rotate(-8deg);
        }
        
        .chart-card:hover .chart-cover:nth-child(2) {
            transform: translateX(45px) translateY(15px) rotate(0deg);
        }
        
        .chart-card:hover .chart-cover:nth-child(3) {
            transform: translateX(90px) translateY(30px) rotate(8deg);
        }
        
        .chart-songs {
            margin-top: 10px;
        }
        
        .chart-song {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .chart-song-rank {
            width: 20px;
            font-size: 14px;
            font-weight: bold;
            color: #999;
        }
        
        .chart-song-rank.top {
            color: #e74c3c;
        }
        
        .chart-song-info {
            flex: 1;
        }
        
        .chart-song-name {
            font-size: 14px;
            color: white;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .chart-song-artist {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>轻聆 - 智能车载音乐播放器原型设计（横屏版）</h1>
        
        <div class="prototype-container">
            <!-- 欢迎界面 -->
            <div class="prototype">
                <div class="prototype-title">启动欢迎页</div>
                <div class="welcome-screen">
                    <div class="welcome-content">
                        <div class="logo"><i class="fas fa-music"></i></div>
                        <div class="welcome-text">
                            <div class="app-name">轻聆</div>
                            <div class="slogan">您的专属智能车载音乐伴侣</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 登录界面 -->
            <div class="prototype">
                <div class="prototype-title">用户登录</div>
                <div class="login-screen">
                    <div class="login-container">
                        <div class="login-title">欢迎使用轻聆</div>
                        <div class="login-buttons">
                            <div class="quick-login-button" id="weixin-login">
                                <i class="fab fa-weixin"></i> 微信一键登录
                            </div>
                            <div class="quick-login-button" id="qr-login">
                                <i class="fas fa-qrcode"></i> 扫码登录
                            </div>
                            <div class="quick-login-button" id="phone-login">
                                <i class="fas fa-mobile-alt"></i> 手机号登录
                            </div>
                            <div class="quick-login-button" id="guest-login">
                                <i class="fas fa-user-circle"></i> 游客登录
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主界面 -->
            <div class="prototype">
                <div class="prototype-title">主界面</div>
                <div class="main-container">
                    <div class="sidebar">
                        <div class="sidebar-icon active" data-target="player"><i class="fas fa-music"></i></div>
                        <div class="sidebar-icon" data-target="favorite"><i class="fas fa-heart"></i></div>
                        <div class="sidebar-icon" data-target="online"><i class="fas fa-globe"></i></div>
                        <div class="sidebar-icon" data-target="driving"><i class="fas fa-car"></i></div>
                        <div class="sidebar-icon" data-target="user"><i class="fas fa-user"></i></div>
                        <div class="sidebar-icon" data-target="settings"><i class="fas fa-cog"></i></div>
                    </div>
                    <div class="main-content">
                        <div class="album-container">
                            <div class="album-art" style="background: linear-gradient(45deg, #3498db, #9b59b6); box-shadow: 0 10px 20px rgba(0,0,0,0.3);"></div>
                            <div class="song-info">
                                <div class="song-title">星辰大海</div>
                                <div class="artist">黄霄雲</div>
                            </div>
                        </div>
                        <div class="lyrics-container">
                            <div class="lyrics-content">
                                <div class="lyrics-line">星辰大海</div>
                                <div class="lyrics-line">黄霄雲</div>
                                <div class="lyrics-line">词：唐恬</div>
                                <div class="lyrics-line">曲：钱雯雯</div>
                                <div class="lyrics-line prev">山川阅尽恍如一瞬</div>
                                <div class="lyrics-line active">白驹过隙世间烟云</div>
                                <div class="lyrics-line next">往事如歌几人记得</div>
                                <div class="lyrics-line">天地苍茫任我闯荡</div>
                                <div class="lyrics-line">风一样的梦想追逐</div>
                                <div class="lyrics-line">沧桑和感动都是关于成长的路</div>
                                <div class="lyrics-line">仿佛穿越一场大雨</div>
                                <div class="lyrics-line">看星辰大海吹过春夏秋冬</div>
                                <div class="lyrics-line">是你一直在我身旁</div>
                                <div class="lyrics-line">给我勇气能够抬头挺胸</div>
                                <div class="lyrics-line">虽然眼泪是生活给我的考验</div>
                                <div class="lyrics-line">告诉自己要坚强</div>
                            </div>
                            
                            <div class="player-control-panel">
                                <div class="side-btn" id="playlist-btn"><i class="fas fa-list"></i></div>
                                <div class="control-btn" id="prev-btn"><i class="fas fa-step-backward"></i></div>
                                <div class="play-btn" id="play-btn"><i class="fas fa-pause-circle"></i></div>
                                <div class="control-btn" id="next-btn"><i class="fas fa-step-forward"></i></div>
                                <div class="side-btn play-mode" id="play-mode-btn">
                                    <i class="fas fa-random"></i>
                                    <span class="play-mode-tooltip">随机播放</span>
                                </div>
                            </div>
                            
                            <div class="progress-container" id="progress-bar">
                                <div class="progress-bar"></div>
                            </div>
                            <div class="time-info">
                                <div class="current-time">1:23</div>
                                <div class="total-time">4:12</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 收藏界面 -->
            <div class="prototype">
                <div class="prototype-title">我的音乐库</div>
                <div class="music-library">
                    <div class="library-sidebar">
                        <div class="sidebar-icon" data-target="player"><i class="fas fa-music"></i></div>
                        <div class="sidebar-icon active" data-target="favorite"><i class="fas fa-heart"></i></div>
                        <div class="sidebar-icon" data-target="online"><i class="fas fa-globe"></i></div>
                        <div class="sidebar-icon" data-target="driving"><i class="fas fa-car"></i></div>
                        <div class="sidebar-icon" data-target="user"><i class="fas fa-user"></i></div>
                        <div class="sidebar-icon" data-target="settings"><i class="fas fa-cog"></i></div>
                    </div>
                    <div class="library-content">
                        <div class="library-header">
                            <div class="library-title">我的音乐库</div>
                            <div class="search-container">
                                <div class="search-icon"><i class="fas fa-search"></i></div>
                                <input type="text" class="search-input" placeholder="搜索收藏的歌曲、歌手或专辑...">
                            </div>
                        </div>
                        <div class="category-tabs">
                            <div class="tab active" data-category="songs">歌曲</div>
                            <div class="tab" data-category="albums">专辑</div>
                            <div class="tab" data-category="artists">歌手</div>
                            <div class="tab" data-category="playlists">歌单</div>
                        </div>
                        <div class="song-list">
                            <div class="song-item" data-song="1">
                                <div class="song-item-thumbnail" style="background: linear-gradient(45deg, #3498db, #9b59b6);"></div>
                                <div class="song-item-info">
                                    <div class="song-item-title">星辰大海</div>
                                    <div class="song-item-artist">黄霄雲</div>
                                </div>
                                <div class="song-item-action"><i class="fas fa-ellipsis-h"></i></div>
                            </div>
                            <div class="song-item" data-song="2">
                                <div class="song-item-thumbnail" style="background: linear-gradient(45deg, #e74c3c, #f39c12);"></div>
                                <div class="song-item-info">
                                    <div class="song-item-title">起风了</div>
                                    <div class="song-item-artist">买辣椒也用券</div>
                                </div>
                                <div class="song-item-action"><i class="fas fa-ellipsis-h"></i></div>
                            </div>
                            <div class="song-item" data-song="3">
                                <div class="song-item-thumbnail" style="background: linear-gradient(45deg, #2ecc71, #1abc9c);"></div>
                                <div class="song-item-info">
                                    <div class="song-item-title">无条件</div>
                                    <div class="song-item-artist">陈奕迅</div>
                                </div>
                                <div class="song-item-action"><i class="fas fa-ellipsis-h"></i></div>
                            </div>
                            <div class="song-item" data-song="4">
                                <div class="song-item-thumbnail" style="background: linear-gradient(45deg, #9b59b6, #8e44ad);"></div>
                                <div class="song-item-info">
                                    <div class="song-item-title">消愁</div>
                                    <div class="song-item-artist">毛不易</div>
                                </div>
                                <div class="song-item-action"><i class="fas fa-ellipsis-h"></i></div>
                            </div>
                            <div class="song-item" data-song="5">
                                <div class="song-item-thumbnail" style="background: linear-gradient(45deg, #f1c40f, #f39c12);"></div>
                                <div class="song-item-info">
                                    <div class="song-item-title">爱你</div>
                                    <div class="song-item-artist">王心凌</div>
                                </div>
                                <div class="song-item-action"><i class="fas fa-ellipsis-h"></i></div>
                            </div>
                            <div class="song-item" data-song="6">
                                <div class="song-item-thumbnail" style="background: linear-gradient(45deg, #3498db, #2980b9);"></div>
                                <div class="song-item-info">
                                    <div class="song-item-title">倒数</div>
                                    <div class="song-item-artist">G.E.M. 邓紫棋</div>
                                </div>
                                <div class="song-item-action"><i class="fas fa-ellipsis-h"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 在线音乐界面 -->
            <div class="prototype">
                <div class="prototype-title">音乐探索</div>
                <div class="online-music">
                    <div class="online-sidebar">
                        <div class="sidebar-icon" data-target="player"><i class="fas fa-music"></i></div>
                        <div class="sidebar-icon" data-target="favorite"><i class="fas fa-heart"></i></div>
                        <div class="sidebar-icon active" data-target="online"><i class="fas fa-globe"></i></div>
                        <div class="sidebar-icon" data-target="driving"><i class="fas fa-car"></i></div>
                        <div class="sidebar-icon" data-target="user"><i class="fas fa-user"></i></div>
                        <div class="sidebar-icon" data-target="settings"><i class="fas fa-cog"></i></div>
                    </div>
                    <div class="online-content">
                        <div class="online-header">
                            <div class="search-container">
                                <div class="search-icon"><i class="fas fa-search"></i></div>
                                <input type="text" class="search-input" placeholder="搜索歌曲、歌手、专辑或歌单...">
                            </div>
                        </div>
                        <div class="music-content-container">
                            <!-- 专属推荐 -->
                            <div class="music-section">
                                <div class="section-header">
                                    <div class="section-title">专属推荐</div>
                                    <div class="section-more">查看更多 <i class="fas fa-angle-right"></i></div>
                                </div>
                                <div class="card-grid">
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #3498db, #9b59b6); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">根据你的喜好推荐 | 周末私享歌单</div>
                                        <div class="music-card-desc">已收藏30万次</div>
                                    </div>
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #e74c3c, #f39c12); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">独家定制 | 你可能喜欢的华语新歌</div>
                                        <div class="music-card-desc">每周更新</div>
                                    </div>
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #2ecc71, #1abc9c); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">心情电台 | 治愈系音乐精选</div>
                                        <div class="music-card-desc">缓解疲劳的最佳选择</div>
                                    </div>
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #9b59b6, #8e44ad); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">车载必听 | 都市夜驾的氛围感音乐</div>
                                        <div class="music-card-desc">50万人正在听</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 排行榜 -->
                            <div class="music-section charts-section">
                                <div class="section-header">
                                    <div class="section-title">热门榜单</div>
                                    <div class="section-more">查看全部 <i class="fas fa-angle-right"></i></div>
                                </div>
                                <div class="charts-container">
                                    <div class="chart-card">
                                        <div class="chart-header">
                                            <div class="chart-title">流行热歌榜</div>
                                            <div class="chart-more"><i class="fas fa-chevron-right"></i></div>
                                        </div>
                                        <div class="chart-cover-container">
                                            <div class="chart-covers">
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #3498db, #2980b9); border-radius: 6px;"></div>
                                                </div>
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #e74c3c, #c0392b); border-radius: 6px;"></div>
                                                </div>
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #2ecc71, #27ae60); border-radius: 6px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart-songs">
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">1</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">星辰大海</div>
                                                    <div class="chart-song-artist">黄霄雲</div>
                                                </div>
                                            </div>
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">2</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">起风了</div>
                                                    <div class="chart-song-artist">买辣椒也用券</div>
                                                </div>
                                            </div>
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">3</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">无条件</div>
                                                    <div class="chart-song-artist">陈奕迅</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="chart-card">
                                        <div class="chart-header">
                                            <div class="chart-title">飙升榜</div>
                                            <div class="chart-more"><i class="fas fa-chevron-right"></i></div>
                                        </div>
                                        <div class="chart-cover-container">
                                            <div class="chart-covers">
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #f39c12, #f1c40f); border-radius: 6px;"></div>
                                                </div>
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #9b59b6, #8e44ad); border-radius: 6px;"></div>
                                                </div>
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #1abc9c, #16a085); border-radius: 6px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart-songs">
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">1</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">消愁</div>
                                                    <div class="chart-song-artist">毛不易</div>
                                                </div>
                                            </div>
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">2</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">倒数</div>
                                                    <div class="chart-song-artist">G.E.M. 邓紫棋</div>
                                                </div>
                                            </div>
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">3</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">爱你</div>
                                                    <div class="chart-song-artist">王心凌</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="chart-card">
                                        <div class="chart-header">
                                            <div class="chart-title">车载音乐榜</div>
                                            <div class="chart-more"><i class="fas fa-chevron-right"></i></div>
                                        </div>
                                        <div class="chart-cover-container">
                                            <div class="chart-covers">
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #16a085, #2ecc71); border-radius: 6px;"></div>
                                                </div>
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #2980b9, #3498db); border-radius: 6px;"></div>
                                                </div>
                                                <div class="chart-cover">
                                                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #c0392b, #e74c3c); border-radius: 6px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="chart-songs">
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">1</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">有何不可</div>
                                                    <div class="chart-song-artist">许嵩</div>
                                                </div>
                                            </div>
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">2</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">红颜如霜</div>
                                                    <div class="chart-song-artist">周深</div>
                                                </div>
                                            </div>
                                            <div class="chart-song">
                                                <div class="chart-song-rank top">3</div>
                                                <div class="chart-song-info">
                                                    <div class="chart-song-name">一路向北</div>
                                                    <div class="chart-song-artist">周杰伦</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 新歌速递 -->
                            <div class="music-section new-songs-section">
                                <div class="section-header">
                                    <div class="section-title">新歌速递</div>
                                    <div class="section-more">更多新歌 <i class="fas fa-angle-right"></i></div>
                                </div>
                                <div class="new-song-tabs">
                                    <div class="new-song-tab active">全部</div>
                                    <div class="new-song-tab">华语</div>
                                    <div class="new-song-tab">流行</div>
                                    <div class="new-song-tab">摇滚</div>
                                    <div class="new-song-tab">民谣</div>
                                    <div class="new-song-tab">电子</div>
                                </div>
                                <div class="new-songs-grid">
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #3498db, #2980b9); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲1</div>
                                                <div class="new-song-artist">热门歌手A</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                            <span class="new-song-tag">独家</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #e74c3c, #c0392b); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲2</div>
                                                <div class="new-song-artist">热门歌手B</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #2ecc71, #27ae60); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲3</div>
                                                <div class="new-song-artist">热门歌手C</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                            <span class="new-song-tag">热门</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #9b59b6, #8e44ad); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲4</div>
                                                <div class="new-song-artist">热门歌手D</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #f1c40f, #f39c12); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲5</div>
                                                <div class="new-song-artist">热门歌手E</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                            <span class="new-song-tag">独家</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #1abc9c, #16a085); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲6</div>
                                                <div class="new-song-artist">热门歌手F</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #34495e, #2c3e50); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲7</div>
                                                <div class="new-song-artist">热门歌手G</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                            <span class="new-song-tag">推荐</span>
                                        </div>
                                    </div>
                                    <div class="new-song-card">
                                        <div class="new-song-info">
                                            <div class="new-song-cover" style="background: linear-gradient(45deg, #e67e22, #d35400); display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-music" style="color: white; font-size: 24px;"></i>
                                            </div>
                                            <div class="new-song-detail">
                                                <div class="new-song-name">最新单曲8</div>
                                                <div class="new-song-artist">热门歌手H</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="new-song-tag">新</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 热门专辑 -->
                            <div class="music-section">
                                <div class="section-header">
                                    <div class="section-title">热门专辑</div>
                                    <div class="section-more">查看更多 <i class="fas fa-angle-right"></i></div>
                                </div>
                                <div class="card-grid">
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #16a085, #1abc9c); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">热门专辑1</div>
                                        <div class="music-card-desc">知名歌手A</div>
                                    </div>
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #c0392b, #e74c3c); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">热门专辑2</div>
                                        <div class="music-card-desc">知名歌手B</div>
                                    </div>
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #8e44ad, #9b59b6); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">热门专辑3</div>
                                        <div class="music-card-desc">知名歌手C</div>
                                    </div>
                                    <div class="music-card">
                                        <div class="music-card-cover">
                                            <div style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #2980b9, #3498db); border-radius: 8px;"></div>
                                        </div>
                                        <div class="music-card-title">热门专辑4</div>
                                        <div class="music-card-desc">知名歌手D</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 驾驶模式界面 -->
            <div class="prototype">
                <div class="prototype-title">驾驶模式</div>
                <div class="driving-container">
                    <div class="sidebar">
                        <div class="sidebar-icon" data-target="player"><i class="fas fa-music"></i></div>
                        <div class="sidebar-icon" data-target="favorite"><i class="fas fa-heart"></i></div>
                        <div class="sidebar-icon" data-target="online"><i class="fas fa-globe"></i></div>
                        <div class="sidebar-icon active" data-target="driving"><i class="fas fa-car"></i></div>
                        <div class="sidebar-icon" data-target="user"><i class="fas fa-user"></i></div>
                        <div class="sidebar-icon" data-target="settings"><i class="fas fa-cog"></i></div>
                    </div>
                    <div class="driving-content">
                        <div class="driving-main">
                            <div class="driving-controller">
                                <div class="driving-player">
                                    <div class="driving-album" style="background: linear-gradient(45deg, #3498db, #9b59b6); box-shadow: 0 10px 20px rgba(0,0,0,0.3);"></div>
                                    <div class="driving-info">
                                        <div class="driving-song-title">星辰大海</div>
                                        <div class="driving-artist">黄霄雲</div>
                                    </div>
                                </div>
                                
                                <div class="driving-progress">
                                    <div class="driving-progress-bar"></div>
                                </div>
                                
                                <div class="driving-time-info">
                                    <div>1:23</div>
                                    <div>4:12</div>
                                </div>
                                
                                <div class="driving-control-buttons">
                                    <div class="driving-btn" id="driving-prev"><i class="fas fa-step-backward"></i></div>
                                    <div class="driving-btn" id="driving-play" style="font-size: 60px;"><i class="fas fa-pause-circle"></i></div>
                                    <div class="driving-btn" id="driving-next"><i class="fas fa-step-forward"></i></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="driving-sidebar">
                            <div class="quick-actions">
                                <div class="quick-action" id="voice-control">
                                    <div class="quick-action-icon"><i class="fas fa-microphone"></i></div>
                                    <div class="quick-action-name">语音模式</div>
                                </div>
                                <div class="quick-action" id="driving-playlist">
                                    <div class="quick-action-icon"><i class="fas fa-list"></i></div>
                                    <div class="quick-action-name">播放列表</div>
                                </div>
                                <div class="quick-action" id="driving-shuffle">
                                    <div class="quick-action-icon play-mode" id="driving-mode-btn">
                                        <i class="fas fa-random"></i>
                                        <span class="play-mode-tooltip">随机播放</span>
                                    </div>
                                    <div class="quick-action-name">播放顺序</div>
                                </div>
                                <div class="quick-action" id="driving-volume">
                                    <div class="quick-action-icon"><i class="fas fa-volume-up"></i></div>
                                    <div class="quick-action-name">音量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 用户界面 -->
            <div class="prototype">
                <div class="prototype-title">用户中心</div>
                <div class="main-container">
                    <div class="sidebar">
                        <div class="sidebar-icon" data-target="player"><i class="fas fa-music"></i></div>
                        <div class="sidebar-icon" data-target="favorite"><i class="fas fa-heart"></i></div>
                        <div class="sidebar-icon" data-target="online"><i class="fas fa-globe"></i></div>
                        <div class="sidebar-icon" data-target="driving"><i class="fas fa-car"></i></div>
                        <div class="sidebar-icon active" data-target="user"><i class="fas fa-user"></i></div>
                        <div class="sidebar-icon" data-target="settings"><i class="fas fa-cog"></i></div>
                    </div>
                    <div class="main-content user-center-content">
                        <div class="user-header">
                            <div class="user-cover-image" style="background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.7)), linear-gradient(90deg, #16222A, #3A6073);"></div>
                            <div class="user-profile-large">
                                <div class="user-avatar-large" style="background: linear-gradient(45deg, #4e54c8, #8f94fb); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user" style="font-size: 48px; color: white;"></i>
                                </div>
                                <div class="user-info-large">
                                    <div class="user-name-large">张小明</div>
                                    <div class="user-tags">
                                        <div class="user-tag vip-tag"><i class="fas fa-crown"></i> VIP会员</div>
                                        <div class="user-tag level-tag">Lv.6</div>
                                    </div>
                                    <div class="user-signature">音乐是生活的调味剂，让心灵得到治愈</div>
                                </div>
                            </div>
                        </div>
                        <div class="user-stats">
                            <div class="stat-item">
                                <div class="stat-value">268</div>
                                <div class="stat-label">收藏歌曲</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">32</div>
                                <div class="stat-label">创建歌单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">86</div>
                                <div class="stat-label">关注歌手</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">124</div>
                                <div class="stat-label">收听时长(小时)</div>
                            </div>
                        </div>
                        <div class="user-sections">
                            <div class="user-section">
                                <div class="section-header">
                                    <div class="section-title">账户信息</div>
                                    <div class="section-more">更多 <i class="fas fa-angle-right"></i></div>
                                </div>
                                <div class="account-info-list">
                                    <div class="account-info-item">
                                        <div class="info-label">手机号</div>
                                        <div class="info-value">138****6789</div>
                                    </div>
                                    <div class="account-info-item">
                                        <div class="info-label">邮箱</div>
                                        <div class="info-value"><EMAIL></div>
                                    </div>
                                    <div class="account-info-item">
                                        <div class="info-label">会员状态</div>
                                        <div class="info-value highlight">年费会员（有效期至2024年12月）</div>
                                    </div>
                                    <div class="account-info-item">
                                        <div class="info-label">注册时间</div>
                                        <div class="info-value">2021年03月15日</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 设置界面 -->
            <div class="prototype">
                <div class="prototype-title">应用设置</div>
                <div class="settings-container">
                    <div class="settings-sidebar">
                        <div class="sidebar-icon" data-target="player"><i class="fas fa-music"></i></div>
                        <div class="sidebar-icon" data-target="favorite"><i class="fas fa-heart"></i></div>
                        <div class="sidebar-icon" data-target="online"><i class="fas fa-globe"></i></div>
                        <div class="sidebar-icon" data-target="driving"><i class="fas fa-car"></i></div>
                        <div class="sidebar-icon" data-target="user"><i class="fas fa-user"></i></div>
                        <div class="sidebar-icon active" data-target="settings"><i class="fas fa-cog"></i></div>
                    </div>
                    <div class="settings-content">
                        <div class="settings-header">
                            <div class="settings-title">应用设置</div>
                        </div>
                        <div class="settings-body">
                            <div class="settings-categories">
                                <div class="settings-category active" data-setting="general">通用</div>
                                <div class="settings-category" data-setting="playback">播放</div>
                                <div class="settings-category" data-setting="driving">驾驶模式</div>
                                <div class="settings-category" data-setting="about">关于</div>
                            </div>
                            <div class="settings-options">
                                <div class="settings-group">
                                    <div class="settings-group-title">通用设置</div>
                                    <div class="settings-item">
                                        <div>
                                            <div class="settings-item-title">自动播放</div>
                                            <div class="settings-item-desc">启动应用时自动播放音乐</div>
                                        </div>
                                        <div class="switch on" id="auto-play-toggle"></div>
                                    </div>
                                    <div class="settings-item">
                                        <div>
                                            <div class="settings-item-title">夜间模式</div>
                                            <div class="settings-item-desc">启用深色主题</div>
                                        </div>
                                        <div class="switch on" id="night-mode-toggle"></div>
                                    </div>
                                    <div class="settings-item" id="language-selector">
                                        <div>
                                            <div class="settings-item-title">语言</div>
                                            <div class="settings-item-desc">选择应用显示语言</div>
                                        </div>
                                        <div class="settings-item-value">
                                            简体中文
                                            <span class="chevron-icon"><i class="fas fa-chevron-right"></i></span>
                                        </div>
                                    </div>
                                    <div class="settings-item">
                                        <div>
                                            <div class="settings-item-title">屏幕常亮</div>
                                            <div class="settings-item-desc">播放音乐时保持屏幕亮起</div>
                                        </div>
                                        <div class="switch on" id="screen-on-toggle"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 播放顺序模式切换
            const playModes = [
                { name: '顺序播放', icon: 'fas fa-stream' },
                { name: '单曲循环', icon: 'fas fa-redo-alt' },
                { name: '列表循环', icon: 'fas fa-sync-alt' },
                { name: '随机播放', icon: 'fas fa-random' }
            ];
            
            let currentModeIndex = 3; // 默认是随机播放
            
            // 主界面播放顺序按钮
            const playModeBtn = document.getElementById('play-mode-btn');
            if (playModeBtn) {
                playModeBtn.addEventListener('click', function() {
                    // 切换到下一个播放模式
                    currentModeIndex = (currentModeIndex + 1) % playModes.length;
                    updatePlayMode(playModeBtn, currentModeIndex);
                    alert(`已切换为${playModes[currentModeIndex].name}`);
                });
            }
            
            // 驾驶模式播放顺序按钮
            const drivingModeBtn = document.getElementById('driving-mode-btn');
            if (drivingModeBtn) {
                drivingModeBtn.addEventListener('click', function() {
                    // 切换到下一个播放模式
                    currentModeIndex = (currentModeIndex + 1) % playModes.length;
                    updatePlayMode(drivingModeBtn, currentModeIndex);
                    alert(`已切换为${playModes[currentModeIndex].name}`);
                });
            }
            
            // 更新播放模式图标和提示
            function updatePlayMode(element, modeIndex) {
                const mode = playModes[modeIndex];
                const iconElement = element.querySelector('i');
                const tooltipElement = element.querySelector('.play-mode-tooltip');
                
                // 移除所有可能的图标类
                iconElement.className = mode.icon;
                
                // 更新提示文本
                if (tooltipElement) {
                    tooltipElement.textContent = mode.name;
                }
                
                // 同步另一个界面的播放模式
                if (element.id === 'play-mode-btn' && drivingModeBtn) {
                    updatePlayMode(drivingModeBtn, modeIndex);
                } else if (element.id === 'driving-mode-btn' && playModeBtn) {
                    updatePlayMode(playModeBtn, modeIndex);
                }
            }
            
            // 登录按钮点击事件
            const loginButtons = document.querySelectorAll('.quick-login-button');
            loginButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 登录成功后可以跳转到主界面
                    alert('登录成功，即将进入主界面');
                    // 实际应用中这里会有真实的登录逻辑
                });
            });
            
            // 主界面播放控制
            const playBtn = document.getElementById('play-btn');
            if (playBtn) {
                playBtn.addEventListener('click', function() {
                    // 切换播放/暂停图标
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-pause-circle')) {
                        icon.classList.remove('fa-pause-circle');
                        icon.classList.add('fa-play-circle');
                    } else {
                        icon.classList.remove('fa-play-circle');
                        icon.classList.add('fa-pause-circle');
                    }
                });
            }
            
            // 上一首/下一首按钮
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            if (prevBtn) {
                prevBtn.addEventListener('click', function() {
                    alert('切换到上一首歌曲');
                });
            }
            
            if (nextBtn) {
                nextBtn.addEventListener('click', function() {
                    alert('切换到下一首歌曲');
                });
            }
            
            // 播放列表按钮
            const playlistBtn = document.getElementById('playlist-btn');
            if (playlistBtn) {
                playlistBtn.addEventListener('click', function() {
                    alert('打开当前播放列表');
                });
            }
            
            // 随机播放按钮 - 替换为播放模式处理
            const shuffleBtn = document.getElementById('shuffle-btn');
            if (shuffleBtn) {
                shuffleBtn.addEventListener('click', function() {
                    // 此代码已被上面的播放模式切换逻辑替代
                });
            }
            
            // 进度条交互
            const progressBar = document.getElementById('progress-bar');
            if (progressBar) {
                progressBar.addEventListener('click', function(e) {
                    const width = this.clientWidth;
                    const clickX = e.offsetX;
                    const percent = (clickX / width) * 100;
                    
                    // 设置进度条宽度
                    this.querySelector('.progress-bar').style.width = percent + '%';
                    
                    // 更新当前时间显示（真实应用中会计算实际时间）
                    const totalSeconds = 252; // 4:12 转为秒
                    const currentSeconds = Math.floor(totalSeconds * percent / 100);
                    const minutes = Math.floor(currentSeconds / 60);
                    const seconds = currentSeconds % 60;
                    document.querySelector('.current-time').textContent = 
                        `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                });
            }
            
            // 歌曲列表项点击事件
            const songItems = document.querySelectorAll('.song-item');
            songItems.forEach(item => {
                item.addEventListener('click', function() {
                    const songTitle = this.querySelector('.song-item-title').textContent;
                    const songArtist = this.querySelector('.song-item-artist').textContent;
                    alert(`播放歌曲：${songTitle} - ${songArtist}`);
                });
            });
            
            // 标签切换
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    // 激活当前标签
                    this.classList.add('active');
                    
                    const category = this.getAttribute('data-category');
                    alert(`切换到${this.textContent}分类`);
                });
            });
            
            // 在线音乐标签切换
            const onlineTabs = document.querySelectorAll('.online-tab');
            onlineTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有激活状态
                    onlineTabs.forEach(t => t.classList.remove('active'));
                    // 激活当前标签
                    this.classList.add('active');
                    
                    const tabName = this.getAttribute('data-tab');
                    alert(`切换到${this.textContent}标签`);
                });
            });
            
            // 歌单项点击事件
            const playlistItems = document.querySelectorAll('.playlist-item');
            playlistItems.forEach(item => {
                item.addEventListener('click', function() {
                    const playlistName = this.querySelector('.playlist-name').textContent;
                    alert(`打开歌单：${playlistName}`);
                });
            });
            
            // 驾驶模式控制
            const drivingPlayBtn = document.getElementById('driving-play');
            if (drivingPlayBtn) {
                drivingPlayBtn.addEventListener('click', function() {
                    // 切换播放/暂停图标
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-pause-circle')) {
                        icon.classList.remove('fa-pause-circle');
                        icon.classList.add('fa-play-circle');
                    } else {
                        icon.classList.remove('fa-play-circle');
                        icon.classList.add('fa-pause-circle');
                    }
                });
            }
            
            // 驾驶模式快速操作
            const quickActions = document.querySelectorAll('.quick-action');
            quickActions.forEach(action => {
                action.addEventListener('click', function() {
                    const actionId = this.id;
                    const actionName = this.querySelector('.quick-action-name').textContent;
                    
                    switch (actionId) {
                        case 'voice-control':
                            alert('激活语音控制: "播放周杰伦的歌"');
                            break;
                        case 'driving-playlist':
                            alert('打开驾驶模式播放列表');
                            break;
                        case 'driving-shuffle':
                            alert('切换随机播放模式');
                            break;
                        case 'driving-volume':
                            alert('调整音量');
                            break;
                        default:
                            alert(`点击了${actionName}`);
                    }
                });
            });
            
            // 设置切换
            const settingsCategories = document.querySelectorAll('.settings-category');
            settingsCategories.forEach(category => {
                category.addEventListener('click', function() {
                    // 移除所有激活状态
                    settingsCategories.forEach(c => c.classList.remove('active'));
                    // 激活当前类别
                    this.classList.add('active');
                    
                    const settingName = this.getAttribute('data-setting');
                    alert(`切换到${this.textContent}设置`);
                });
            });
            
            // 开关切换
            const switches = document.querySelectorAll('.switch');
            switches.forEach(switchElem => {
                switchElem.addEventListener('click', function() {
                    this.classList.toggle('on');
                    
                    const settingTitle = this.closest('.settings-item').querySelector('.settings-item-title').textContent;
                    const status = this.classList.contains('on') ? '开启' : '关闭';
                    alert(`${settingTitle}已${status}`);
                });
            });
            
            // 侧边栏导航
            const sidebarIcons = document.querySelectorAll('.sidebar-icon, .library-sidebar .sidebar-icon, .online-sidebar .sidebar-icon, .settings-sidebar .sidebar-icon');
            sidebarIcons.forEach(icon => {
                icon.addEventListener('click', function() {
                    const target = this.getAttribute('data-target');
                    if (target) {
                        alert(`切换到${target}界面`);
                    }
                });
            });

            // 驾驶模式进度条交互
            const drivingProgress = document.querySelector('.driving-progress');
            if (drivingProgress) {
                drivingProgress.addEventListener('click', function(e) {
                    const width = this.clientWidth;
                    const clickX = e.offsetX;
                    const percent = (clickX / width) * 100;
                    
                    // 设置进度条宽度
                    this.querySelector('.driving-progress-bar').style.width = percent + '%';
                    
                    // 更新当前时间显示
                    const totalSeconds = 252; // 4:12 转为秒
                    const currentSeconds = Math.floor(totalSeconds * percent / 100);
                    const minutes = Math.floor(currentSeconds / 60);
                    const seconds = currentSeconds % 60;
                    
                    // 更新时间显示
                    const timeInfoElements = document.querySelector('.driving-time-info').children;
                    if (timeInfoElements && timeInfoElements.length > 0) {
                        timeInfoElements[0].textContent = `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                    }
                });
            }
        });
    </script>
</body>
</html>

