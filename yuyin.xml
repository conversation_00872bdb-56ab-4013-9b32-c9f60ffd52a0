<mxfile host="app.diagrams.net" modified="2023-11-05T08:41:09.961Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="MNgFrVb1ekvmN7bm3q_S" version="15.7.0" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="语音控制流程">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="APP-INIT-1" value="应用启动" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="40" y="20" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="PERM-REQ-1" value="请求所需权限" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="40" y="100" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="INIT-1" value="初始化语音唤醒器&lt;br&gt;(EventManager &quot;wp&quot;)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="200" y="20" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="INIT-2" value="初始化语音识别器&lt;br&gt;(EventManager &quot;asr&quot;)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="200" y="80" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="INIT-3" value="初始化语音合成器&lt;br&gt;(SpeechSynthesizer)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="200" y="140" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="START-WAKEUP" value="启动语音唤醒监听&lt;br&gt;(startWakeup)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="400" y="80" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WAKEUP-DETECT" value="检测到唤醒词" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="220" y="220" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="BEEP-SOUND" value="播放提示音&lt;br&gt;(playBeepSound)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="210" y="340" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WAKEUP-TYPE" value="唤醒词类型?" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="220" y="420" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="DIRECT-CMD" value="直接执行操作命令&lt;br&gt;(handleWakeupSuccess)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="40" y="500" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="WAKEUP-STOP" value="停止语音唤醒&lt;br&gt;(stopWakeup)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="360" y="500" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MUSIC-PAUSE" value="暂停音乐播放&lt;br&gt;(记录播放状态)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="360" y="580" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="START-ASR" value="启动语音识别&lt;br&gt;(startSpeechRecognition)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="350" y="660" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ASR-RESULT" value="识别结果是否有效" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="220" y="740" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PROCESS-CMD" value="处理语音命令&lt;br&gt;(processVoiceCommand)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="200" y="860" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="CMD-1" value="播放/暂停" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="40" y="940" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CMD-2" value="下一首/上一首" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="140" y="940" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CMD-3" value="随机播放" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="250" y="940" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CMD-4" value="循环播放" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="330" y="940" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CMD-5" value="在线音乐" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="410" y="940" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SPEAK-ERROR" value="语音合成播报错误&lt;br&gt;(speakMessage)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="390" y="765" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="RESTART-WAKEUP" value="重新启动语音唤醒&lt;br&gt;(startWakeup)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="210" y="1020" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="CONN-1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="APP-INIT-1" target="PERM-REQ-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="APP-INIT-1" target="INIT-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="INIT-1" target="INIT-2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="INIT-2" target="INIT-3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-5" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="INIT-2" target="START-WAKEUP">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="INIT-3" target="WAKEUP-DETECT">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="START-WAKEUP" target="WAKEUP-DETECT">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="480" y="260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-8" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WAKEUP-DETECT" target="BEEP-SOUND">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="BEEP-SOUND" target="WAKEUP-TYPE">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-10" value="操作命令词" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WAKEUP-TYPE" target="DIRECT-CMD">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="460" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-11" value="唤醒词(小度你好)" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WAKEUP-TYPE" target="WAKEUP-STOP">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="420" y="460" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WAKEUP-STOP" target="MUSIC-PAUSE">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="MUSIC-PAUSE" target="START-ASR">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="START-ASR" target="ASR-RESULT">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="420" y="780" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-15" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="ASR-RESULT" target="PROCESS-CMD">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-16" value="否" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="ASR-RESULT" target="SPEAK-ERROR">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="440" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-17" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.071;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="PROCESS-CMD" target="CMD-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="280" y="920" />
              <mxPoint x="46" y="920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-18" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="PROCESS-CMD" target="CMD-2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="280" y="920" />
              <mxPoint x="190" y="920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-19" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="PROCESS-CMD" target="CMD-3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="PROCESS-CMD" target="CMD-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="280" y="920" />
              <mxPoint x="365" y="920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="PROCESS-CMD" target="CMD-5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="280" y="920" />
              <mxPoint x="445" y="920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="CMD-3" target="RESTART-WAKEUP">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="285" y="1000" />
              <mxPoint x="280" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="DIRECT-CMD" target="RESTART-WAKEUP">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="1040" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="SPEAK-ERROR" target="RESTART-WAKEUP">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="450" y="1040" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-25" value="" style="curved=1;endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="RESTART-WAKEUP" target="WAKEUP-DETECT">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="800" as="sourcePoint" />
            <mxPoint x="440" y="750" as="targetPoint" />
            <Array as="points">
              <mxPoint x="20" y="1040" />
              <mxPoint x="20" y="260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CONN-26" value="未检测到" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WAKEUP-DETECT">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="400" as="sourcePoint" />
            <mxPoint x="220" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="180" y="260" />
              <mxPoint x="180" y="290" />
              <mxPoint x="200" y="290" />
              <mxPoint x="200" y="260" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>