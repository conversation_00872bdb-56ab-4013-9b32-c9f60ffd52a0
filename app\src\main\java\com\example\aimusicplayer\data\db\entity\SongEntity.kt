package com.example.aimusicplayer.data.db.entity

import android.os.Parcelable
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

/**
 * 歌曲实体类
 * 用于数据库存储歌曲信息
 */
@Entity(
    tableName = "songs",
    indices = [
        Index("title"),
        Index("artist"),
        Index("album")
    ]
)
data class SongEntity(
    // 歌曲类型:本地/网络
    @ColumnInfo("type")
    val type: Int = TYPE_ONLINE,

    // 歌曲ID
    @ColumnInfo("song_id")
    val songId: Long = 0,

    // 音乐标题
    @ColumnInfo("title")
    val title: String = "",

    // 艺术家
    @ColumnInfo("artist")
    val artist: String = "",

    // 艺术家ID
    @ColumnInfo("artist_id")
    val artistId: Long = 0,

    // 专辑
    @ColumnInfo("album")
    val album: String = "",

    // 专辑ID
    @ColumnInfo("album_id")
    val albumId: Long = 0,

    // 专辑封面
    @ColumnInfo("album_cover")
    val albumCover: String = "",

    // 持续时间
    @ColumnInfo("duration")
    val duration: Long = 0,

    // 播放地址
    @ColumnInfo("uri", defaultValue = "")
    var uri: String = "",

    // [本地]文件路径
    @ColumnInfo("path")
    var path: String = "",

    // [本地]文件名
    @ColumnInfo("file_name")
    val fileName: String = "",

    // [本地]文件大小
    @ColumnInfo("file_size")
    val fileSize: Long = 0,

    // 是否VIP歌曲
    @ColumnInfo("is_vip")
    val isVip: Boolean = false,

    // 是否收藏
    @ColumnInfo("is_favorite")
    val isFavorite: Boolean = false,

    // 上次播放时间
    @ColumnInfo("last_played_time")
    val lastPlayedTime: Long = 0
) {
    @PrimaryKey
    @ColumnInfo("unique_id")
    var uniqueId: String = generateUniqueId(type, songId)

    override fun hashCode(): Int {
        return uniqueId.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return other is SongEntity
                && other.uniqueId == this.uniqueId
    }

    fun isLocal() = type == TYPE_LOCAL

    /**
     * 转换为MediaItem
     */
    fun toMediaItem(): MediaItem {
        val metadata = MediaMetadata.Builder()
            .setTitle(title)
            .setArtist(artist)
            .setAlbumTitle(album)
            .setArtworkUri(android.net.Uri.parse(albumCover))
            .setMediaType(MediaMetadata.MEDIA_TYPE_MUSIC)
            .setIsBrowsable(false)
            .setIsPlayable(true)
            .build()

        val mediaId = uniqueId

        return MediaItem.Builder()
            .setMediaId(mediaId)
            .setMediaMetadata(metadata)
            .setUri(if (isLocal()) path else uri)
            .build()
    }

    companion object {
        const val TYPE_LOCAL = 0
        const val TYPE_ONLINE = 1

        /**
         * 生成唯一ID
         */
        fun generateUniqueId(type: Int, songId: Long): String {
            return "${type}_$songId"
        }

        /**
         * 从MediaItem转换为SongEntity
         */
        fun fromMediaItem(mediaItem: MediaItem): SongEntity {
            val metadata = mediaItem.mediaMetadata
            val isLocal = mediaItem.localConfiguration?.uri?.scheme == "file"
            val type = if (isLocal) TYPE_LOCAL else TYPE_ONLINE
            val songId = mediaItem.mediaId.split("_").lastOrNull()?.toLongOrNull() ?: 0

            return SongEntity(
                type = type,
                songId = songId,
                title = metadata.title?.toString() ?: "",
                artist = metadata.artist?.toString() ?: "",
                album = metadata.albumTitle?.toString() ?: "",
                albumCover = metadata.artworkUri?.toString() ?: "",
                duration = metadata.extras?.getLong("duration") ?: 0,
                uri = mediaItem.requestMetadata.mediaUri?.toString() ?: "",
                path = if (isLocal) mediaItem.localConfiguration?.uri?.path ?: "" else ""
            )
        }
    }
}
