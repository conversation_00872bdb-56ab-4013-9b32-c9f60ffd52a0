package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.renderscript.Allocation
import android.renderscript.Element
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import android.widget.ImageView
import androidx.annotation.WorkerThread
import androidx.palette.graphics.Palette
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * 背景模糊效果工具类
 * 用于实现专辑封面背景模糊效果
 */
object BlurUtils {
    // 缓存已处理的模糊图片，避免重复计算
    private val blurCache = ConcurrentHashMap<String, Bitmap>()
    
    // 缓存提取的颜色，避免重复计算
    private val colorCache = ConcurrentHashMap<String, Int>()
    
    // 默认模糊半径
    private const val DEFAULT_BLUR_RADIUS = 25f
    
    // 最大缓存数量
    private const val MAX_CACHE_SIZE = 20
    
    /**
     * 加载并模糊图片
     * @param context 上下文
     * @param imageUrl 图片URL
     * @param targetView 目标ImageView
     * @param radius 模糊半径，默认25
     */
    suspend fun loadAndBlurImage(
        context: Context,
        imageUrl: String,
        targetView: ImageView,
        radius: Float = DEFAULT_BLUR_RADIUS
    ) {
        withContext(Dispatchers.IO) {
            try {
                // 检查缓存
                val cachedBitmap = blurCache[imageUrl]
                if (cachedBitmap != null) {
                    withContext(Dispatchers.Main) {
                        targetView.setImageBitmap(cachedBitmap)
                    }
                    return@withContext
                }
                
                // 加载原始图片
                val originalBitmap = Glide.with(context)
                    .asBitmap()
                    .load(imageUrl)
                    .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
                    .submit()
                    .get()
                
                // 缩小图片以提高性能
                val scaledBitmap = Bitmap.createScaledBitmap(
                    originalBitmap,
                    originalBitmap.width / 2,
                    originalBitmap.height / 2,
                    true
                )
                
                // 模糊处理
                val blurredBitmap = blurBitmap(context, scaledBitmap, radius)
                
                // 缓存结果
                if (blurCache.size >= MAX_CACHE_SIZE) {
                    // 如果缓存已满，移除第一个元素
                    blurCache.keys.firstOrNull()?.let { blurCache.remove(it) }
                }
                blurCache[imageUrl] = blurredBitmap
                
                // 设置到ImageView
                withContext(Dispatchers.Main) {
                    targetView.setImageBitmap(blurredBitmap)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 模糊处理Bitmap
     * @param context 上下文
     * @param bitmap 原始Bitmap
     * @param radius 模糊半径
     * @return 模糊后的Bitmap
     */
    @WorkerThread
    private fun blurBitmap(context: Context, bitmap: Bitmap, radius: Float): Bitmap {
        val outputBitmap = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        
        val rs = RenderScript.create(context)
        val intrinsicBlur = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs))
        
        val allocationIn = Allocation.createFromBitmap(rs, bitmap)
        val allocationOut = Allocation.createFromBitmap(rs, outputBitmap)
        
        intrinsicBlur.setRadius(radius)
        intrinsicBlur.setInput(allocationIn)
        intrinsicBlur.forEach(allocationOut)
        
        allocationOut.copyTo(outputBitmap)
        
        rs.destroy()
        return outputBitmap
    }
    
    /**
     * 从ImageView中提取主色调
     * @param imageView 包含图片的ImageView
     * @return 提取的主色调
     */
    suspend fun extractDominantColor(imageView: ImageView, defaultColor: Int): Int {
        return withContext(Dispatchers.IO) {
            try {
                val drawable = imageView.drawable
                if (drawable is BitmapDrawable) {
                    val bitmap = drawable.bitmap
                    val key = bitmap.toString()
                    
                    // 检查缓存
                    val cachedColor = colorCache[key]
                    if (cachedColor != null) {
                        return@withContext cachedColor
                    }
                    
                    // 使用Palette提取颜色
                    val palette = Palette.from(bitmap).generate()
                    val dominantColor = palette.getDominantColor(defaultColor)
                    
                    // 缓存结果
                    if (colorCache.size >= MAX_CACHE_SIZE) {
                        colorCache.keys.firstOrNull()?.let { colorCache.remove(it) }
                    }
                    colorCache[key] = dominantColor
                    
                    return@withContext dominantColor
                }
                defaultColor
            } catch (e: Exception) {
                e.printStackTrace()
                defaultColor
            }
        }
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        blurCache.clear()
        colorCache.clear()
    }
}
