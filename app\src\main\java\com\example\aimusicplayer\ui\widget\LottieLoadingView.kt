package com.example.aimusicplayer.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.LottieDrawable
import com.example.aimusicplayer.R

/**
 * 自定义Lottie加载动画视图
 * 提供更精美的加载动画效果，替代传统的ProgressBar
 */
class LottieLoadingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    
    private val animationView: LottieAnimationView
    private val messageView: TextView
    
    private var animationAsset = "music_loading.json" // 默认动画
    private var loadingMessage = "加载中..." // 默认消息
    private var autoPlay = true // 默认自动播放
    private var loop = true // 默认循环播放
    
    init {
        // 加载布局
        val view = LayoutInflater.from(context).inflate(R.layout.view_lottie_loading, this, true)
        
        // 获取视图引用
        animationView = view.findViewById(R.id.lottie_animation_view)
        messageView = view.findViewById(R.id.loading_message)
        
        // 处理自定义属性
        if (attrs != null) {
            val a = context.obtainStyledAttributes(attrs, R.styleable.LottieLoadingView)
            
            // 获取动画资源
            val animationAssetAttr = a.getString(R.styleable.LottieLoadingView_lottieAnimationAsset)
            if (!animationAssetAttr.isNullOrEmpty()) {
                this.animationAsset = animationAssetAttr
            }
            
            // 获取加载消息
            val messageAttr = a.getString(R.styleable.LottieLoadingView_loadingMessage)
            if (!messageAttr.isNullOrEmpty()) {
                this.loadingMessage = messageAttr
            }
            
            // 获取自动播放设置
            this.autoPlay = a.getBoolean(R.styleable.LottieLoadingView_autoPlay, true)
            
            // 获取循环播放设置
            this.loop = a.getBoolean(R.styleable.LottieLoadingView_loop, true)
            
            a.recycle()
        }
        
        // 设置动画
        animationView.setAnimation(animationAsset)
        
        // 设置循环播放
        animationView.repeatCount = if (loop) LottieDrawable.INFINITE else 0
        
        // 设置消息
        messageView.text = loadingMessage
        
        // 自动播放
        if (autoPlay) {
            animationView.playAnimation()
        }
    }
    
    /**
     * 设置动画资源
     * @param animationAsset 动画资源名称（assets目录下的JSON文件）
     */
    fun setAnimationAsset(animationAsset: String) {
        this.animationAsset = animationAsset
        animationView.setAnimation(animationAsset)
        
        // 如果设置为自动播放，则重新开始播放
        if (autoPlay) {
            animationView.playAnimation()
        }
    }
    
    /**
     * 设置加载消息
     * @param message 加载消息
     */
    fun setLoadingMessage(message: String) {
        this.loadingMessage = message
        messageView.text = message
    }
    
    /**
     * 显示加载消息
     */
    fun showMessage() {
        messageView.visibility = View.VISIBLE
    }
    
    /**
     * 隐藏加载消息
     */
    fun hideMessage() {
        messageView.visibility = View.GONE
    }
    
    /**
     * 开始播放动画
     */
    fun startAnimation() {
        animationView.playAnimation()
    }
    
    /**
     * 暂停动画
     */
    fun pauseAnimation() {
        animationView.pauseAnimation()
    }
    
    /**
     * 停止动画
     */
    fun stopAnimation() {
        animationView.cancelAnimation()
    }
    
    /**
     * 设置动画速度
     * @param speed 速度（1.0f为正常速度）
     */
    fun setSpeed(speed: Float) {
        animationView.speed = speed
    }
    
    /**
     * 设置是否循环播放
     * @param loop 是否循环播放
     */
    fun setLoop(loop: Boolean) {
        this.loop = loop
        animationView.repeatCount = if (loop) LottieDrawable.INFINITE else 0
    }
}
