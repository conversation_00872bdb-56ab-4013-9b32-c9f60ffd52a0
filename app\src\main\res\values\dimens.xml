<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 文本尺寸 -->
    <dimen name="text_size_headline">24sp</dimen>         <!-- 大标题 -->
    <dimen name="text_size_title">20sp</dimen>            <!-- 标题 -->
    <dimen name="text_size_subtitle">18sp</dimen>         <!-- 副标题 -->
    <dimen name="text_size_body">16sp</dimen>             <!-- 正文 -->
    <dimen name="text_size_caption">14sp</dimen>          <!-- 说明文字 -->
    <dimen name="text_size_small">12sp</dimen>            <!-- 小字体 -->

    <!-- 驾驶模式文本尺寸 (更大以便驾驶时阅读) -->
    <dimen name="driving_text_size_title">30sp</dimen>    <!-- 驾驶模式标题 -->
    <dimen name="driving_text_size_body">24sp</dimen>     <!-- 驾驶模式正文 -->
    <dimen name="driving_text_size_small">20sp</dimen>    <!-- 驾驶模式小字 -->

    <!-- 边距 -->
    <dimen name="margin_tiny">2dp</dimen>                 <!-- 极小边距 -->
    <dimen name="margin_small">4dp</dimen>                <!-- 小边距 -->
    <dimen name="margin_medium">8dp</dimen>               <!-- 中等边距 -->
    <dimen name="margin_normal">16dp</dimen>              <!-- 标准边距 -->
    <dimen name="margin_large">24dp</dimen>               <!-- 大边距 -->
    <dimen name="margin_xlarge">32dp</dimen>              <!-- 超大边距 -->
    <dimen name="margin_xxlarge">48dp</dimen>             <!-- 特大边距 -->

    <!-- 间距 -->
    <dimen name="spacing_large">24dp</dimen>              <!-- 大间距 -->

    <!-- 内边距 -->
    <dimen name="padding_tiny">2dp</dimen>                <!-- 极小内边距 -->
    <dimen name="padding_small">4dp</dimen>               <!-- 小内边距 -->
    <dimen name="padding_medium">8dp</dimen>              <!-- 中等内边距 -->
    <dimen name="padding_normal">16dp</dimen>             <!-- 标准内边距 -->
    <dimen name="padding_large">24dp</dimen>              <!-- 大内边距 -->
    <dimen name="padding_xlarge">32dp</dimen>             <!-- 超大内边距 -->

    <!-- 圆角 -->
    <dimen name="corner_radius_small">4dp</dimen>         <!-- 小圆角 -->
    <dimen name="corner_radius_medium">8dp</dimen>        <!-- 中等圆角 -->
    <dimen name="corner_radius_large">16dp</dimen>        <!-- 大圆角 -->

    <!-- 高度 -->
    <dimen name="height_button">48dp</dimen>              <!-- 按钮高度 -->
    <dimen name="height_toolbar">56dp</dimen>             <!-- 工具栏高度 -->
    <dimen name="height_navigation_item">56dp</dimen>     <!-- 导航项高度 -->
    <dimen name="height_divider">1dp</dimen>              <!-- 分隔线高度 -->
    <dimen name="height_seekbar">24dp</dimen>             <!-- 进度条高度 -->
    <dimen name="height_list_item">72dp</dimen>           <!-- 列表项高度 -->
    <dimen name="height_card">180dp</dimen>               <!-- 卡片高度 -->

    <!-- 宽度 -->
    <dimen name="width_sidebar">80dp</dimen>              <!-- 侧边栏宽度 -->
    <dimen name="width_icon">24dp</dimen>                 <!-- 图标宽度 -->
    <dimen name="width_fab">56dp</dimen>                  <!-- 浮动按钮宽度 -->
    <dimen name="width_sidebar_selection_indicator">4dp</dimen> <!-- 侧边栏选中指示器宽度 -->

    <!-- 图像尺寸 -->
    <dimen name="image_thumbnail_small">40dp</dimen>      <!-- 小缩略图 -->
    <dimen name="image_thumbnail_medium">60dp</dimen>     <!-- 中缩略图 -->
    <dimen name="image_thumbnail_large">80dp</dimen>      <!-- 大缩略图 -->
    <dimen name="image_cover_small">120dp</dimen>         <!-- 小封面 -->
    <dimen name="image_cover_medium">180dp</dimen>        <!-- 中封面 -->
    <dimen name="image_cover_large">240dp</dimen>         <!-- 大封面 -->
    <dimen name="image_cover_xlarge">320dp</dimen>        <!-- 特大封面 -->

    <!-- 播放器控件 -->
    <dimen name="player_control_size_small">40dp</dimen>  <!-- 小控制按钮 -->
    <dimen name="player_control_size_medium">56dp</dimen> <!-- 中控制按钮 -->
    <dimen name="player_control_size_large">72dp</dimen>  <!-- 大控制按钮 (播放/暂停) -->
    <dimen name="player_seekbar_height">6dp</dimen>       <!-- 进度条高度 -->
    <dimen name="player_seekbar_thumb">16dp</dimen>       <!-- 进度条拖动点 -->

    <!-- 驾驶模式控件 -->
    <dimen name="driving_control_size">96dp</dimen>       <!-- 驾驶模式控制按钮 -->
    <dimen name="driving_seekbar_height">12dp</dimen>     <!-- 驾驶模式进度条高度 -->
    <dimen name="driving_seekbar_thumb">24dp</dimen>      <!-- 驾驶模式进度条拖动点 -->

    <!-- 动画持续时间 (毫秒) -->
    <integer name="anim_duration_short">150</integer>     <!-- 短动画 -->
    <integer name="anim_duration_medium">300</integer>    <!-- 中等动画 -->
    <integer name="anim_duration_long">500</integer>      <!-- 长动画 -->
</resources>