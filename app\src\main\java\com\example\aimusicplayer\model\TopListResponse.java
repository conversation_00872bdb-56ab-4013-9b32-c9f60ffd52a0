package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class TopListResponse {
    private int code;
    private List<PlayList> list;

    public int getCode() {
        return code;
    }

    public List<PlayList> getList() {
        return list;
    }

    public static class PlayList {
        private long id;
        private String name;
        private String description;
        private long updateTime;
        private String coverImgUrl;
        @SerializedName("trackCount")
        private int songCount;
        private long playCount;
        private boolean subscribed;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public long getUpdateTime() {
            return updateTime;
        }
        
        public String getCoverImgUrl() {
            return coverImgUrl;
        }
        
        public int getSongCount() {
            return songCount;
        }
        
        public long getPlayCount() {
            return playCount;
        }
        
        public boolean isSubscribed() {
            return subscribed;
        }
    }
} 