package com.example.aimusicplayer.ui.intelligence

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.example.aimusicplayer.R
import com.example.aimusicplayer.databinding.FragmentIntelligenceBinding
import com.example.aimusicplayer.ui.adapter.SongAdapter
import com.example.aimusicplayer.utils.showToast
import dagger.hilt.android.AndroidEntryPoint

/**
 * 心动模式Fragment
 * 显示根据当前歌曲推荐的相似歌曲列表
 */
@AndroidEntryPoint
class IntelligenceFragment : Fragment() {

    private var _binding: FragmentIntelligenceBinding? = null
    private val binding get() = _binding!!

    private val viewModel: IntelligenceViewModel by viewModels()
    private val args: IntelligenceFragmentArgs by navArgs()

    private lateinit var songAdapter: SongAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentIntelligenceBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupToolbar()
        setupRecyclerView()
        observeViewModel()

        // 加载心动模式歌曲列表
        viewModel.loadIntelligenceList(args.songId, args.playlistId)
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }

    private fun setupRecyclerView() {
        songAdapter = SongAdapter { song ->
            // 点击歌曲时，跳转到播放页面
            val action = IntelligenceFragmentDirections.actionIntelligenceFragmentToPlayerFragment(
                songId = song.songId,
                playlistId = -1L
            )
            findNavController().navigate(action)
        }

        binding.rvIntelligenceSongs.adapter = songAdapter
    }

    private fun observeViewModel() {
        // 观察当前歌曲信息
        viewModel.currentSong.observe(viewLifecycleOwner) { song ->
            if (song != null) {
                binding.tvSongTitle.text = song.title
                binding.tvArtist.text = song.artist

                Glide.with(this)
                    .load(song.albumCover)
                    .placeholder(R.drawable.default_album_art)
                    .error(R.drawable.default_album_art)
                    .into(binding.ivAlbumCover)
            }
        }

        // 观察心动模式歌曲列表
        viewModel.intelligenceSongs.observe(viewLifecycleOwner) { songs ->
            songAdapter.submitList(songs)

            // 更新UI状态
            binding.tvEmpty.visibility = if (songs.isEmpty()) View.VISIBLE else View.GONE
            binding.rvIntelligenceSongs.visibility = if (songs.isEmpty()) View.GONE else View.VISIBLE
        }

        // 观察加载状态
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                requireContext().showToast(errorMsg)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
