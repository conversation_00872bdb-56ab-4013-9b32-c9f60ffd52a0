package com.example.aimusicplayer.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.model.SearchSuggestResponse.SuggestItem;

import java.util.List;

public class SearchSuggestAdapter extends RecyclerView.Adapter<SearchSuggestAdapter.ViewHolder> {

    public static final int TYPE_SONG = 1;
    public static final int TYPE_ARTIST = 2;
    public static final int TYPE_ALBUM = 3;
    public static final int TYPE_PLAYLIST = 4;

    private List<SuggestItem> suggestItems;
    private int type;
    private OnSuggestItemClickListener listener;

    public interface OnSuggestItemClickListener {
        void onSuggestItemClick(SuggestItem item);
    }

    public SearchSuggestAdapter(List<SuggestItem> suggestItems, int type, OnSuggestItemClickListener listener) {
        this.suggestItems = suggestItems;
        this.type = type;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_search_suggest, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SuggestItem item = suggestItems.get(position);
        
        // 设置图标
        switch (type) {
            case TYPE_SONG:
                holder.typeIconView.setImageResource(android.R.drawable.ic_media_play);
                break;
            case TYPE_ARTIST:
                holder.typeIconView.setImageResource(android.R.drawable.ic_menu_myplaces);
                break;
            case TYPE_ALBUM:
                holder.typeIconView.setImageResource(android.R.drawable.ic_menu_gallery);
                break;
            case TYPE_PLAYLIST:
                holder.typeIconView.setImageResource(android.R.drawable.ic_menu_sort_by_size);
                break;
        }
        
        // 设置标题
        holder.titleTextView.setText(item.getDisplayName());
        
        // 设置副标题（如果是歌曲，显示艺术家信息）
        if (type == TYPE_SONG && item.getArtists() != null && !item.getArtists().isEmpty()) {
            holder.subTextView.setVisibility(View.VISIBLE);
            holder.subTextView.setText(item.getArtistDisplay());
        } else {
            holder.subTextView.setVisibility(View.GONE);
        }
        
        // 点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onSuggestItemClick(item);
            }
        });
    }

    @Override
    public int getItemCount() {
        return suggestItems == null ? 0 : suggestItems.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView typeIconView;
        TextView titleTextView;
        TextView subTextView;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            typeIconView = itemView.findViewById(R.id.typeIconView);
            titleTextView = itemView.findViewById(R.id.suggestTitleTextView);
            subTextView = itemView.findViewById(R.id.suggestSubTextView);
        }
    }
} 