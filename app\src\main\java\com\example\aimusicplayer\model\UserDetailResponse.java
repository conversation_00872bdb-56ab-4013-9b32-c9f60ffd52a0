package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

/**
 * 用户详情响应
 * 对应API: /user/detail
 */
public class UserDetailResponse {
    private int code;
    
    @SerializedName("profile")
    private UserProfile profile;
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public UserProfile getProfile() {
        return profile;
    }
    
    public void setProfile(UserProfile profile) {
        this.profile = profile;
    }
    
    /**
     * 用户资料
     */
    public static class UserProfile {
        @SerializedName("userId")
        private String userId;
        
        @SerializedName("nickname")
        private String nickname;
        
        @SerializedName("avatarUrl")
        private String avatarUrl;
        
        @SerializedName("backgroundUrl")
        private String backgroundUrl;
        
        @SerializedName("signature")
        private String signature;
        
        @SerializedName("followeds")
        private int followeds;
        
        @SerializedName("follows")
        private int follows;
        
        @SerializedName("playlistCount")
        private int playlistCount;
        
        @SerializedName("vipType")
        private int vipType;
        
        @SerializedName("level")
        private int level;
        
        public String getUserId() {
            return userId;
        }
        
        public void setUserId(String userId) {
            this.userId = userId;
        }
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
        
        public String getBackgroundUrl() {
            return backgroundUrl;
        }
        
        public void setBackgroundUrl(String backgroundUrl) {
            this.backgroundUrl = backgroundUrl;
        }
        
        public String getSignature() {
            return signature;
        }
        
        public void setSignature(String signature) {
            this.signature = signature;
        }
        
        public int getFolloweds() {
            return followeds;
        }
        
        public void setFolloweds(int followeds) {
            this.followeds = followeds;
        }
        
        public int getFollows() {
            return follows;
        }
        
        public void setFollows(int follows) {
            this.follows = follows;
        }
        
        public int getPlaylistCount() {
            return playlistCount;
        }
        
        public void setPlaylistCount(int playlistCount) {
            this.playlistCount = playlistCount;
        }
        
        public int getVipType() {
            return vipType;
        }
        
        public void setVipType(int vipType) {
            this.vipType = vipType;
        }
        
        public int getLevel() {
            return level;
        }
        
        public void setLevel(int level) {
            this.level = level;
        }
    }
}
