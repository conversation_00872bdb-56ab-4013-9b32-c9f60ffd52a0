plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'org.jetbrains.kotlin.plugin.parcelize' // 添加Parcelize插件
    id 'com.google.devtools.ksp'
    id 'com.google.dagger.hilt.android'
    id 'androidx.navigation.safeargs.kotlin' // 添加Safe Args插件
}
android {
    namespace 'com.example.aimusicplayer'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.aimusicplayer"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        ndk {
            // 支持所有主要架构，确保应用能在大多数Android设备上运行
            // 包括ARM架构(手机主流)和x86架构(部分平板和模拟器)
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    // 启用ViewBinding
    buildFeatures {
        viewBinding true
    }



    // KSP配置
    ksp {
        // Hilt配置
        arg("dagger.fastInit", "enabled")
        arg("dagger.formatGeneratedSource", "disabled")

        // Room配置
        arg("room.schemaLocation", "$projectDir/schemas")
        arg("room.incremental", "true")

        // 通用配置
        arg("ksp.incremental", "true")
        arg("ksp.incremental.log", "true")
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs', 'src/main/jniLibs']
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    // 移除 androidx.media:media:1.6.0，由 Media3 提供更完整的功能
    // 移除 com.android.volley:volley:1.2.1，统一使用 Retrofit/OkHttp
    implementation 'com.google.code.gson:gson:2.10.1'

    // Retrofit相关依赖 - 更新到最新版本
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:converter-scalars:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // 移除 androidx.mediarouter:mediarouter:1.4.0，由 Media3 提供更完整的功能

    // androidx.media3依赖（替换旧的ExoPlayer）- 更新到最新版本
    implementation 'androidx.media3:media3-exoplayer:1.2.1'
    implementation 'androidx.media3:media3-ui:1.2.1'
    implementation 'androidx.media3:media3-session:1.2.1'
    implementation 'androidx.media3:media3-common:1.2.1'
    implementation 'androidx.media3:media3-datasource-okhttp:1.2.1'

    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.karumi:dexter:6.2.3'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'

    // Glide图片加载库 - 更新到最新版本
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.github.bumptech.glide:okhttp3-integration:4.16.0'
    // 使用KSP版本的Glide编译器
    ksp 'com.github.bumptech.glide:ksp:4.16.0'

    // Palette库 - 用于从图片提取颜色
    implementation 'androidx.palette:palette:1.0.0'

    // Glide变换库 - 用于图片变换，包括模糊处理，替代Blurry库
    implementation 'jp.wasabeef:glide-transformations:4.3.0'

    // ZXing二维码库
    implementation 'com.google.zxing:core:3.4.1'
    implementation 'com.journeyapps:zxing-android-embedded:4.2.0'

    // Hilt依赖注入
    implementation 'com.google.dagger:hilt-android:2.50'
    // 使用KSP版本的Hilt编译器
    ksp 'com.google.dagger:hilt-compiler:2.50'

    // Lifecycle组件 - 更新到最新版本
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'

    // Kotlin协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Lottie动画库
    implementation 'com.airbnb.android:lottie:6.1.0'

    // CircleImageView - 圆形图片控件
    implementation 'de.hdodenhof:circleimageview:3.1.0'

    // Navigation组件 - 只需要ktx版本，它包含了非ktx版本的所有功能
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.5'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.5'

    // Room数据库
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    // 使用KSP版本的Room编译器
    ksp 'androidx.room:room-compiler:2.6.1'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
