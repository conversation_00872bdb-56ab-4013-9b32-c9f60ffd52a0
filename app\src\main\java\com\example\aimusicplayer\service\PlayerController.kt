package com.example.aimusicplayer.service

import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.media3.common.MediaItem
import kotlinx.coroutines.flow.StateFlow

/**
 * 播放控制器接口
 * 定义了播放器的各种控制方法
 */
interface PlayerController {
    val playlist: LiveData<List<MediaItem>>
    val currentSong: LiveData<MediaItem?>
    val playState: StateFlow<PlayState>
    val playProgress: StateFlow<Long>
    val bufferingPercent: StateFlow<Int>
    val playMode: StateFlow<PlayMode>

    @MainThread
    fun addAndPlay(song: MediaItem)

    @MainThread
    fun replaceAll(songList: List<MediaItem>, song: MediaItem)

    @MainThread
    fun play(mediaId: String)

    @MainThread
    fun delete(song: MediaItem)

    @MainThread
    fun clearPlaylist()

    @MainThread
    fun playPause()

    @MainThread
    fun next()

    @MainThread
    fun prev()

    @MainThread
    fun seekTo(msec: Int)

    @MainThread
    fun getAudioSessionId(): Int

    @MainThread
    fun setPlayMode(mode: PlayMode)

    @MainThread
    fun stop()

    /**
     * 获取当前播放列表
     * @return 当前播放列表
     */
    @MainThread
    fun getPlaylist(): List<MediaItem>

    /**
     * 获取当前播放索引
     * @return 当前播放索引
     */
    @MainThread
    fun getCurrentIndex(): Int

    /**
     * 获取当前播放进度
     * @return 当前播放进度（毫秒）
     */
    @MainThread
    fun getCurrentPosition(): Long

    /**
     * 获取当前歌曲总时长
     * @return 当前歌曲总时长（毫秒）
     */
    @MainThread
    fun getDuration(): Long

    @MainThread
    fun playAtIndex(index: Int)
}
