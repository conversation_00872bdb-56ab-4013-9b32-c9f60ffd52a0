package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.load.engine.cache.LruResourceCache
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.module.AppGlideModule
import com.bumptech.glide.request.RequestOptions
import okhttp3.OkHttpClient
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * 自定义Glide模块
 * 用于配置Glide的全局设置，优化图片加载性能
 * 合并了AppGlideModule和CustomGlideModule的功能
 */
@GlideModule
class GlideModule : AppGlideModule() {
    companion object {
        private const val TAG = "GlideModule"

        // 磁盘缓存大小 (50MB)
        private const val DISK_CACHE_SIZE = 50 * 1024 * 1024

        // 连接超时时间 (15秒)
        private const val TIMEOUT = 15L

        /**
         * 获取默认的请求选项
         * 可在应用中统一使用这些选项
         * @return RequestOptions对象
         */
        @JvmStatic
        fun getDefaultRequestOptions(): RequestOptions {
            return RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .centerCrop() // 居中裁剪
                .dontAnimate() // 禁用动画以提高性能
                .dontTransform() // 禁用变换以提高性能
                .encodeFormat(Bitmap.CompressFormat.JPEG) // 使用JPEG格式
                .encodeQuality(85) // 85%质量，平衡大小和质量
        }

        /**
         * 获取专辑封面的请求选项
         * @return RequestOptions对象
         */
        @JvmStatic
        fun getAlbumArtRequestOptions(): RequestOptions {
            return RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL) // 缓存所有版本
                .centerCrop() // 居中裁剪
                .encodeFormat(Bitmap.CompressFormat.JPEG) // 使用JPEG格式
                .encodeQuality(90) // 90%质量，专辑封面需要更高质量
        }

        /**
         * 获取头像的请求选项
         * @return RequestOptions对象
         */
        @JvmStatic
        fun getAvatarRequestOptions(): RequestOptions {
            return RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL) // 缓存所有版本
                .circleCrop() // 圆形裁剪
                .encodeFormat(Bitmap.CompressFormat.JPEG) // 使用JPEG格式
                .encodeQuality(85) // 85%质量
        }
    }

    override fun applyOptions(context: Context, builder: GlideBuilder) {
        // 计算最佳内存缓存大小
        val calculator = MemorySizeCalculator.Builder(context)
            .setMemoryCacheScreens(2f) // 缓存2个屏幕的图片
            .build()

        // 设置内存缓存大小
        builder.setMemoryCache(LruResourceCache(calculator.memoryCacheSize.toLong()))

        // 设置位图池大小
        builder.setBitmapPool(LruBitmapPool(calculator.bitmapPoolSize.toLong()))

        // 设置磁盘缓存大小和位置
        builder.setDiskCache(InternalCacheDiskCacheFactory(context, DISK_CACHE_SIZE.toLong()))

        // 设置默认请求选项
        builder.setDefaultRequestOptions(
            RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565格式减少内存占用
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC) // 自动选择最佳缓存策略
                .skipMemoryCache(false) // 使用内存缓存
        )
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        // 创建OkHttp客户端，设置超时
        val okHttpClient = OkHttpClient.Builder()
            .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
            .build()

        // 注册OkHttp作为Glide的网络组件
        registry.replace(GlideUrl::class.java, InputStream::class.java, OkHttpUrlLoader.Factory(okHttpClient))
    }

    override fun isManifestParsingEnabled(): Boolean {
        // 禁用清单解析以提高性能
        return false
    }
}
