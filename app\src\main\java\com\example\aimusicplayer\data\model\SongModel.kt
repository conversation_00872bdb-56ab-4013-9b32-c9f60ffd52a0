package com.example.aimusicplayer.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 歌曲模型
 * 用于UI展示的歌曲数据模型
 */
@Parcelize
data class SongModel(
    val songId: Long,
    val title: String,
    val artist: String,
    val album: String,
    val albumCover: String?,
    val duration: Long,
    val isVip: Boolean = false,
    val isLocal: Boolean = false,
    val url: String? = null
) : Parcelable {
    companion object {
        /**
         * 从Song转换为SongModel
         */
        fun fromSong(song: Song): SongModel {
            return SongModel(
                songId = song.id,
                title = song.name,
                artist = song.getArtistNames(),
                album = song.getActualAlbum().name,
                albumCover = song.getActualAlbum().picUrl ?: song.picUrl,
                duration = song.dt,
                isVip = false,
                isLocal = false,
                url = null
            )
        }

        /**
         * 从MediaItem转换为SongModel
         */
        fun fromMediaItem(mediaItem: androidx.media3.common.MediaItem): SongModel {
            val metadata = mediaItem.mediaMetadata
            return SongModel(
                songId = mediaItem.mediaId.toLongOrNull() ?: -1L,
                title = metadata.title?.toString() ?: "未知歌曲",
                artist = metadata.artist?.toString() ?: "未知艺术家",
                album = metadata.albumTitle?.toString() ?: "未知专辑",
                albumCover = metadata.artworkUri?.toString(),
                duration = metadata.extras?.getLong("duration") ?: 0L,
                isVip = false,
                isLocal = mediaItem.localConfiguration != null,
                url = mediaItem.localConfiguration?.uri?.toString()
            )
        }
    }
}
