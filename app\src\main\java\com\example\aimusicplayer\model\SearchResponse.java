package com.example.aimusicplayer.model;

import com.example.aimusicplayer.api.ApiResponse;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 搜索响应
 * 使用MVVM架构
 */
public class SearchResponse extends ApiResponse<SearchResponse.Data> {

    public static class Data {
        @SerializedName("result")
        private Result result;

        public Result getResult() {
            return result;
        }

        public void setResult(Result result) {
            this.result = result;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "result=" + result +
                    '}';
        }
    }

    public static class Result {
        @SerializedName("songs")
        private List<Song> songs;

        @SerializedName("songCount")
        private int songCount;

        @SerializedName("hasMore")
        private boolean hasMore;

        public List<Song> getSongs() {
            return songs;
        }

        public void setSongs(List<Song> songs) {
            this.songs = songs;
        }

        public int getSongCount() {
            return songCount;
        }

        public void setSongCount(int songCount) {
            this.songCount = songCount;
        }

        public boolean isHasMore() {
            return hasMore;
        }

        public void setHasMore(boolean hasMore) {
            this.hasMore = hasMore;
        }

        @Override
        public String toString() {
            return "Result{" +
                    "songs=" + songs +
                    ", songCount=" + songCount +
                    ", hasMore=" + hasMore +
                    '}';
        }
    }

    public static class Song {
        @SerializedName("id")
        private String id;

        @SerializedName("name")
        private String name;

        @SerializedName("artists")
        private List<Artist> artists;

        @SerializedName("album")
        private Album album;

        @SerializedName("duration")
        private long duration;

        @SerializedName("fee")
        private int fee;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Artist> getArtists() {
            return artists;
        }

        public void setArtists(List<Artist> artists) {
            this.artists = artists;
        }

        public Album getAlbum() {
            return album;
        }

        public void setAlbum(Album album) {
            this.album = album;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }

        public int getFee() {
            return fee;
        }

        public void setFee(int fee) {
            this.fee = fee;
        }

        /**
         * 获取艺术家显示名称
         * @return 艺术家显示名称
         */
        public String getArtistDisplay() {
            if (artists == null || artists.isEmpty()) {
                return "";
            }

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < artists.size(); i++) {
                if (i > 0) {
                    sb.append("/");
                }
                sb.append(artists.get(i).getName());
            }

            return sb.toString();
        }

        @Override
        public String toString() {
            return "Song{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", artists=" + artists +
                    ", album=" + album +
                    ", duration=" + duration +
                    ", fee=" + fee +
                    '}';
        }
    }

    public static class Artist {
        @SerializedName("id")
        private String id;

        @SerializedName("name")
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "Artist{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    '}';
        }
    }

    public static class Album {
        @SerializedName("id")
        private String id;

        @SerializedName("name")
        private String name;

        @SerializedName("picUrl")
        private String picUrl;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }

        @Override
        public String toString() {
            return "Album{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", picUrl='" + picUrl + '\'' +
                    '}';
        }
    }
}