package com.example.aimusicplayer.ui.intelligence

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.db.entity.SongEntity
import com.example.aimusicplayer.data.model.SongModel
import com.example.aimusicplayer.data.source.MusicDataSource
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.viewmodel.FlowViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 心动模式ViewModel
 * 负责加载和管理心动模式相关数据
 * 使用Flow管理UI状态
 */
@HiltViewModel
class IntelligenceViewModel @Inject constructor(
    application: Application,
    private val musicDataSource: MusicDataSource,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "IntelligenceViewModel"
    }

    // 当前歌曲信息 - 使用StateFlow
    private val _currentSongFlow = MutableStateFlow<SongModel?>(null)
    val currentSongFlow: StateFlow<SongModel?> = _currentSongFlow.asStateFlow()
    val currentSong: LiveData<SongModel?> = currentSongFlow.asLiveData() // 兼容LiveData

    // 心动模式歌曲列表 - 使用StateFlow
    private val _intelligenceSongsFlow = MutableStateFlow<List<SongModel>>(emptyList())
    val intelligenceSongsFlow: StateFlow<List<SongModel>> = _intelligenceSongsFlow.asStateFlow()
    val intelligenceSongs: LiveData<List<SongModel>> = intelligenceSongsFlow.asLiveData() // 兼容LiveData

    /**
     * 加载心动模式歌曲列表
     * @param songId 歌曲ID
     * @param playlistId 歌单ID，可选
     */
    fun loadIntelligenceList(songId: Long, playlistId: Long = -1L) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载心动模式歌曲列表失败", e)
                handleError(e, "加载心动模式歌曲列表失败：${e.message}")
                _intelligenceSongsFlow.value = emptyList()
            }
        ) {
            setLoading(true)
            try {
                // 加载当前歌曲信息
                val song = musicDataSource.getSongByIdFromDb(songId, SongEntity.TYPE_ONLINE)
                if (song != null) {
                    _currentSongFlow.value = SongModel(
                        songId = song.songId,
                        title = song.title,
                        artist = song.artist,
                        album = song.album,
                        albumCover = song.albumCover,
                        duration = song.duration,
                        isVip = song.isVip, // 假设 SongEntity 包含 isVip
                        isLocal = song.type == SongEntity.TYPE_LOCAL,
                        url = song.uri
                    )
                }

                // 加载心动模式歌曲列表
                val songs = if (playlistId > 0) {
                    musicDataSource.getIntelligenceList(songId, playlistId)
                } else {
                    musicDataSource.getIntelligenceList(songId)
                }

                // 转换为SongModel列表
                _intelligenceSongsFlow.value = songs.map { entity ->
                    SongModel(
                        songId = entity.songId,
                        title = entity.title,
                        artist = entity.artist,
                        album = entity.album,
                        albumCover = entity.albumCover,
                        duration = entity.duration,
                        isVip = entity.isVip, // 假设 SongEntity 包含 isVip
                        isLocal = entity.type == SongEntity.TYPE_LOCAL,
                        url = entity.uri
                    )
                }
            } finally {
                setLoading(false)
            }
        }
    }
}
