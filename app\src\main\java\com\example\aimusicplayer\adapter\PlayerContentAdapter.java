package com.example.aimusicplayer.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;

/**
 * 播放器内容适配器
 * 用于ViewPager2，管理歌词、评论和播放列表页面
 */
public class PlayerContentAdapter extends RecyclerView.Adapter<PlayerContentAdapter.PageViewHolder> {

    // 页面类型
    public static final int PAGE_LYRICS = 0;
    public static final int PAGE_COMMENTS = 1;
    public static final int PAGE_PLAYLIST = 2;

    // 页面布局资源ID
    private static final int[] PAGE_LAYOUTS = {
            R.layout.page_player_lyrics,
            R.layout.page_player_comments,
            R.layout.page_player_playlist
    };

    // 页面创建监听器
    private final OnPageCreatedListener listener;

    /**
     * 页面创建监听器接口
     */
    public interface OnPageCreatedListener {
        void onPageCreated(View view, int position);
    }

    public PlayerContentAdapter(OnPageCreatedListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public PageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(PAGE_LAYOUTS[viewType], parent, false);
        return new PageViewHolder(view, viewType);
    }

    @Override
    public void onBindViewHolder(@NonNull PageViewHolder holder, int position) {
        if (listener != null) {
            listener.onPageCreated(holder.itemView, position);
        }
    }

    @Override
    public int getItemCount() {
        return PAGE_LAYOUTS.length;
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

    static class PageViewHolder extends RecyclerView.ViewHolder {
        final int pageType;

        public PageViewHolder(@NonNull View itemView, int pageType) {
            super(itemView);
            this.pageType = pageType;
        }
    }
}
