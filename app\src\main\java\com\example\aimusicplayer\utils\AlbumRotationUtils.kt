package com.example.aimusicplayer.utils

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.LinearInterpolator

/**
 * 专辑封面旋转动画工具类
 * 用于实现黑胶唱片专辑封面旋转动画
 */
object AlbumRotationUtils {
    // 存储每个View的旋转动画
    private val rotationAnimators = mutableMapOf<Int, ObjectAnimator>()
    
    // 存储每个View的当前旋转角度
    private val rotationAngles = mutableMapOf<Int, Float>()
    
    // 默认旋转时长（一圈）
    private const val DEFAULT_ROTATION_DURATION = 20000L // 20秒
    
    /**
     * 开始旋转动画
     * @param view 需要旋转的View
     * @param duration 旋转一圈的时长，默认20秒
     */
    fun startRotation(view: View, duration: Long = DEFAULT_ROTATION_DURATION) {
        val viewId = view.id
        
        // 如果已经有动画在运行，先停止
        stopRotation(view)
        
        // 获取当前旋转角度，如果没有记录则为0
        val currentRotation = rotationAngles[viewId] ?: 0f
        
        // 创建旋转动画
        val animator = ObjectAnimator.ofFloat(view, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            this.duration = duration
            interpolator = LinearInterpolator()
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.RESTART
            
            // 监听动画更新，记录当前角度
            addUpdateListener { animation ->
                val value = animation.animatedValue as Float
                rotationAngles[viewId] = value % 360
            }
        }
        
        // 保存动画
        rotationAnimators[viewId] = animator
        
        // 开始动画
        animator.start()
    }
    
    /**
     * 暂停旋转动画
     * @param view 需要暂停旋转的View
     */
    fun pauseRotation(view: View) {
        val viewId = view.id
        rotationAnimators[viewId]?.pause()
    }
    
    /**
     * 恢复旋转动画
     * @param view 需要恢复旋转的View
     */
    fun resumeRotation(view: View) {
        val viewId = view.id
        rotationAnimators[viewId]?.resume()
    }
    
    /**
     * 停止旋转动画
     * @param view 需要停止旋转的View
     */
    fun stopRotation(view: View) {
        val viewId = view.id
        rotationAnimators[viewId]?.cancel()
        rotationAnimators.remove(viewId)
    }
    
    /**
     * 重置旋转角度
     * @param view 需要重置的View
     */
    fun resetRotation(view: View) {
        val viewId = view.id
        
        // 停止动画
        stopRotation(view)
        
        // 重置角度
        view.rotation = 0f
        rotationAngles[viewId] = 0f
    }
    
    /**
     * 设置旋转角度
     * @param view 需要设置角度的View
     * @param angle 角度值
     */
    fun setRotation(view: View, angle: Float) {
        val viewId = view.id
        view.rotation = angle
        rotationAngles[viewId] = angle
    }
    
    /**
     * 获取当前旋转角度
     * @param view 需要获取角度的View
     * @return 当前角度
     */
    fun getRotation(view: View): Float {
        val viewId = view.id
        return rotationAngles[viewId] ?: 0f
    }
    
    /**
     * 清理资源
     */
    fun clear() {
        for (animator in rotationAnimators.values) {
            animator.cancel()
        }
        rotationAnimators.clear()
        rotationAngles.clear()
    }
}
