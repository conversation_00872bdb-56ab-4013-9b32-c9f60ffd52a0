<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:ordering="together">
    
    <!-- 缩放动画 -->
    <scale
        android:duration="300"
        android:fromXScale="1.0"
        android:fromYScale="1.0"
        android:pivotX="50%"
        android:pivotY="50%"
        android:toXScale="1.3"
        android:toYScale="1.3"
        android:repeatMode="reverse"
        android:repeatCount="1"
        android:interpolator="@android:interpolator/overshoot" />
    
    <!-- 旋转动画 -->
    <rotate
        android:duration="300"
        android:fromDegrees="0"
        android:toDegrees="15"
        android:pivotX="50%"
        android:pivotY="50%"
        android:repeatMode="reverse"
        android:repeatCount="1"
        android:interpolator="@android:interpolator/accelerate_decelerate" />
</set>
