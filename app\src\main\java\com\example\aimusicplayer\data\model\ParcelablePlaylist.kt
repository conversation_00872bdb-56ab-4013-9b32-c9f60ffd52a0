package com.example.aimusicplayer.data.model

import android.os.Parcelable
import com.example.aimusicplayer.data.db.entity.PlaylistEntity
import kotlinx.parcelize.Parcelize

/**
 * 可序列化的歌单模型
 * 用于在Fragment/Activity之间传递歌单数据
 */
@Parcelize
data class ParcelablePlaylist(
    // 歌单ID
    val playlistId: Long,
    
    // 歌单名称
    val name: String,
    
    // 歌单封面
    val coverUrl: String,
    
    // 歌单描述
    val description: String = "",
    
    // 创建者ID
    val creatorId: Long = 0,
    
    // 创建者名称
    val creatorName: String = "",
    
    // 歌曲数量
    val songCount: Int = 0,
    
    // 播放次数
    val playCount: Int = 0,
    
    // 是否已收藏
    val isSubscribed: Boolean = false,
    
    // 创建时间
    val createTime: Long = System.currentTimeMillis(),
    
    // 更新时间
    val updateTime: Long = System.currentTimeMillis(),
    
    // 是否为本地歌单
    val isLocal: Boolean = false,
    
    // 歌曲列表
    val songs: List<ParcelableSong> = emptyList()
) : Parcelable {

    /**
     * 转换为PlaylistEntity
     */
    fun toPlaylistEntity(): PlaylistEntity {
        return PlaylistEntity(
            playlistId = playlistId,
            name = name,
            coverUrl = coverUrl,
            description = description,
            creatorId = creatorId,
            creatorName = creatorName,
            songCount = songCount,
            playCount = playCount,
            isSubscribed = isSubscribed,
            createTime = createTime,
            updateTime = updateTime,
            isLocal = isLocal
        )
    }

    companion object {
        /**
         * 从PlaylistEntity转换
         */
        fun fromPlaylistEntity(entity: PlaylistEntity, songs: List<ParcelableSong> = emptyList()): ParcelablePlaylist {
            return ParcelablePlaylist(
                playlistId = entity.playlistId,
                name = entity.name,
                coverUrl = entity.coverUrl,
                description = entity.description,
                creatorId = entity.creatorId,
                creatorName = entity.creatorName,
                songCount = entity.songCount,
                playCount = entity.playCount,
                isSubscribed = entity.isSubscribed,
                createTime = entity.createTime,
                updateTime = entity.updateTime,
                isLocal = entity.isLocal,
                songs = songs
            )
        }
    }
}
