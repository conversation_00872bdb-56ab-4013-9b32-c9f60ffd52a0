package com.example.aimusicplayer.model;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;

import java.io.Serializable;

/**
 * 用户资料模型类
 * 包含用户基本信息，如ID、用户名、头像URL等
 * 实现Serializable接口，方便序列化和反序列化
 */
public class UserProfile implements Serializable {
    private static final long serialVersionUID = 1L;
    private static final String TAG = "UserProfile";
    
    // 用户基本信息
    private String userId;          // 用户ID
    private String nickname;        // 用户昵称
    private String avatarUrl;       // 头像URL
    private String backgroundUrl;   // 背景图URL
    private String signature;       // 个性签名
    private int gender;             // 性别：0-未知，1-男，2-女
    private int userType;           // 用户类型：0-普通用户，1-VIP，2-达人
    private int vipType;            // VIP类型
    private long createTime;        // 账号创建时间
    private long lastLoginTime;     // 最后登录时间
    private boolean isLoggedIn;     // 是否已登录
    
    // 统计数据
    private int level;              // 用户等级
    private int listenSongs;        // 听歌数量
    
    // SharedPreferences键名
    private static final String PREFS_NAME = "user_profile";
    private static final String KEY_USER_PROFILE = "user_profile_json";
    
    /**
     * 默认构造函数
     */
    public UserProfile() {
        this.isLoggedIn = false;
    }
    
    /**
     * 带参数的构造函数
     */
    public UserProfile(String userId, String nickname, String avatarUrl) {
        this.userId = userId;
        this.nickname = nickname;
        this.avatarUrl = avatarUrl;
        this.isLoggedIn = true;
        this.lastLoginTime = System.currentTimeMillis();
    }
    
    /**
     * 完整的构造函数
     */
    public UserProfile(String userId, String nickname, String avatarUrl, String backgroundUrl, 
                      String signature, int gender, int userType, int vipType, 
                      long createTime, int level, int listenSongs) {
        this.userId = userId;
        this.nickname = nickname;
        this.avatarUrl = avatarUrl;
        this.backgroundUrl = backgroundUrl;
        this.signature = signature;
        this.gender = gender;
        this.userType = userType;
        this.vipType = vipType;
        this.createTime = createTime;
        this.level = level;
        this.listenSongs = listenSongs;
        this.isLoggedIn = true;
        this.lastLoginTime = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getBackgroundUrl() {
        return backgroundUrl;
    }
    
    public void setBackgroundUrl(String backgroundUrl) {
        this.backgroundUrl = backgroundUrl;
    }
    
    public String getSignature() {
        return signature;
    }
    
    public void setSignature(String signature) {
        this.signature = signature;
    }
    
    public int getGender() {
        return gender;
    }
    
    public void setGender(int gender) {
        this.gender = gender;
    }
    
    public int getUserType() {
        return userType;
    }
    
    public void setUserType(int userType) {
        this.userType = userType;
    }
    
    public int getVipType() {
        return vipType;
    }
    
    public void setVipType(int vipType) {
        this.vipType = vipType;
    }
    
    public long getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }
    
    public long getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(long lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    public boolean isLoggedIn() {
        return isLoggedIn;
    }
    
    public void setLoggedIn(boolean loggedIn) {
        isLoggedIn = loggedIn;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public int getListenSongs() {
        return listenSongs;
    }
    
    public void setListenSongs(int listenSongs) {
        this.listenSongs = listenSongs;
    }
    
    /**
     * 保存用户资料到SharedPreferences
     */
    public void saveToPrefs(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        Gson gson = new Gson();
        String json = gson.toJson(this);
        
        editor.putString(KEY_USER_PROFILE, json);
        editor.apply();
        
        Log.d(TAG, "用户资料已保存: " + nickname);
    }
    
    /**
     * 从SharedPreferences加载用户资料
     */
    public static UserProfile loadFromPrefs(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String json = prefs.getString(KEY_USER_PROFILE, "");
        
        if (TextUtils.isEmpty(json)) {
            Log.d(TAG, "没有保存的用户资料");
            return new UserProfile(); // 返回一个未登录的用户资料
        }
        
        try {
            Gson gson = new Gson();
            UserProfile profile = gson.fromJson(json, UserProfile.class);
            Log.d(TAG, "已加载用户资料: " + profile.getNickname());
            return profile;
        } catch (Exception e) {
            Log.e(TAG, "加载用户资料失败", e);
            return new UserProfile(); // 出错时返回一个未登录的用户资料
        }
    }
    
    /**
     * 清除保存的用户资料
     */
    public static void clearFromPrefs(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(KEY_USER_PROFILE);
        editor.apply();
        
        Log.d(TAG, "用户资料已清除");
    }
    
    @Override
    public String toString() {
        return "UserProfile{" +
                "userId='" + userId + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", isLoggedIn=" + isLoggedIn +
                ", level=" + level +
                '}';
    }
}
