package com.example.aimusicplayer.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index

/**
 * 歌单与歌曲的多对多关系表
 */
@Entity(
    tableName = "playlist_song_cross_ref",
    primaryKeys = ["playlist_id", "song_unique_id"],
    indices = [
        Index("playlist_id"),
        Index("song_unique_id")
    ]
)
data class PlaylistSongCrossRef(
    @ColumnInfo("playlist_id")
    val playlistId: Long,

    @ColumnInfo("song_unique_id")
    val songUniqueId: String,

    // 在歌单中的排序位置
    @ColumnInfo("sort_order")
    val sortOrder: Int = 0,

    // 添加时间
    @ColumnInfo("add_time")
    val addTime: Long = System.currentTimeMillis()
)
