package com.example.aimusicplayer.model;

import com.example.aimusicplayer.api.ApiResponse;
import com.google.gson.annotations.SerializedName;

/**
 * 获取二维码 Key 的响应模型
 * 使用MVVM架构
 */
public class QrKeyResponse extends ApiResponse<QrKeyResponse.Data> {

    public static class Data {
        @SerializedName("unikey")
        private String unikey;

        public String getUnikey() {
            return unikey;
        }

        public void setUnikey(String unikey) {
            this.unikey = unikey;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "unikey='" + unikey + '\'' +
                    '}';
        }
    }
}