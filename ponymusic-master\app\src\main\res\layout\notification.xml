<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:divider="@drawable/common_ic_divider_flexbox_12"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:showDividers="middle">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:contentDescription="@null"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_launcher" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fadingEdge="horizontal"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Compat.Notification.Title.Media"
            tools:text="发如雪" />

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fadingEdge="horizontal"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.Compat.Notification.Info.Media"
            tools:text="周杰伦" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_play_pause"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:contentDescription="@null"
        android:src="@drawable/ic_notification_pause" />

    <ImageView
        android:id="@+id/iv_next"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:contentDescription="@null"
        android:src="@drawable/ic_notification_next" />
</LinearLayout>