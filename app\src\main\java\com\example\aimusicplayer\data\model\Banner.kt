package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * Banner数据模型
 */
data class Banner(
    var id: String = "",
    var imageUrl: String = "",
    var targetUrl: String = "",
    var title: String = "",
    var type: Int = 0, // 1: 歌曲, 2: 专辑, 3: 歌单, 4: 外部链接
    var targetId: String = "",
    
    @SerializedName("titleColor")
    var titleColor: String = "",
    
    @SerializedName("typeTitle")
    var typeTitle: String = "",
    
    @SerializedName("url")
    var url: String = "",
    
    @SerializedName("targetType")
    var targetType: Int = 0
) {
    /**
     * 从Java版本的Banner创建
     */
    companion object {
        @JvmStatic
        fun fromJavaBanner(banner: com.example.aimusicplayer.model.Banner): Banner {
            return Banner(
                id = banner.id ?: "",
                imageUrl = banner.imageUrl ?: "",
                targetUrl = banner.targetUrl ?: "",
                title = banner.title ?: "",
                type = banner.type,
                targetId = banner.targetId ?: ""
            )
        }
        
        @JvmStatic
        fun fromJavaBannerResponse(banner: Banner): Banner {
            return Banner(
                id = "",
                imageUrl = banner.imageUrl,
                targetUrl = banner.url,
                title = banner.typeTitle,
                type = banner.targetType,
                targetId = banner.targetId,
                titleColor = banner.titleColor,
                typeTitle = banner.typeTitle,
                url = banner.url
            )
        }
    }
    
    /**
     * 转换为Java版本的Banner
     */
    fun toJavaBanner(): com.example.aimusicplayer.model.Banner {
        val javaBanner = com.example.aimusicplayer.model.Banner()
        javaBanner.id = id
        javaBanner.imageUrl = imageUrl
        javaBanner.targetUrl = targetUrl
        javaBanner.title = title
        javaBanner.type = type
        javaBanner.targetId = targetId
        return javaBanner
    }
}
