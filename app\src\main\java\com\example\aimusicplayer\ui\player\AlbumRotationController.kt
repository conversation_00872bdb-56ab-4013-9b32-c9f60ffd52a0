package com.example.aimusicplayer.ui.player

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.LinearInterpolator
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 专辑旋转控制器
 * 用于控制黑胶唱片专辑封面旋转动画
 */
@Singleton
class AlbumRotationController @Inject constructor() {
    
    companion object {
        private const val TAG = "AlbumRotationController"
        
        // 旋转动画持续时间（毫秒）
        private const val ROTATION_DURATION = 30000L
        
        // 旋转属性
        private const val ROTATION = "rotation"
        
        // 淡入淡出动画持续时间（毫秒）
        private const val FADE_DURATION = 500L
        
        // 透明度属性
        private const val ALPHA = "alpha"
    }
    
    // 旋转动画
    private var rotationAnimator: ObjectAnimator? = null
    
    // 当前旋转角度
    private var currentRotation = 0f
    
    // 是否正在播放
    private var isPlaying = false
    
    /**
     * 开始旋转动画
     * @param view 目标视图
     * @param fromScratch 是否从头开始
     */
    fun startRotation(view: View, fromScratch: Boolean = false) {
        // 如果已经在播放，不重复创建动画
        if (isPlaying && rotationAnimator?.isRunning == true) {
            return
        }
        
        // 如果需要从头开始，重置角度
        if (fromScratch) {
            currentRotation = 0f
            view.rotation = 0f
        }
        
        // 创建旋转动画
        rotationAnimator = ObjectAnimator.ofFloat(view, ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ROTATION_DURATION
            interpolator = LinearInterpolator()
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.RESTART
            
            // 监听动画更新，记录当前角度
            addUpdateListener { animation ->
                currentRotation = animation.animatedValue as Float
            }
            
            // 开始动画
            start()
        }
        
        isPlaying = true
    }
    
    /**
     * 暂停旋转动画
     */
    fun pauseRotation() {
        rotationAnimator?.pause()
        isPlaying = false
    }
    
    /**
     * 恢复旋转动画
     */
    fun resumeRotation() {
        rotationAnimator?.resume()
        isPlaying = true
    }
    
    /**
     * 停止旋转动画
     */
    fun stopRotation() {
        rotationAnimator?.cancel()
        rotationAnimator = null
        isPlaying = false
    }
    
    /**
     * 切换播放状态
     * @param view 目标视图
     * @param isPlaying 是否播放
     */
    fun togglePlayState(view: View, isPlaying: Boolean) {
        if (isPlaying) {
            if (this.isPlaying) {
                resumeRotation()
            } else {
                startRotation(view)
            }
        } else {
            pauseRotation()
        }
        
        this.isPlaying = isPlaying
    }
    
    /**
     * 切换歌曲
     * @param view 目标视图
     * @param isPlaying 是否播放
     */
    fun switchSong(view: View, isPlaying: Boolean) {
        // 停止当前动画
        stopRotation()
        
        // 重置角度
        currentRotation = 0f
        view.rotation = 0f
        
        // 如果正在播放，开始新的动画
        if (isPlaying) {
            startRotation(view, true)
        }
        
        this.isPlaying = isPlaying
    }
    
    /**
     * 淡入动画
     * @param view 目标视图
     * @param duration 动画持续时间
     * @param onEnd 动画结束回调
     */
    fun fadeIn(view: View, duration: Long = FADE_DURATION, onEnd: (() -> Unit)? = null) {
        view.alpha = 0f
        view.visibility = View.VISIBLE
        
        ObjectAnimator.ofFloat(view, ALPHA, 0f, 1f).apply {
            this.duration = duration
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    onEnd?.invoke()
                }
            })
            start()
        }
    }
    
    /**
     * 淡出动画
     * @param view 目标视图
     * @param duration 动画持续时间
     * @param onEnd 动画结束回调
     */
    fun fadeOut(view: View, duration: Long = FADE_DURATION, onEnd: (() -> Unit)? = null) {
        ObjectAnimator.ofFloat(view, ALPHA, 1f, 0f).apply {
            this.duration = duration
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    onEnd?.invoke()
                }
            })
            start()
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        stopRotation()
    }
}
