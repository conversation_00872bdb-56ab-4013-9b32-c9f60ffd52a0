package com.example.aimusicplayer.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.model.OnlineSong;

import java.util.List;

public class OnlineSongAdapter extends RecyclerView.Adapter<OnlineSongAdapter.SongViewHolder> {

    private List<OnlineSong> songs;
    private OnSongClickListener listener;

    public OnlineSongAdapter(List<OnlineSong> songs, OnSongClickListener listener) {
        this.songs = songs;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SongViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_online_song, parent, false);
        return new SongViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull SongViewHolder holder, int position) {
        OnlineSong song = songs.get(position);
        holder.songNameTextView.setText(song.getName());
        holder.artistTextView.setText(song.getArtist());
        holder.albumTextView.setText(song.getAlbumName());
        
        // 设置VIP标签不可见
        holder.vipTagTextView.setVisibility(View.GONE);
    }

    @Override
    public int getItemCount() {
        return songs.size();
    }

    public class SongViewHolder extends RecyclerView.ViewHolder {
        TextView songNameTextView;
        TextView artistTextView;
        TextView albumTextView;
        TextView vipTagTextView;

        public SongViewHolder(@NonNull View itemView) {
            super(itemView);
            songNameTextView = itemView.findViewById(R.id.item_song_name);
            artistTextView = itemView.findViewById(R.id.item_artist_name);
            albumTextView = itemView.findViewById(R.id.item_album_name);
            vipTagTextView = itemView.findViewById(R.id.item_vip_tag);

            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onSongClick(position);
                }
            });
        }
    }

    public interface OnSongClickListener {
        void onSongClick(int position);
    }
} 