package com.example.aimusicplayer.data.model

import java.util.Date

/**
 * 评论数据模型
 */
data class Comment(
    val commentId: Long,
    val userId: Long,
    val username: String,
    val avatarUrl: String,
    val content: String,
    val createTime: Date,
    val likeCount: Int,
    val liked: Boolean,
    val replyCount: Int = 0,
    val replies: List<Reply> = emptyList()
)

/**
 * 回复数据模型
 */
data class Reply(
    val replyId: Long,
    val commentId: Long,
    val userId: Long,
    val username: String,
    val avatarUrl: String,
    val content: String,
    val createTime: Date,
    val likeCount: Int,
    val liked: Boolean
)
