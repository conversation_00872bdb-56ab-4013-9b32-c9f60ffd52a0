package com.example.aimusicplayer.utils

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.view.animation.OvershootInterpolator
import androidx.core.view.ViewCompat

/**
 * 按钮动画工具类
 * 提供各种按钮动画效果，优化用户交互体验
 * 特别适合车载场景的大按钮交互
 */
object ButtonAnimationUtils {

    /**
     * 为视图添加按下缩小、松开弹回的动画效果
     * 适用于所有类型的按钮和可点击视图
     *
     * @param view 需要添加动画的视图
     * @param scaleRatio 按下时缩小的比例，默认为0.9
     * @param duration 动画持续时间，默认为150毫秒
     * @param enableVibration 是否启用触觉反馈，默认为true
     * @param vibrateOnActionDown 是否在按下时触发振动，默认为true
     * @param vibrateOnActionUp 是否在松开时触发振动，默认为false
     */
    fun addScaleAnimation(
        view: View,
        scaleRatio: Float = 0.9f,
        duration: Long = 150,
        enableVibration: Boolean = true,
        vibrateOnActionDown: Boolean = true,
        vibrateOnActionUp: Boolean = false
    ) {
        view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时缩小
                    AnimatorSet().apply {
                        playTogether(
                            ObjectAnimator.ofFloat(v, View.SCALE_X, 1f, scaleRatio),
                            ObjectAnimator.ofFloat(v, View.SCALE_Y, 1f, scaleRatio)
                        )
                        this.duration = duration
                        interpolator = DecelerateInterpolator()
                        start()
                    }

                    // 触觉反馈
                    if (enableVibration && vibrateOnActionDown) {
                        provideVibrationFeedback(v.context, 20)
                    }

                    // 不消费事件，允许点击事件继续传递
                    false
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 松开或取消时恢复
                    AnimatorSet().apply {
                        playTogether(
                            ObjectAnimator.ofFloat(v, View.SCALE_X, scaleRatio, 1f),
                            ObjectAnimator.ofFloat(v, View.SCALE_Y, scaleRatio, 1f)
                        )
                        this.duration = duration
                        interpolator = OvershootInterpolator(1.5f) // 添加弹性效果
                        start()
                    }

                    // 触觉反馈
                    if (enableVibration && vibrateOnActionUp) {
                        provideVibrationFeedback(v.context, 10)
                    }

                    // 不消费事件，允许点击事件继续传递
                    false
                }
                else -> false
            }
        }
    }

    /**
     * 为视图添加波纹效果和缩放动画
     * 适用于Material Design风格的按钮
     *
     * @param view 需要添加动画的视图
     * @param scaleRatio 按下时缩小的比例，默认为0.95
     * @param duration 动画持续时间，默认为150毫秒
     * @param enableVibration 是否启用触觉反馈，默认为true
     */
    fun addRippleScaleAnimation(
        view: View,
        scaleRatio: Float = 0.95f,
        duration: Long = 150,
        enableVibration: Boolean = true
    ) {
        // 确保视图有波纹效果
        ViewCompat.setBackground(view, view.background)

        // 添加触摸监听器
        view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时缩小
                    AnimatorSet().apply {
                        playTogether(
                            ObjectAnimator.ofFloat(v, View.SCALE_X, 1f, scaleRatio),
                            ObjectAnimator.ofFloat(v, View.SCALE_Y, 1f, scaleRatio)
                        )
                        this.duration = duration
                        interpolator = DecelerateInterpolator()
                        start()
                    }

                    // 触觉反馈
                    if (enableVibration) {
                        provideVibrationFeedback(v.context, 20)
                    }

                    // 不消费事件，允许波纹效果显示
                    false
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 松开或取消时恢复
                    AnimatorSet().apply {
                        playTogether(
                            ObjectAnimator.ofFloat(v, View.SCALE_X, scaleRatio, 1f),
                            ObjectAnimator.ofFloat(v, View.SCALE_Y, scaleRatio, 1f)
                        )
                        this.duration = duration
                        interpolator = DecelerateInterpolator()
                        start()
                    }

                    // 不消费事件，允许点击事件继续传递
                    false
                }
                else -> false
            }
        }
    }

    /**
     * 提供触觉反馈
     *
     * @param context 上下文
     * @param duration 振动持续时间，单位毫秒
     * @param amplitude 振动强度，范围0-255，默认为VibrationEffect.DEFAULT_AMPLITUDE
     */
    private fun provideVibrationFeedback(
        context: Context,
        duration: Long,
        amplitude: Int = VibrationEffect.DEFAULT_AMPLITUDE
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
            vibrator?.vibrate(VibrationEffect.createOneShot(duration, amplitude))
        } else {
            @Suppress("DEPRECATION")
            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
            vibrator?.vibrate(duration)
        }
    }
}
