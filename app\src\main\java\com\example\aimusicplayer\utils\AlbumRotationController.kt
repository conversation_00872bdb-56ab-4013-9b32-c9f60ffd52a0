package com.example.aimusicplayer.utils

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.os.Build
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 专辑封面旋转控制器
 * 负责专辑封面和黑胶唱片的旋转动画
 * 使用Flow管理状态
 * 优化后的版本，参考云音乐黑胶唱片效果
 */
@Singleton
class AlbumRotationController @Inject constructor() {
    companion object {
        private const val TAG = "AlbumRotationController"

        // 动画常量
        private const val ALBUM_ROTATION_DURATION = 24000L // 24秒旋转一圈
        private const val VINYL_ROTATION_DURATION = 18000L // 18秒旋转一圈
        private const val NEEDLE_ANIMATION_DURATION = 800L // 唱针动画时长
        private const val NEEDLE_ROTATION_PLAY = 0f // 播放时唱针角度
        private const val NEEDLE_ROTATION_PAUSE = -30f // 暂停时唱针角度
    }

    // 专辑封面旋转动画
    private var albumRotationAnimator: ObjectAnimator? = null

    // 黑胶唱片旋转动画
    private var vinylRotationAnimator: ObjectAnimator? = null

    // 唱针动画
    private var needleAnimator: ObjectAnimator? = null

    // 动画状态的StateFlow
    private val _isAnimatingFlow = MutableStateFlow(false)
    val isAnimatingFlow: StateFlow<Boolean> = _isAnimatingFlow.asStateFlow()

    // 兼容LiveData
    val isAnimating: LiveData<Boolean> = MutableLiveData(_isAnimatingFlow.value)

    // UI组件
    private var albumArt: ImageView? = null
    private var vinylBackground: ImageView? = null
    private var vinylArm: ImageView? = null

    /**
     * 初始化UI组件
     * @param albumArt 专辑封面ImageView
     * @param vinylBackground 黑胶唱片ImageView
     * @param vinylArm 唱针ImageView (可选)
     */
    fun initializeViews(albumArt: ImageView, vinylBackground: ImageView, vinylArm: ImageView? = null) {
        this.albumArt = albumArt
        this.vinylBackground = vinylBackground
        this.vinylArm = vinylArm

        // 启用硬件加速以提高动画流畅度
        albumArt.apply {
            setLayerType(View.LAYER_TYPE_HARDWARE, null)
            pivotX = width / 2f
            pivotY = height / 2f
        }

        vinylBackground.apply {
            setLayerType(View.LAYER_TYPE_HARDWARE, null)
            pivotX = width / 2f
            pivotY = height / 2f
        }

        vinylArm?.apply {
            setLayerType(View.LAYER_TYPE_HARDWARE, null)
            // 设置唱针旋转中心点
            pivotX = 0f
            pivotY = 0f
            rotation = NEEDLE_ROTATION_PAUSE // 初始状态为抬起
        }

        // 创建专辑封面无限旋转动画
        albumRotationAnimator = ObjectAnimator.ofFloat(albumArt, View.ROTATION, 0f, 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            interpolator = LinearInterpolator() // 线性插值器，匀速旋转
            repeatCount = ValueAnimator.INFINITE // 无限循环
            repeatMode = ValueAnimator.RESTART // 重新开始模式

            // 使用属性动画的RenderThread渲染
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                setAutoCancel(true)
            }
        }

        // 创建黑胶唱片无限旋转动画
        vinylRotationAnimator = ObjectAnimator.ofFloat(vinylBackground, View.ROTATION, 0f, 360f).apply {
            duration = VINYL_ROTATION_DURATION
            interpolator = LinearInterpolator() // 线性插值器，匀速旋转
            repeatCount = ValueAnimator.INFINITE // 无限循环
            repeatMode = ValueAnimator.RESTART // 重新开始模式

            // 使用属性动画的RenderThread渲染
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                setAutoCancel(true)
            }
        }
    }

    /**
     * 启动旋转动画
     * 使用平滑的过渡效果
     * 参考ponymusic-master项目的实现，优化启动效果
     */
    fun startRotationAnimations() {
        val albumArt = this.albumArt
        val vinylBackground = this.vinylBackground

        if (albumArt == null || vinylBackground == null) {
            Log.e(TAG, "无法启动旋转动画：UI组件为null")
            return
        }

        // 如果动画已经在运行，不需要重新启动
        if ((albumRotationAnimator?.isRunning == true) || (vinylRotationAnimator?.isRunning == true)) {
            return
        }

        // 保存当前旋转角度
        val albumCurrentRotation = albumArt.rotation
        val vinylCurrentRotation = vinylBackground.rotation

        // 取消之前的动画
        albumRotationAnimator?.cancel()
        vinylRotationAnimator?.cancel()

        // 播放唱针放下动画
        animateNeedle(true)

        // 创建加速启动动画
        // 专辑封面加速启动
        val albumStartAnimator = ObjectAnimator.ofFloat(
            albumArt,
            View.ROTATION,
            albumCurrentRotation,
            albumCurrentRotation + 40f // 先旋转一小段距离
        ).apply {
            duration = 800 // 800毫秒加速
            interpolator = AccelerateDecelerateInterpolator() // 先加速后减速

            // 动画结束后启动无限旋转
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 获取当前旋转角度
                    val endRotation = albumArt.rotation

                    // 创建无限旋转动画
                    albumRotationAnimator = ObjectAnimator.ofFloat(
                        albumArt,
                        View.ROTATION,
                        endRotation,
                        endRotation + 360f
                    ).apply {
                        duration = ALBUM_ROTATION_DURATION
                        interpolator = LinearInterpolator()
                        repeatCount = ValueAnimator.INFINITE
                        repeatMode = ValueAnimator.RESTART
                        start()
                    }
                }
            })
        }

        // 黑胶唱片加速启动
        val vinylStartAnimator = ObjectAnimator.ofFloat(
            vinylBackground,
            View.ROTATION,
            vinylCurrentRotation,
            vinylCurrentRotation + 50f // 先旋转一小段距离
        ).apply {
            duration = 800 // 800毫秒加速
            interpolator = AccelerateDecelerateInterpolator() // 先加速后减速

            // 动画结束后启动无限旋转
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 获取当前旋转角度
                    val endRotation = vinylBackground.rotation

                    // 创建无限旋转动画
                    vinylRotationAnimator = ObjectAnimator.ofFloat(
                        vinylBackground,
                        View.ROTATION,
                        endRotation,
                        endRotation + 360f
                    ).apply {
                        duration = VINYL_ROTATION_DURATION
                        interpolator = LinearInterpolator()
                        repeatCount = ValueAnimator.INFINITE
                        repeatMode = ValueAnimator.RESTART
                        start()
                    }
                }
            })
        }

        // 启用硬件加速
        albumArt.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        vinylBackground.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        // 启动加速动画
        albumStartAnimator.start()
        vinylStartAnimator.start()

        // 更新状态
        _isAnimatingFlow.value = true
    }

    /**
     * 唱针动画
     * 参考ponymusic-master项目的实现，优化唱针动画效果
     * @param isPlaying 是否正在播放
     */
    private fun animateNeedle(isPlaying: Boolean) {
        val vinylArm = this.vinylArm ?: return

        // 取消之前的动画
        needleAnimator?.cancel()

        // 创建新的动画集合
        val animatorSet = AnimatorSet()

        if (isPlaying) {
            // 唱臂放下动画 - 添加弹性效果
            // 先快速下降到接近播放位置
            val firstPhase = ObjectAnimator.ofFloat(
                vinylArm,
                View.ROTATION,
                NEEDLE_ROTATION_PAUSE,
                NEEDLE_ROTATION_PLAY - 2f // 稍微超过目标位置
            ).apply {
                duration = NEEDLE_ANIMATION_DURATION * 2 / 3 // 占总时长的2/3
                interpolator = AccelerateDecelerateInterpolator() // 先加速后减速
            }

            // 然后轻微回弹到最终位置
            val secondPhase = ObjectAnimator.ofFloat(
                vinylArm,
                View.ROTATION,
                NEEDLE_ROTATION_PLAY - 2f,
                NEEDLE_ROTATION_PLAY
            ).apply {
                duration = NEEDLE_ANIMATION_DURATION / 3 // 占总时长的1/3
                interpolator = DecelerateInterpolator(1.5f) // 强减速，模拟弹性
            }

            // 按顺序播放两个动画
            animatorSet.playSequentially(firstPhase, secondPhase)
        } else {
            // 唱臂抬起动画 - 添加惯性效果
            // 先缓慢开始抬起
            val firstPhase = ObjectAnimator.ofFloat(
                vinylArm,
                View.ROTATION,
                NEEDLE_ROTATION_PLAY,
                NEEDLE_ROTATION_PLAY + (NEEDLE_ROTATION_PAUSE - NEEDLE_ROTATION_PLAY) * 0.3f // 移动30%的距离
            ).apply {
                duration = NEEDLE_ANIMATION_DURATION / 3 // 占总时长的1/3
                interpolator = DecelerateInterpolator() // 缓慢开始
            }

            // 然后加速完成剩余的抬起动作
            val secondPhase = ObjectAnimator.ofFloat(
                vinylArm,
                View.ROTATION,
                NEEDLE_ROTATION_PLAY + (NEEDLE_ROTATION_PAUSE - NEEDLE_ROTATION_PLAY) * 0.3f,
                NEEDLE_ROTATION_PAUSE
            ).apply {
                duration = NEEDLE_ANIMATION_DURATION * 2 / 3 // 占总时长的2/3
                interpolator = AccelerateInterpolator(1.2f) // 加速抬起
            }

            // 按顺序播放两个动画
            animatorSet.playSequentially(firstPhase, secondPhase)
        }

        // 设置动画监听器
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 确保最终位置正确
                vinylArm.rotation = if (isPlaying) NEEDLE_ROTATION_PLAY else NEEDLE_ROTATION_PAUSE
            }
        })

        // 启动动画集合
        animatorSet.start()

        // 保存动画引用
        needleAnimator = ObjectAnimator.ofFloat(vinylArm, View.ROTATION, 0f, 0f).apply {
            duration = 1
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 当这个空动画结束时，检查animatorSet是否仍在运行
                    if (animatorSet.isRunning) {
                        // 如果仍在运行，取消它
                        animatorSet.cancel()
                    }
                }
            })
        }
    }

    /**
     * 暂停旋转动画
     * 使用平滑的减速效果
     * 参考ponymusic-master项目的实现，优化减速效果
     */
    fun pauseRotationAnimations() {
        val albumArt = this.albumArt
        val vinylBackground = this.vinylBackground

        if (albumArt == null || vinylBackground == null) {
            Log.e(TAG, "无法暂停旋转动画：UI组件为null")
            return
        }

        // 如果动画已经暂停，不需要再次暂停
        if ((albumRotationAnimator?.isRunning != true) && (vinylRotationAnimator?.isRunning != true)) {
            return
        }

        // 先播放唱针抬起动画，再暂停旋转
        animateNeedle(false)

        // 创建减速动画 - 使用更自然的减速效果
        albumRotationAnimator?.let { animator ->
            if (animator.isRunning) {
                // 获取当前旋转角度和旋转速度
                val currentRotation = albumArt.rotation
                val rotationVelocity = 360f / ALBUM_ROTATION_DURATION * 1000 // 每秒旋转的角度

                // 取消当前动画
                animator.cancel()

                // 创建减速动画
                val decelerateAnimator = ObjectAnimator.ofFloat(
                    albumArt,
                    View.ROTATION,
                    currentRotation,
                    currentRotation + rotationVelocity * 0.5f // 额外旋转半秒的角度
                ).apply {
                    duration = 500 // 500毫秒减速
                    interpolator = DecelerateInterpolator(1.5f) // 使用更强的减速效果

                    // 添加动画更新监听器，记录最终角度
                    addUpdateListener { animation ->
                        val value = animation.animatedValue as Float
                        // 确保角度在0-360范围内
                        albumArt.rotation = value % 360
                    }
                }

                // 启动减速动画
                decelerateAnimator.start()
            }
        }

        vinylRotationAnimator?.let { animator ->
            if (animator.isRunning) {
                // 获取当前旋转角度和旋转速度
                val currentRotation = vinylBackground.rotation
                val rotationVelocity = 360f / VINYL_ROTATION_DURATION * 1000 // 每秒旋转的角度

                // 取消当前动画
                animator.cancel()

                // 创建减速动画
                val decelerateAnimator = ObjectAnimator.ofFloat(
                    vinylBackground,
                    View.ROTATION,
                    currentRotation,
                    currentRotation + rotationVelocity * 0.5f // 额外旋转半秒的角度
                ).apply {
                    duration = 500 // 500毫秒减速
                    interpolator = DecelerateInterpolator(1.5f) // 使用更强的减速效果

                    // 添加动画更新监听器，记录最终角度
                    addUpdateListener { animation ->
                        val value = animation.animatedValue as Float
                        // 确保角度在0-360范围内
                        vinylBackground.rotation = value % 360
                    }
                }

                // 启动减速动画
                decelerateAnimator.start()
            }
        }

        // 更新状态
        _isAnimatingFlow.value = false
    }
    /**
     * 重置旋转动画
     * 切换歌曲时调用，将专辑封面和黑胶唱片平滑过渡到初始角度
     * 参考ponymusic-master项目的实现，优化切换效果
     */
    fun resetRotationAnimations() {
        val albumArt = this.albumArt
        val vinylBackground = this.vinylBackground

        if (albumArt == null || vinylBackground == null) {
            Log.e(TAG, "无法重置旋转动画：UI组件为null")
            return
        }

        // 先抬起唱针
        vinylArm?.let {
            animateNeedle(false)
        }

        // 停止当前的旋转动画
        albumRotationAnimator?.cancel()
        vinylRotationAnimator?.cancel()
        needleAnimator?.cancel()

        // 获取当前旋转角度
        val albumCurrentRotation = albumArt.rotation
        val vinylCurrentRotation = vinylBackground.rotation

        // 创建平滑过渡的动画集合
        val animatorSet = AnimatorSet()

        // 第一阶段：减速停止
        val albumSlowDownAnimator = ObjectAnimator.ofFloat(
            albumArt,
            View.ROTATION,
            albumCurrentRotation,
            albumCurrentRotation + 30f // 额外旋转30度
        ).apply {
            duration = 400 // 400毫秒减速
            interpolator = DecelerateInterpolator(1.5f) // 强减速
        }

        val vinylSlowDownAnimator = ObjectAnimator.ofFloat(
            vinylBackground,
            View.ROTATION,
            vinylCurrentRotation,
            vinylCurrentRotation + 40f // 额外旋转40度
        ).apply {
            duration = 400 // 400毫秒减速
            interpolator = DecelerateInterpolator(1.5f) // 强减速
        }

        // 第二阶段：平滑过渡到0度
        val albumResetAnimator = ObjectAnimator.ofFloat(
            albumArt,
            View.ROTATION,
            0f, // 从0度开始（会在第一阶段结束后设置）
            360f // 旋转一整圈
        ).apply {
            duration = 500 // 500毫秒过渡
            interpolator = AccelerateDecelerateInterpolator()
        }

        val vinylResetAnimator = ObjectAnimator.ofFloat(
            vinylBackground,
            View.ROTATION,
            0f, // 从0度开始（会在第一阶段结束后设置）
            360f // 旋转一整圈
        ).apply {
            duration = 600 // 600毫秒过渡
            interpolator = AccelerateDecelerateInterpolator()
        }

        // 第一阶段结束后执行第二阶段
        val slowDownSet = AnimatorSet()
        slowDownSet.playTogether(albumSlowDownAnimator, vinylSlowDownAnimator)
        slowDownSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 重置旋转角度为0，准备开始第二阶段
                albumArt.rotation = 0f
                vinylBackground.rotation = 0f

                // 启动第二阶段动画
                val resetSet = AnimatorSet()
                resetSet.playTogether(albumResetAnimator, vinylResetAnimator)
                resetSet.addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 确保旋转角度为0
                        albumArt.rotation = 0f
                        vinylBackground.rotation = 0f

                        // 如果有唱针，确保唱针角度正确
                        vinylArm?.rotation = NEEDLE_ROTATION_PAUSE

                        // 如果当前是播放状态，重新启动动画
                        if (_isAnimatingFlow.value) {
                            startRotationAnimations()
                        }
                    }
                })
                resetSet.start()
            }
        })

        // 启动第一阶段动画
        slowDownSet.start()
    }

    /**
     * 设置动画状态
     * @param isPlaying 是否正在播放
     */
    fun setAnimating(isPlaying: Boolean) {
        if (isPlaying) {
            startRotationAnimations()
        } else {
            pauseRotationAnimations()
        }
    }
}