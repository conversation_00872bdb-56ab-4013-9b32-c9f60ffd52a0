package com.example.aimusicplayer

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.multidex.MultiDexApplication
import com.example.aimusicplayer.utils.Constants
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

/**
 * 应用程序类
 * 用于初始化Hilt和其他全局配置
 * 提供全局应用上下文和用户Cookie管理
 */
@HiltAndroidApp
class MusicApplication : MultiDexApplication() {

    companion object {
        private const val TAG = "MusicApplication"
        private lateinit var instance: MusicApplication

        /**
         * 获取应用上下文
         * @return 应用上下文
         */
        fun getContext(): Context = instance
    }

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onCreate() {
        super.onCreate()

        // 保存静态实例
        instance = this

        // 设置夜间模式
        val isNightMode = sharedPreferences.getBoolean(Constants.PREF_NIGHT_MODE, false)
        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        }

        Log.d(TAG, "应用启动初始化完成")
    }

    /**
     * 保存用户Cookie
     * @param cookie 用户登录Cookie
     */
    fun saveCookie(cookie: String?) {
        if (cookie.isNullOrEmpty()) {
            Log.w(TAG, "尝试保存空Cookie")
            return
        }

        // 记录cookie信息（隐藏敏感信息）
        val logCookie = if (cookie.length > 20) {
            cookie.substring(0, 10) + "..." + cookie.substring(cookie.length - 10)
        } else {
            cookie
        }
        Log.d(TAG, "保存Cookie: $logCookie")

        sharedPreferences.edit().apply {
            putString(Constants.PREF_COOKIE, cookie)
            apply()
        }

        // 验证保存是否成功
        val savedCookie = sharedPreferences.getString(Constants.PREF_COOKIE, "")
        if (!savedCookie.isNullOrEmpty()) {
            Log.d(TAG, "Cookie已成功保存")
        } else {
            Log.e(TAG, "Cookie保存失败，保存后读取为空")
        }
    }

    /**
     * 获取用户Cookie
     * @return 保存的Cookie，如果没有则返回空字符串
     */
    fun getCookie(): String {
        val cookie = sharedPreferences.getString(Constants.PREF_COOKIE, "") ?: ""
        if (cookie.isEmpty()) {
            Log.w(TAG, "获取到的Cookie为空")
        } else {
            val logCookie = if (cookie.length > 20) {
                cookie.substring(0, 10) + "..." + cookie.substring(cookie.length - 10)
            } else {
                cookie
            }
            Log.d(TAG, "获取到Cookie: $logCookie")
        }
        return cookie
    }

    /**
     * 清除用户Cookie
     */
    fun clearCookie() {
        sharedPreferences.edit().apply {
            remove(Constants.PREF_COOKIE)
            apply()
        }
        Log.d(TAG, "Cookie已清除")
    }
}
