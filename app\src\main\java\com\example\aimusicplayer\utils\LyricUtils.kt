package com.example.aimusicplayer.utils

import android.util.Log
import java.util.regex.Pattern

/**
 * 歌词工具类
 * 用于解析和处理歌词
 */
object LyricUtils {
    private const val TAG = "LyricUtils"
    
    // 歌词时间标签正则表达式
    private val timePattern = Pattern.compile("\\[(\\d{2}):(\\d{2})\\.(\\d{2,3})\\]")
    
    // 翻译歌词标记
    private const val TRANSLATION_TAG = "【翻译】"
    
    /**
     * 歌词行数据类
     * @param time 时间戳（毫秒）
     * @param text 歌词文本
     * @param translation 翻译文本（可选）
     */
    data class LyricLine(
        val time: Long,
        val text: String,
        var translation: String? = null
    )
    
    /**
     * 解析歌词文本
     * @param lyricText 原始歌词文本
     * @return 解析后的歌词行列表，按时间排序
     */
    fun parseLyric(lyricText: String?): List<LyricLine> {
        if (lyricText.isNullOrEmpty()) {
            return emptyList()
        }
        
        val lines = lyricText.split("\n")
        val lyricLines = mutableListOf<LyricLine>()
        val translationMap = mutableMapOf<Long, String>()
        
        // 第一遍解析：提取所有歌词行和翻译行
        for (line in lines) {
            val trimmedLine = line.trim()
            if (trimmedLine.isEmpty()) continue
            
            // 检查是否是翻译行
            val isTranslation = trimmedLine.contains(TRANSLATION_TAG)
            
            // 提取时间标签
            val matcher = timePattern.matcher(trimmedLine)
            if (matcher.find()) {
                val minutes = matcher.group(1)?.toInt() ?: 0
                val seconds = matcher.group(2)?.toInt() ?: 0
                val milliseconds = matcher.group(3)?.let {
                    if (it.length == 2) it.toInt() * 10 else it.toInt()
                } ?: 0
                
                val timeMs = (minutes * 60 * 1000 + seconds * 1000 + milliseconds).toLong()
                
                // 提取歌词文本（去除时间标签）
                val text = trimmedLine.substring(matcher.end()).trim()
                
                if (isTranslation) {
                    // 存储翻译，稍后匹配
                    translationMap[timeMs] = text.replace(TRANSLATION_TAG, "").trim()
                } else {
                    // 添加歌词行
                    lyricLines.add(LyricLine(timeMs, text))
                }
            }
        }
        
        // 第二遍处理：匹配翻译
        for (lyricLine in lyricLines) {
            lyricLine.translation = translationMap[lyricLine.time]
        }
        
        // 按时间排序
        return lyricLines.sortedBy { it.time }
    }
    
    /**
     * 根据当前播放位置查找当前歌词行索引
     * @param position 当前播放位置（毫秒）
     * @param lyricLines 歌词行列表
     * @return 当前歌词行索引，如果没有找到则返回-1
     */
    fun findCurrentLineIndex(position: Long, lyricLines: List<LyricLine>): Int {
        if (lyricLines.isEmpty()) return -1
        
        // 如果当前位置小于第一行时间，返回-1
        if (position < lyricLines.first().time) return -1
        
        // 如果当前位置大于最后一行时间，返回最后一行
        if (position >= lyricLines.last().time) return lyricLines.size - 1
        
        // 二分查找当前行
        var left = 0
        var right = lyricLines.size - 1
        
        while (left <= right) {
            val mid = (left + right) / 2
            
            if (mid + 1 < lyricLines.size && position >= lyricLines[mid].time && position < lyricLines[mid + 1].time) {
                return mid
            } else if (position < lyricLines[mid].time) {
                right = mid - 1
            } else {
                left = mid + 1
            }
        }
        
        return left - 1
    }
    
    /**
     * 获取当前显示的歌词行列表
     * @param currentIndex 当前歌词行索引
     * @param lyricLines 歌词行列表
     * @param lineCount 需要显示的行数（当前行前后各显示几行）
     * @return 当前显示的歌词行列表
     */
    fun getDisplayLines(currentIndex: Int, lyricLines: List<LyricLine>, lineCount: Int = 2): List<LyricLine> {
        if (lyricLines.isEmpty() || currentIndex < 0) return emptyList()
        
        val result = mutableListOf<LyricLine>()
        val startIndex = maxOf(0, currentIndex - lineCount)
        val endIndex = minOf(lyricLines.size - 1, currentIndex + lineCount)
        
        for (i in startIndex..endIndex) {
            result.add(lyricLines[i])
        }
        
        return result
    }
}
