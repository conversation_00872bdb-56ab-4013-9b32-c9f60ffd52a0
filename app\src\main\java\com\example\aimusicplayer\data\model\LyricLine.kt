package com.example.aimusicplayer.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 歌词行
 * 表示一行歌词及其对应的时间和翻译
 */
@Parcelize
data class LyricLine(
    // 时间（毫秒）
    val time: Long,

    // 歌词文本
    val text: String,

    // 翻译歌词（可选）
    val translation: String? = null
) : Parcelable {
    /**
     * 是否有翻译歌词
     * @return 是否有翻译歌词
     */
    fun hasTranslation(): Boolean {
        return !translation.isNullOrEmpty()
    }

    /**
     * 获取完整歌词文本（包括翻译）
     * @return 完整歌词文本
     */
    fun getFullText(): String {
        return if (hasTranslation()) {
            "$text\n$translation"
        } else {
            text
        }
    }

    /**
     * 转换为Java版本的LyricEntry
     * 用于兼容旧代码
     */
    fun toLyricEntry(): com.example.aimusicplayer.model.LyricEntry {
        return com.example.aimusicplayer.model.LyricEntry(time, text, translation)
    }

    companion object {
        /**
         * 从Java版本的LyricEntry创建
         * 用于兼容旧代码
         */
        fun fromLyricEntry(entry: com.example.aimusicplayer.model.LyricEntry): LyricLine {
            return LyricLine(
                time = entry.time,
                text = entry.text ?: "",
                translation = entry.translatedText
            )
        }
    }
}
