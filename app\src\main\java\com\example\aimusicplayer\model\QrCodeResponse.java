package com.example.aimusicplayer.model;

import com.example.aimusicplayer.api.ApiResponse;
import com.google.gson.annotations.SerializedName;

/**
 * 生成二维码图片的响应模型
 * 使用MVVM架构
 */
public class QrCodeResponse extends ApiResponse<QrCodeResponse.Data> {

    public static class Data {
        @SerializedName("qrurl")
        private String qrUrl; // 二维码图片 URL (可能不存在)

        @SerializedName("qrimg")
        private String qrImg; // Base64 编码的二维码图片数据 (可能不存在)

        public String getQrUrl() {
            return qrUrl;
        }

        public void setQrUrl(String qrUrl) {
            this.qrUrl = qrUrl;
        }

        public String getQrImg() {
            return qrImg;
        }

        public String getQrimg() {
            return qrImg;
        }

        public void setQrImg(String qrImg) {
            this.qrImg = qrImg;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "qrUrl='" + qrUrl + '\'' +
                    ", qrImg='" + qrImg + '\'' +
                    '}';
        }
    }
}