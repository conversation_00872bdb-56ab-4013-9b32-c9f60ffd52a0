<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="10dp">

    <TextView
        android:id="@+id/tvIndex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:minWidth="48dp"
        android:paddingHorizontal="8dp"
        android:singleLine="true"
        android:textColor="@color/common_text_h2_color"
        android:textSize="16dp"
        tools:text="9" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="歌曲"
            android:textColor="@color/common_text_h1_color"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/tvSubTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="歌手 - 专辑"
            android:textColor="@color/grey"
            android:textSize="12sp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:scaleType="centerInside"
        android:src="@drawable/ic_more"
        app:tint="@color/common_text_h2_color" />
</LinearLayout>