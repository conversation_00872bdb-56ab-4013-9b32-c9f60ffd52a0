package com.example.aimusicplayer.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 用户实体类
 */
@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    @ColumnInfo("user_id")
    val userId: Long,
    
    // 用户名
    @ColumnInfo("username")
    val username: String,
    
    // 昵称
    @ColumnInfo("nickname")
    val nickname: String,
    
    // 头像URL
    @ColumnInfo("avatar_url")
    val avatarUrl: String,
    
    // 背景图URL
    @ColumnInfo("background_url")
    val backgroundUrl: String = "",
    
    // 个性签名
    @ColumnInfo("signature")
    val signature: String = "",
    
    // 关注数
    @ColumnInfo("follows")
    val follows: Int = 0,
    
    // 粉丝数
    @ColumnInfo("followers")
    val followers: Int = 0,
    
    // 用户等级
    @ColumnInfo("level")
    val level: Int = 0,
    
    // 会员类型
    @ColumnInfo("vip_type")
    val vipType: Int = 0,
    
    // 最后登录时间
    @ColumnInfo("last_login_time")
    val lastLoginTime: Long = System.currentTimeMillis(),
    
    // 登录Cookie
    @ColumnInfo("cookie")
    val cookie: String = ""
)
