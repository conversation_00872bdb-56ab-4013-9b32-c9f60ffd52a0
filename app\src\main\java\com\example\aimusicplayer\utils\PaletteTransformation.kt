package com.example.aimusicplayer.utils

import android.graphics.Bitmap
import android.util.Log
import androidx.palette.graphics.Palette
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import java.nio.charset.Charset
import java.security.MessageDigest

/**
 * Glide图片转换，用于从图片中提取颜色
 * 在加载图片的同时提取颜色，避免二次处理
 */
class PaletteTransformation(private val callback: PaletteCallback?) : BitmapTransformation() {
    companion object {
        private const val TAG = "PaletteTransformation"
        private const val ID = "com.example.aimusicplayer.utils.PaletteTransformation"
        private val ID_BYTES = ID.toByteArray(Charset.forName("UTF-8"))
    }

    /**
     * 调色板回调接口
     */
    interface PaletteCallback {
        /**
         * 当调色板准备好时回调
         * @param palette 调色板对象
         */
        fun onPaletteReady(palette: Palette)
    }

    override fun transform(pool: BitmapPool, toTransform: Bitmap, outWidth: Int, outHeight: Int): Bitmap {
        try {
            // 使用Palette库从图片中提取颜色
            val builder = Palette.Builder(toTransform)
            builder.maximumColorCount(24) // 提取更多颜色，获得更准确的结果
            val palette = builder.generate()
            
            callback?.onPaletteReady(palette)
        } catch (e: Exception) {
            Log.e(TAG, "提取颜色失败", e)
        }
        
        // 返回原始图片，不做任何变换
        return toTransform
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(ID_BYTES)
    }

    override fun equals(other: Any?): Boolean {
        return other is PaletteTransformation
    }

    override fun hashCode(): Int {
        return ID.hashCode()
    }
}
