package com.example.aimusicplayer.utils

import android.content.Context
import android.util.Log
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

/**
 * 播放列表缓存工具类
 * 提供播放列表的持久化存储和读取功能
 */
class PlaylistCache(private val context: Context) {

    private val TAG = "PlaylistCache"

    // 缓存目录
    private val cacheDir: File by lazy {
        File(context.cacheDir, "playlists").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    /**
     * 保存播放列表
     * @param playlistId 播放列表ID
     * @param mediaItems 媒体项列表
     * @param currentIndex 当前播放索引
     */
    suspend fun savePlaylist(
        playlistId: String,
        mediaItems: List<MediaItem>,
        currentIndex: Int
    ) = withContext(Dispatchers.IO) {
        try {
            val playlistFile = File(cacheDir, "$playlistId.json")

            // 创建JSON对象
            val jsonObject = JSONObject()
            jsonObject.put("playlistId", playlistId)
            jsonObject.put("currentIndex", currentIndex)
            jsonObject.put("timestamp", System.currentTimeMillis())

            // 创建媒体项数组
            val itemsArray = JSONArray()
            for (mediaItem in mediaItems) {
                val itemObject = JSONObject()
                itemObject.put("mediaId", mediaItem.mediaId)

                // 保存媒体元数据
                val metadata = mediaItem.mediaMetadata
                val metadataObject = JSONObject()
                metadataObject.put("title", metadata.title)
                metadataObject.put("artist", metadata.artist)
                metadataObject.put("albumTitle", metadata.albumTitle)
                metadataObject.put("artworkUri", metadata.artworkUri?.toString())
                metadataObject.put("mediaUri", mediaItem.requestMetadata.mediaUri?.toString())

                // 保存额外信息
                val extras = metadata.extras
                if (extras != null) {
                    val extrasObject = JSONObject()
                    for (key in extras.keySet()) {
                        when (val value = extras.get(key)) {
                            is String -> extrasObject.put(key, value)
                            is Int -> extrasObject.put(key, value)
                            is Long -> extrasObject.put(key, value)
                            is Boolean -> extrasObject.put(key, value)
                            is Float -> extrasObject.put(key, value)
                            is Double -> extrasObject.put(key, value)
                            else -> extrasObject.put(key, value.toString())
                        }
                    }
                    metadataObject.put("extras", extrasObject)
                }

                itemObject.put("metadata", metadataObject)
                itemsArray.put(itemObject)
            }

            jsonObject.put("items", itemsArray)

            // 写入文件
            FileOutputStream(playlistFile).use { output ->
                output.write(jsonObject.toString().toByteArray())
            }

            Log.d(TAG, "保存播放列表成功: $playlistId, 共${mediaItems.size}首歌曲")
        } catch (e: Exception) {
            Log.e(TAG, "保存播放列表失败: $playlistId", e)
        }
    }

    /**
     * 读取播放列表
     * @param playlistId 播放列表ID
     * @return 播放列表信息，包含媒体项列表和当前播放索引
     */
    suspend fun loadPlaylist(playlistId: String): PlaylistInfo? = withContext(Dispatchers.IO) {
        try {
            val playlistFile = File(cacheDir, "$playlistId.json")
            if (!playlistFile.exists()) {
                Log.d(TAG, "播放列表缓存不存在: $playlistId")
                return@withContext null
            }

            // 读取文件内容
            val content = FileInputStream(playlistFile).use { input ->
                val bytes = ByteArray(input.available())
                input.read(bytes)
                String(bytes)
            }

            // 解析JSON
            val jsonObject = JSONObject(content)
            val currentIndex = jsonObject.getInt("currentIndex")
            val timestamp = jsonObject.getLong("timestamp")

            // 解析媒体项
            val itemsArray = jsonObject.getJSONArray("items")
            val mediaItems = mutableListOf<MediaItem>()

            for (i in 0 until itemsArray.length()) {
                val itemObject = itemsArray.getJSONObject(i)
                val mediaId = itemObject.getString("mediaId")

                // 解析媒体元数据
                val metadataObject = itemObject.getJSONObject("metadata")
                val title = metadataObject.optString("title")
                val artist = metadataObject.optString("artist")
                val album = metadataObject.optString("albumTitle")
                val artworkUri = metadataObject.optString("artworkUri")
                val mediaUri = metadataObject.optString("mediaUri")

                // 构建媒体元数据
                val metadataBuilder = MediaMetadata.Builder()
                    .setTitle(title)
                    .setArtist(artist)
                    .setAlbumTitle(album)

                if (artworkUri.isNotEmpty()) {
                    metadataBuilder.setArtworkUri(android.net.Uri.parse(artworkUri))
                }

                // 解析额外信息
                if (metadataObject.has("extras")) {
                    val extrasObject = metadataObject.getJSONObject("extras")
                    val extras = androidx.core.os.bundleOf()

                    val keys = extrasObject.keys()
                    while (keys.hasNext()) {
                        val key = keys.next()
                        val value = extrasObject.get(key)
                        when (value) {
                            is String -> extras.putString(key, value)
                            is Int -> extras.putInt(key, value)
                            is Long -> extras.putLong(key, value)
                            is Boolean -> extras.putBoolean(key, value)
                            is Float -> extras.putFloat(key, value)
                            is Double -> extras.putDouble(key, value)
                            else -> extras.putString(key, value.toString())
                        }
                    }

                    metadataBuilder.setExtras(extras)
                }

                // 构建媒体项
                val mediaItemBuilder = MediaItem.Builder()
                    .setMediaId(mediaId)
                    .setMediaMetadata(metadataBuilder.build())

                if (mediaUri.isNotEmpty()) {
                    mediaItemBuilder.setUri(android.net.Uri.parse(mediaUri))
                }

                mediaItems.add(mediaItemBuilder.build())
            }

            Log.d(TAG, "读取播放列表成功: $playlistId, 共${mediaItems.size}首歌曲")
            return@withContext PlaylistInfo(mediaItems, currentIndex, timestamp)
        } catch (e: Exception) {
            Log.e(TAG, "读取播放列表失败: $playlistId", e)
            return@withContext null
        }
    }

    /**
     * 删除播放列表缓存
     * @param playlistId 播放列表ID，如果为null则删除所有缓存
     */
    suspend fun clearCache(playlistId: String? = null) = withContext(Dispatchers.IO) {
        try {
            if (playlistId != null) {
                // 删除指定播放列表缓存
                val playlistFile = File(cacheDir, "$playlistId.json")
                if (playlistFile.exists()) {
                    playlistFile.delete()
                    Log.d(TAG, "删除播放列表缓存成功: $playlistId")
                } else {
                    Log.d(TAG, "播放列表缓存不存在: $playlistId")
                }
            } else {
                // 删除所有播放列表缓存
                cacheDir.listFiles()?.forEach { it.delete() }
                Log.d(TAG, "删除所有播放列表缓存成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除播放列表缓存失败", e)
        }
    }

    /**
     * 获取所有缓存的播放列表ID
     * @return 播放列表ID列表
     */
    suspend fun getAllCachedPlaylistIds(): List<String> = withContext(Dispatchers.IO) {
        try {
            val files = cacheDir.listFiles() ?: return@withContext emptyList()
            return@withContext files.filter { it.name.endsWith(".json") }
                .map { it.name.removeSuffix(".json") }
        } catch (e: Exception) {
            Log.e(TAG, "获取所有缓存的播放列表ID失败", e)
            return@withContext emptyList()
        }
    }

    /**
     * 播放列表信息
     * @param mediaItems 媒体项列表
     * @param currentIndex 当前播放索引
     * @param timestamp 缓存时间戳
     */
    data class PlaylistInfo(
        val mediaItems: List<MediaItem>,
        val currentIndex: Int,
        val timestamp: Long
    )
}
