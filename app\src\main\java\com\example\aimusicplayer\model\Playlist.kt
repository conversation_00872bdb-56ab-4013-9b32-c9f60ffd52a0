package com.example.aimusicplayer.model

/**
 * 歌单模型类
 * 用于兼容旧版本代码
 */
class Playlist {
    var id: String? = null
    var name: String? = null
    var coverUrl: String? = null
    var description: String? = null
    var creatorId: String? = null
    var creatorName: String? = null
    var songCount: Int = 0
    var playCount: Int = 0
    var isSubscribed: Boolean = false
    var songs: List<SongDetailResponse.Song>? = null
} 