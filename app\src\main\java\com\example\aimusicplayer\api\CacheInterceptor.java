package com.example.aimusicplayer.api;

import android.content.Context;
import android.util.Log;

import com.example.aimusicplayer.utils.NetworkUtils;

import java.io.IOException;

import okhttp3.CacheControl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 缓存拦截器
 * 用于实现API缓存策略
 * 支持根据网络状态和API类型设置不同的缓存策略
 */
public class CacheInterceptor implements Interceptor {
    private static final String TAG = "CacheInterceptor";

    private final Context context;
    private final int maxAge; // 在线缓存过期时间（秒）
    private final int maxStale; // 离线缓存过期时间（秒）

    /**
     * 构造函数
     * @param context 上下文
     * @param maxAge 在线缓存过期时间（秒）
     * @param maxStale 离线缓存过期时间（秒）
     */
    public CacheInterceptor(Context context, int maxAge, int maxStale) {
        this.context = context;
        this.maxAge = maxAge;
        this.maxStale = maxStale;
    }

    /**
     * 构造函数（使用默认缓存时间）
     * @param context 上下文
     */
    public CacheInterceptor(Context context) {
        this(context, 60, 60 * 60 * 24); // 默认在线缓存60秒，离线缓存1天
    }

    /**
     * 拦截请求，应用默认缓存策略
     */
    @Override
    public Response intercept(Chain chain) throws IOException {
        return intercept(chain, maxAge, maxStale);
    }

    /**
     * 拦截请求，应用自定义缓存策略
     * @param chain 拦截器链
     * @param customMaxAge 自定义在线缓存过期时间（秒）
     * @param customMaxStale 自定义离线缓存过期时间（秒）
     * @return 响应
     * @throws IOException IO异常
     */
    public Response intercept(Chain chain, int customMaxAge, int customMaxStale) throws IOException {
        Request request = chain.request();
        String url = request.url().toString();

        // 检查请求是否包含no-cache指令
        boolean noCache = request.cacheControl().noCache();

        // 如果请求明确指定不缓存，则跳过缓存处理
        if (noCache) {
            Log.d(TAG, "跳过缓存: " + url);
            return chain.proceed(request);
        }

        // 如果没有网络，强制使用缓存
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.d(TAG, "无网络，强制使用缓存: " + url);
            request = request.newBuilder()
                    .cacheControl(CacheControl.FORCE_CACHE)
                    .build();
        }

        Response response = chain.proceed(request);

        // 添加自定义缓存标记，用于跟踪缓存命中
        String cacheHeader = response.cacheResponse() != null ? "HIT" : "MISS";

        if (NetworkUtils.isNetworkAvailable(context)) {
            // 有网络时，设置在线缓存过期时间
            Log.d(TAG, "设置在线缓存 (max-age=" + customMaxAge + "): " + url);
            response = response.newBuilder()
                    .removeHeader("Pragma") // 移除干扰头
                    .header("Cache-Control", "public, max-age=" + customMaxAge)
                    .header("X-Cache", cacheHeader)
                    .build();
        } else {
            // 无网络时，设置离线缓存过期时间
            Log.d(TAG, "设置离线缓存 (max-stale=" + customMaxStale + "): " + url);
            response = response.newBuilder()
                    .removeHeader("Pragma") // 移除干扰头
                    .header("Cache-Control", "public, only-if-cached, max-stale=" + customMaxStale)
                    .header("X-Cache", cacheHeader)
                    .build();
        }

        return response;
    }
}
