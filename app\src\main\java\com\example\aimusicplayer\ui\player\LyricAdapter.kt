package com.example.aimusicplayer.ui.player

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.LyricInfo
import com.example.aimusicplayer.data.model.LyricLine

/**
 * 歌词适配器
 * 用于在ViewPager中显示歌词
 */
class LyricAdapter(
    private val context: Context,
    private val lyricInfo: LyricInfo
) : RecyclerView.Adapter<LyricAdapter.LyricViewHolder>() {

    // 当前高亮的行索引
    private var currentLineIndex = -1

    // 当前播放位置
    private var currentPosition = 0L

    /**
     * 歌词ViewHolder
     */
    class LyricViewHolder(val textView: TextView) : RecyclerView.ViewHolder(textView)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LyricViewHolder {
        // 创建TextView
        val textView = TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.CENTER
            textSize = 16f
            setTextColor(Color.WHITE)
            alpha = 0.6f
            setPadding(16, 24, 16, 24) // 增加垂直间距

            // 添加阴影效果，提高可读性
            setShadowLayer(3f, 1f, 1f, Color.BLACK)

            // 设置行间距
            setLineSpacing(0f, 1.2f) // 设置行间距，第一个参数是额外间距，第二个是倍数
        }
        return LyricViewHolder(textView)
    }

    override fun onBindViewHolder(holder: LyricViewHolder, position: Int) {
        // 获取歌词行
        val lyricLine = lyricInfo.entries[position]

        // 设置歌词文本
        holder.textView.text = if (lyricLine.hasTranslation()) {
            "${lyricLine.text}\n${lyricLine.translation}"
        } else {
            lyricLine.text
        }

        // 设置高亮样式
        if (position == currentLineIndex) {
            // 当前行高亮显示
            holder.textView.apply {
                setTextColor(ContextCompat.getColor(context, R.color.white))
                alpha = 1.0f
                textSize = 18f
                typeface = Typeface.DEFAULT_BOLD

                // 添加动画效果
                if (tag != "highlighted") {
                    tag = "highlighted"
                    animate()
                        .scaleX(1.05f)
                        .scaleY(1.05f)
                        .setDuration(200)
                        .start()
                }
            }
        } else {
            // 非当前行样式
            val distance = Math.abs(position - currentLineIndex)
            val alpha = when {
                distance <= 1 -> 0.8f
                distance <= 2 -> 0.6f
                distance <= 3 -> 0.4f
                else -> 0.3f
            }

            holder.textView.apply {
                setTextColor(Color.WHITE)
                this.alpha = alpha
                textSize = 16f
                typeface = Typeface.DEFAULT

                // 重置动画
                if (tag == "highlighted") {
                    tag = null
                    animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(200)
                        .start()
                }
            }
        }
    }

    override fun getItemCount(): Int = lyricInfo.entries.size

    /**
     * 更新当前播放位置
     * @param position 当前播放位置（毫秒）
     */
    fun updateCurrentPosition(position: Long) {
        currentPosition = position

        // 查找当前行索引
        val newLineIndex = lyricInfo.findLineIndexByTime(position)

        // 如果行索引发生变化，更新UI
        if (newLineIndex != currentLineIndex) {
            val oldLineIndex = currentLineIndex
            currentLineIndex = newLineIndex

            // 通知适配器更新
            if (oldLineIndex >= 0) {
                notifyItemChanged(oldLineIndex)

                // 更新周围的行，以便应用渐变透明度
                for (i in Math.max(0, oldLineIndex - 3)..Math.min(itemCount - 1, oldLineIndex + 3)) {
                    if (i != oldLineIndex && i != newLineIndex) {
                        notifyItemChanged(i)
                    }
                }
            }

            if (currentLineIndex >= 0) {
                notifyItemChanged(currentLineIndex)

                // 更新周围的行，以便应用渐变透明度
                for (i in Math.max(0, currentLineIndex - 3)..Math.min(itemCount - 1, currentLineIndex + 3)) {
                    if (i != oldLineIndex && i != currentLineIndex) {
                        notifyItemChanged(i)
                    }
                }
            }
        }
    }

    /**
     * 获取当前高亮的行索引
     */
    fun getCurrentLine(): Int = currentLineIndex

    /**
     * 获取指定索引的歌词行
     * @param index 歌词行索引
     * @return 歌词行，如果索引无效则返回null
     */
    fun getLyricLine(index: Int): LyricLine? {
        return if (index >= 0 && index < lyricInfo.entries.size) {
            lyricInfo.entries[index]
        } else {
            null
        }
    }
}

/**
 * 空歌词适配器
 * 用于在没有歌词时显示提示
 */
class EmptyLyricAdapter(private val context: Context) : RecyclerView.Adapter<EmptyLyricAdapter.EmptyViewHolder>() {

    class EmptyViewHolder(val view: View) : RecyclerView.ViewHolder(view)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EmptyViewHolder {
        // 创建TextView
        val textView = TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            gravity = Gravity.CENTER
            textSize = 20f
            setTextColor(Color.WHITE)
            text = "暂无歌词"

            // 添加阴影效果，提高可读性
            setShadowLayer(3f, 1f, 1f, Color.BLACK)

            // 添加淡入动画
            alpha = 0f
            animate()
                .alpha(1f)
                .setDuration(800)
                .start()
        }
        return EmptyViewHolder(textView)
    }

    override fun onBindViewHolder(holder: EmptyViewHolder, position: Int) {
        // 不需要额外处理
    }

    override fun getItemCount(): Int = 1
}
