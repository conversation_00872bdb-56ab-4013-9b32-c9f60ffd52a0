package com.example.aimusicplayer.data.cache

import android.util.Log
import com.example.aimusicplayer.data.db.dao.ApiCacheDao
import com.example.aimusicplayer.data.db.entity.ApiCacheEntity
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API缓存管理器
 * 用于管理API响应的缓存
 */
@Singleton
class ApiCacheManager @Inject constructor(
    private val apiCacheDao: ApiCacheDao,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "ApiCacheManager"
        
        // 缓存类型
        const val CACHE_TYPE_DEFAULT = "DEFAULT"
        const val CACHE_TYPE_SONG = "SONG"
        const val CACHE_TYPE_PLAYLIST = "PLAYLIST"
        const val CACHE_TYPE_LYRIC = "LYRIC"
        const val CACHE_TYPE_COMMENT = "COMMENT"
        const val CACHE_TYPE_USER = "USER"
        
        // 默认过期时间（5分钟）
        const val DEFAULT_EXPIRATION_TIME = 5 * 60 * 1000L
        
        // 长期缓存过期时间（1天）
        const val LONG_EXPIRATION_TIME = 24 * 60 * 60 * 1000L
    }
    
    /**
     * 保存缓存
     * @param key 缓存键
     * @param data 缓存数据
     * @param expirationTime 过期时间（毫秒）
     * @param cacheType 缓存类型
     */
    suspend fun <T> saveCache(
        key: String,
        data: T,
        expirationTime: Long = DEFAULT_EXPIRATION_TIME,
        cacheType: String = CACHE_TYPE_DEFAULT
    ) {
        withContext(Dispatchers.IO) {
            try {
                val jsonData = gson.toJson(data)
                val cacheEntity = ApiCacheEntity(
                    cacheKey = key,
                    data = jsonData,
                    cacheTime = System.currentTimeMillis(),
                    expirationTime = expirationTime,
                    cacheType = cacheType
                )
                apiCacheDao.insert(cacheEntity)
            } catch (e: Exception) {
                Log.e(TAG, "保存缓存失败: $key", e)
            }
        }
    }
    
    /**
     * 获取缓存
     * @param key 缓存键
     * @param clazz 数据类型
     * @return 缓存数据，如果不存在或已过期则返回null
     */
    suspend fun <T> getCache(key: String, clazz: Class<T>): T? {
        return withContext(Dispatchers.IO) {
            try {
                val cacheEntity = apiCacheDao.getCache(key) ?: return@withContext null
                
                // 检查是否过期
                val currentTime = System.currentTimeMillis()
                if (currentTime - cacheEntity.cacheTime > cacheEntity.expirationTime) {
                    // 缓存已过期，删除缓存
                    apiCacheDao.deleteCache(key)
                    return@withContext null
                }
                
                // 解析数据
                gson.fromJson(cacheEntity.data, clazz)
            } catch (e: Exception) {
                Log.e(TAG, "获取缓存失败: $key", e)
                null
            }
        }
    }
    
    /**
     * 删除缓存
     * @param key 缓存键
     */
    suspend fun deleteCache(key: String) {
        withContext(Dispatchers.IO) {
            try {
                apiCacheDao.deleteCache(key)
            } catch (e: Exception) {
                Log.e(TAG, "删除缓存失败: $key", e)
            }
        }
    }
    
    /**
     * 清除所有缓存
     */
    suspend fun clearAllCache() {
        withContext(Dispatchers.IO) {
            try {
                apiCacheDao.clearAll()
            } catch (e: Exception) {
                Log.e(TAG, "清除所有缓存失败", e)
            }
        }
    }
    
    /**
     * 清除过期缓存
     */
    suspend fun clearExpiredCache() {
        withContext(Dispatchers.IO) {
            try {
                apiCacheDao.clearExpiredCache()
            } catch (e: Exception) {
                Log.e(TAG, "清除过期缓存失败", e)
            }
        }
    }
    
    /**
     * 根据缓存类型清除缓存
     * @param cacheType 缓存类型
     */
    suspend fun clearCacheByType(cacheType: String) {
        withContext(Dispatchers.IO) {
            try {
                apiCacheDao.clearCacheByType(cacheType)
            } catch (e: Exception) {
                Log.e(TAG, "清除缓存失败: $cacheType", e)
            }
        }
    }
    
    /**
     * 获取缓存大小
     * @return 缓存大小（字节）
     */
    suspend fun getCacheSize(): Long {
        return withContext(Dispatchers.IO) {
            try {
                apiCacheDao.getCacheSize() ?: 0L
            } catch (e: Exception) {
                Log.e(TAG, "获取缓存大小失败", e)
                0L
            }
        }
    }
}
