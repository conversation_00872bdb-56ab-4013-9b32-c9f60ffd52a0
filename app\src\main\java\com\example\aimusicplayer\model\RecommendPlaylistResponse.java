package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 推荐歌单响应
 * 对应API: /personalized
 */
public class RecommendPlaylistResponse {
    private int code;
    
    @SerializedName("result")
    private List<RecommendPlaylist> playlists;
    
    @SerializedName("hasTaste")
    private boolean hasTaste;
    
    @SerializedName("category")
    private int category;
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public List<RecommendPlaylist> getPlaylists() {
        return playlists;
    }
    
    public void setPlaylists(List<RecommendPlaylist> playlists) {
        this.playlists = playlists;
    }
    
    public boolean isHasTaste() {
        return hasTaste;
    }
    
    public void setHasTaste(boolean hasTaste) {
        this.hasTaste = hasTaste;
    }
    
    public int getCategory() {
        return category;
    }
    
    public void setCategory(int category) {
        this.category = category;
    }
    
    /**
     * 推荐歌单
     */
    public static class RecommendPlaylist {
        @SerializedName("id")
        private String id;
        
        @SerializedName("type")
        private int type;
        
        @SerializedName("name")
        private String name;
        
        @SerializedName("picUrl")
        private String picUrl;
        
        @SerializedName("playCount")
        private long playCount;
        
        @SerializedName("trackCount")
        private int trackCount;
        
        @SerializedName("copywriter")
        private String copywriter;
        
        @SerializedName("canDislike")
        private boolean canDislike;
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public int getType() {
            return type;
        }
        
        public void setType(int type) {
            this.type = type;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getPicUrl() {
            return picUrl;
        }
        
        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }
        
        public long getPlayCount() {
            return playCount;
        }
        
        public void setPlayCount(long playCount) {
            this.playCount = playCount;
        }
        
        public int getTrackCount() {
            return trackCount;
        }
        
        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }
        
        public String getCopywriter() {
            return copywriter;
        }
        
        public void setCopywriter(String copywriter) {
            this.copywriter = copywriter;
        }
        
        public boolean isCanDislike() {
            return canDislike;
        }
        
        public void setCanDislike(boolean canDislike) {
            this.canDislike = canDislike;
        }
    }
}
