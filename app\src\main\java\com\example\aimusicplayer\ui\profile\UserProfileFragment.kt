package com.example.aimusicplayer.ui.profile

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.example.aimusicplayer.R
import com.example.aimusicplayer.databinding.FragmentUserProfileBinding
import com.example.aimusicplayer.model.User
import com.example.aimusicplayer.ui.login.LoginActivity
import com.example.aimusicplayer.viewmodel.UserProfileViewModel
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 用户资料页面
 * 显示用户基本信息、统计数据和账户信息
 */
@AndroidEntryPoint
class UserProfileFragment : Fragment() {

    private var _binding: FragmentUserProfileBinding? = null
    private val binding get() = _binding!!

    private val viewModel: UserProfileViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUserProfileBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupObservers()
        setupListeners()

        // 加载用户数据
        viewModel.loadUserData()
    }

    private fun setupObservers() {
        // 观察用户数据
        viewModel.userData.observe(viewLifecycleOwner, Observer { user ->
            if (user != null) {
                // 隐藏错误提示
                binding.tvError.visibility = View.GONE

                // 更新UI
                updateUI(user)
            }
        })

        // 观察加载状态
        viewModel.isLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            if (isLoading) {
                binding.progressBar.visibility = View.VISIBLE

                // 添加加载动画
                val rotateAnimation = AnimationUtils.loadAnimation(requireContext(), R.anim.rotate)
                binding.progressBar.startAnimation(rotateAnimation)
            } else {
                binding.progressBar.clearAnimation()
                binding.progressBar.visibility = View.GONE
            }
        })

        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner, Observer { errorMsg ->
            if (!errorMsg.isNullOrEmpty()) {
                // 设置错误文本
                binding.tvError.text = errorMsg

                // 显示错误提示
                binding.tvError.visibility = View.VISIBLE

                // 添加错误动画
                val shakeAnimation = AnimationUtils.loadAnimation(requireContext(), R.anim.shake)
                binding.tvError.startAnimation(shakeAnimation)

                // 根据错误类型显示不同的提示
                when {
                    errorMsg.contains("网络") -> {
                        Snackbar.make(binding.root, "网络连接失败，请检查网络设置", Snackbar.LENGTH_LONG)
                            .setAction("重试") { viewModel.retryLoadUserData() }
                            .show()
                    }
                    errorMsg.contains("超时") -> {
                        Snackbar.make(binding.root, "服务器响应超时，请稍后再试", Snackbar.LENGTH_LONG)
                            .setAction("重试") { viewModel.retryLoadUserData() }
                            .show()
                    }
                    errorMsg.contains("登录") -> {
                        Snackbar.make(binding.root, "登录状态异常，请重新登录", Snackbar.LENGTH_LONG)
                            .setAction("登录") {
                                val intent = Intent(requireActivity(), LoginActivity::class.java)
                                startActivity(intent)
                            }
                            .show()
                    }
                    else -> {
                        // 默认错误提示
                        Snackbar.make(binding.root, errorMsg, Snackbar.LENGTH_SHORT).show()
                    }
                }
            } else {
                binding.tvError.visibility = View.GONE
            }
        })
    }

    private fun setupListeners() {
        // 错误提示点击重试
        binding.tvError.setOnClickListener {
            binding.tvError.visibility = View.GONE
            viewModel.retryLoadUserData()
        }

        // 添加按钮动画效果
        val buttonAnimation = AnimationUtils.loadAnimation(requireContext(), R.anim.button_press)

        // 退出登录按钮
        binding.btnLogout.setOnClickListener { view ->
            // 应用按钮动画
            view.startAnimation(buttonAnimation)

            // 显示确认Snackbar
            Snackbar.make(binding.root, "确定要退出登录吗？", Snackbar.LENGTH_LONG)
                .setAction("确定") {
                    // 显示加载状态
                    binding.progressBar.visibility = View.VISIBLE

                    // 使用非挂起函数版本的logout方法，更适合在UI事件中调用
                    viewModel.logoutSync()

                    // 观察退出登录结果
                    viewModel.userData.observe(viewLifecycleOwner) { user ->
                        if (user == null) {
                            // 用户数据为null表示已退出登录
                            // 显示成功消息
                            Toast.makeText(requireContext(), "已成功退出登录", Toast.LENGTH_SHORT).show()

                            // 使用协程处理延迟跳转
                            lifecycleScope.launch {
                                // 延迟500毫秒后跳转到登录页面，增加流畅感
                                delay(500)

                                // 创建Intent跳转到登录页面
                                val intent = Intent(requireActivity(), LoginActivity::class.java)
                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                                startActivity(intent)

                                // 添加过渡动画
                                requireActivity().overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
                            }
                        }
                    }

                    // 观察错误信息
                    viewModel.errorMessage.observe(viewLifecycleOwner) { errorMsg ->
                        if (!errorMsg.isNullOrEmpty() && errorMsg.contains("退出登录失败")) {
                            // 处理错误
                            binding.progressBar.visibility = View.GONE
                            Snackbar.make(binding.root, errorMsg, Snackbar.LENGTH_LONG).show()
                        }
                    }
                }
                .setActionTextColor(resources.getColor(R.color.colorAccent, null))
                .show()
        }

        // 添加更多按钮点击事件
        binding.tvMore.setOnClickListener { view ->
            view.startAnimation(buttonAnimation)
            Snackbar.make(binding.root, "更多功能正在开发中...", Snackbar.LENGTH_SHORT).show()
        }
    }

    private fun updateUI(user: User) {
        // 创建淡入动画
        val fadeIn = AnimationUtils.loadAnimation(requireContext(), android.R.anim.fade_in)
        fadeIn.duration = 500

        // 创建从下到上的动画
        val slideUp = AnimationUtils.loadAnimation(requireContext(), R.anim.slide_up)

        // 设置用户名并添加动画
        binding.tvUsername.text = user.username
        binding.tvUsername.startAnimation(fadeIn)

        // 设置用户头像并添加动画
        if (user.avatarUrl.isNotEmpty()) {
            Glide.with(this)
                .load(user.avatarUrl)
                .apply(RequestOptions().transform(CircleCrop()))
                .placeholder(R.drawable.default_avatar)
                .error(R.drawable.default_avatar)
                .into(binding.ivUserAvatar)
        }
        binding.ivUserAvatar.startAnimation(fadeIn)

        // 设置VIP标签
        if (user.isVip) {
            binding.tvVipTag.visibility = View.VISIBLE
            binding.tvVipTag.startAnimation(fadeIn)
        } else {
            binding.tvVipTag.visibility = View.GONE
        }

        // 设置等级标签
        binding.tvLevelTag.text = "Lv.${user.level}"
        binding.tvLevelTag.startAnimation(fadeIn)

        // 设置签名
        binding.tvSignature.text = if (user.signature.isNotEmpty()) {
            user.signature
        } else {
            "这个人很懒，什么都没有留下"
        }
        binding.tvSignature.startAnimation(fadeIn)

        // 使用协程延迟加载统计数据，创造渐进式加载效果
        viewLifecycleOwner.lifecycleScope.launch {
            delay(300)

            // 设置统计数据
            binding.tvLikedSongsCount.text = "268" // 这里应该使用实际数据
            binding.tvLikedSongsCount.startAnimation(slideUp)

            delay(100)
            binding.tvPlaylistsCount.text = "32"   // 这里应该使用实际数据
            binding.tvPlaylistsCount.startAnimation(slideUp)

            delay(100)
            binding.tvFollowedArtistsCount.text = user.follows.toString()
            binding.tvFollowedArtistsCount.startAnimation(slideUp)

            delay(100)
            binding.tvListeningHours.text = "124"  // 这里应该使用实际数据
            binding.tvListeningHours.startAnimation(slideUp)

            // 卡片整体动画
            binding.cardUserStats.startAnimation(fadeIn)
        }

        // 使用协程延迟加载账户信息，创造渐进式加载效果
        viewLifecycleOwner.lifecycleScope.launch {
            delay(600)

            // 设置账户信息
            binding.tvPhone.text = "138****6789"  // 这里应该使用实际数据
            binding.tvEmail.text = "<EMAIL>" // 这里应该使用实际数据

            // 设置会员状态
            if (user.isVip) {
                binding.tvVipStatus.text = "年费会员 (有效期至2024年12月)" // 这里应该使用实际数据
                binding.tvVipStatus.setTextColor(resources.getColor(R.color.colorAccent, null))
            } else {
                binding.tvVipStatus.text = "普通用户"
                binding.tvVipStatus.setTextColor(resources.getColor(android.R.color.white, null))
            }

            // 设置注册时间
            val sdf = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
            binding.tvRegisterTime.text = sdf.format(Date()) // 这里应该使用实际数据

            // 退出登录按钮动画
            binding.btnLogout.alpha = 0f
            binding.btnLogout.visibility = View.VISIBLE
            binding.btnLogout.animate()
                .alpha(1f)
                .setDuration(500)
                .start()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
