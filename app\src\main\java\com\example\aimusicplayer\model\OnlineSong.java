package com.example.aimusicplayer.model;

public class OnlineSong {
    private long id;
    private String name;
    private String artist;
    private String albumName;
    private String albumCoverUrl;
    private long duration; // 歌曲时长，单位毫秒
    private String url; // 播放地址
    
    public OnlineSong(long id, String name, String artist, String albumName, String albumCoverUrl, long duration, String url) {
        this.id = id;
        this.name = name;
        this.artist = artist;
        this.albumName = albumName;
        this.albumCoverUrl = albumCoverUrl;
        this.duration = duration;
        this.url = url;
    }
    
    public long getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getArtist() {
        return artist;
    }
    
    public String getAlbumName() {
        return albumName;
    }
    
    public String getAlbumCoverUrl() {
        return albumCoverUrl;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
} 