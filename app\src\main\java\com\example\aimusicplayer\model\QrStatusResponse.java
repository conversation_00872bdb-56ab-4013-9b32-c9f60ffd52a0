package com.example.aimusicplayer.model;

import android.util.Log;

import com.example.aimusicplayer.api.ApiResponse;
import com.google.gson.annotations.SerializedName;

/**
 * 检查二维码扫描状态的响应模型
 * 兼容QrCheckResponse
 */
public class QrStatusResponse extends ApiResponse<QrStatusResponse.Data> {
    private static final String TAG = "QrStatusResponse";

    public static class Data {
        @SerializedName("code")
        private int code;

        @SerializedName("message")
        private String message;

        @SerializedName("cookie")
        private String cookie;

        // 二维码状态码
        // 800: 二维码过期
        // 801: 等待扫码
        // 802: 等待确认
        // 803: 授权登录成功
        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getCookie() {
            // 记录cookie获取情况
            if (cookie == null) {
                Log.w(TAG, "getCookie: cookie为null");
            } else if (cookie.isEmpty()) {
                Log.w(TAG, "getCookie: cookie为空字符串");
            } else {
                Log.d(TAG, "getCookie: cookie长度=" + cookie.length());
            }
            return cookie;
        }

        public void setCookie(String cookie) {
            // 记录cookie设置情况
            if (cookie == null) {
                Log.w(TAG, "setCookie: 尝试设置null cookie");
            } else if (cookie.isEmpty()) {
                Log.w(TAG, "setCookie: 尝试设置空字符串cookie");
            } else {
                Log.d(TAG, "setCookie: 设置cookie，长度=" + cookie.length());
            }
            this.cookie = cookie;
        }

        /**
         * 是否已扫码
         * @return 是否已扫码
         */
        public boolean isScanned() {
            return code == 802 || code == 803;
        }

        /**
         * 是否已授权
         * @return 是否已授权
         */
        public boolean isAuthorized() {
            return code == 803;
        }

        /**
         * 是否已过期
         * @return 是否已过期
         */
        public boolean isExpired() {
            return code == 800;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "code=" + code +
                    ", message='" + message + '\'' +
                    ", cookie='" + (cookie != null ? (cookie.length() > 20 ? cookie.substring(0, 10) + "..." + cookie.substring(cookie.length() - 10) : cookie) : "null") + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "QrStatusResponse{" +
                "status=" + getStatus() +
                ", code=" + getCode() +
                ", message='" + getMessage() + '\'' +
                ", data=" + (getData() != null ? getData().toString() : "null") +
                '}';
    }
}
