package com.example.aimusicplayer.ui.player

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.util.LruCache
import android.view.View
import android.widget.ImageView
import androidx.core.graphics.ColorUtils
import androidx.palette.graphics.Palette
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import jp.wasabeef.glide.transformations.BlurTransformation
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 专辑封面处理器
 * 用于专辑封面颜色提取和背景模糊效果
 */
@Singleton
class AlbumArtProcessor @Inject constructor(
    private val context: Context
) {
    companion object {
        private const val TAG = "AlbumArtProcessor"

        // 默认背景颜色
        private val DEFAULT_BACKGROUND_COLOR = Color.parseColor("#121212")

        // 缓存大小
        private const val CACHE_SIZE = 20
    }

    // 颜色缓存
    private val colorCache = LruCache<String, Int>(CACHE_SIZE)

    // 位图缓存
    private val bitmapCache = LruCache<String, Bitmap>(CACHE_SIZE)

    /**
     * 加载专辑封面
     * @param url 封面URL
     * @param callback 回调
     */
    fun loadAlbumArt(url: String?, callback: (Bitmap?) -> Unit) {
        if (url.isNullOrEmpty()) {
            callback(null)
            return
        }

        // 检查缓存
        val cachedBitmap = bitmapCache.get(url)
        if (cachedBitmap != null) {
            callback(cachedBitmap)
            return
        }

        // 加载图片
        Glide.with(context)
            .asBitmap()
            .load(url)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 缓存位图
                    bitmapCache.put(url, resource)
                    callback(resource)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    callback(null)
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    callback(null)
                }
            })
    }

    /**
     * 提取专辑封面主色调
     * @param bitmap 位图
     * @param callback 回调
     */
    fun extractColor(bitmap: Bitmap?, callback: (Int) -> Unit) {
        if (bitmap == null) {
            callback(DEFAULT_BACKGROUND_COLOR)
            return
        }

        // 生成缓存键
        val cacheKey = bitmap.hashCode().toString()

        // 检查缓存
        val cachedColor = colorCache.get(cacheKey)
        if (cachedColor != null) {
            callback(cachedColor)
            return
        }

        // 提取颜色
        Palette.from(bitmap).generate { palette ->
            val color = palette?.getDarkVibrantColor(palette.getDarkMutedColor(DEFAULT_BACKGROUND_COLOR))
                ?: DEFAULT_BACKGROUND_COLOR

            // 调整颜色亮度，确保不会太亮
            val hsl = FloatArray(3)
            ColorUtils.colorToHSL(color, hsl)
            hsl[2] = 0.1f // 降低亮度
            val adjustedColor = ColorUtils.HSLToColor(hsl)

            // 缓存颜色
            colorCache.put(cacheKey, adjustedColor)

            callback(adjustedColor)
        }
    }

    /**
     * 应用背景模糊效果
     * @param bitmap 位图
     * @param view 目标视图
     * @param radius 模糊半径
     * @param sampling 采样率
     */
    fun applyBlurBackground(bitmap: Bitmap?, view: View, radius: Int = 25, sampling: Int = 4) {
        if (bitmap == null) {
            view.setBackgroundColor(DEFAULT_BACKGROUND_COLOR)
            return
        }

        // 如果视图是ImageView，使用Glide的BlurTransformation
        if (view is ImageView) {
            Glide.with(context)
                .load(bitmap)
                .transform(BlurTransformation(radius, sampling))
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .into(view)
        } else {
            // 对于非ImageView，先创建模糊位图，然后设置为背景
            Glide.with(context)
                .asBitmap()
                .load(bitmap)
                .transform(BlurTransformation(radius, sampling))
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .into(object : CustomTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        view.background = android.graphics.drawable.BitmapDrawable(context.resources, resource)
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                        view.setBackgroundColor(DEFAULT_BACKGROUND_COLOR)
                    }
                })
        }
    }

    /**
     * 清除缓存
     */
    fun clearCache() {
        colorCache.evictAll()
        bitmapCache.evictAll()
    }

    /**
     * 处理专辑封面
     * @param url 封面URL
     * @param onBitmapReady 位图准备好回调
     * @param onColorExtracted 颜色提取回调
     */
    suspend fun processAlbumArt(
        url: String?,
        onBitmapReady: (Bitmap?) -> Unit,
        onColorExtracted: (Int) -> Unit
    ) = withContext(Dispatchers.Main) {
        loadAlbumArt(url) { bitmap ->
            onBitmapReady(bitmap)
            extractColor(bitmap, onColorExtracted)
        }
    }
}
