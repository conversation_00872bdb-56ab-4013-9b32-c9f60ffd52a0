package com.example.aimusicplayer.model;

import java.util.List;

/**
 * 艺术家模型
 * 用于兼容旧代码
 */
public class Artist {
    private long id;
    private String name;
    private List<String> translations;
    private List<String> aliases;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getTranslations() {
        return translations;
    }

    public void setTranslations(List<String> translations) {
        this.translations = translations;
    }

    public List<String> getAliases() {
        return aliases;
    }

    public void setAliases(List<String> aliases) {
        this.aliases = aliases;
    }
}
