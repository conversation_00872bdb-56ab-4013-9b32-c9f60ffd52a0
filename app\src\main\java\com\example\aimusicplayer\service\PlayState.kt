package com.example.aimusicplayer.service

/**
 * 播放状态
 * 定义了播放器的各种状态
 */
sealed class PlayState {
    object Idle : PlayState()
    object Preparing : PlayState()
    object Playing : PlayState()
    object Pause : PlayState()
    data class Error(val errorMessage: String) : PlayState()

    val isIdle: <PERSON>olean
        get() = this is Idle
    val isPreparing: Boolean
        get() = this is Preparing
    val isPlaying: Boolean
        get() = this is Playing
    val isPausing: Boolean
        get() = this is Pause
    val isError: <PERSON>ole<PERSON>
        get() = this is Error
}
