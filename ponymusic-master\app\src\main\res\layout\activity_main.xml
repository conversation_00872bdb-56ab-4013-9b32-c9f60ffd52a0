<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <me.wcy.music.widget.PlayBar
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_divider_size"
            android:background="@color/common_divider" />

        <LinearLayout
            android:id="@+id/tabBar"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:background="@color/tab_bg"
            android:orientation="horizontal" />
    </LinearLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigationView"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:itemIconTint="?attr/colorPrimary"
        app:menu="@menu/navigation_menu" />
</androidx.drawerlayout.widget.DrawerLayout>
