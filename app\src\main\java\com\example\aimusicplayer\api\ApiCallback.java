package com.example.aimusicplayer.api;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 统一的API回调处理类
 * 用于简化API调用，提供统一的错误处理
 * @param <T> 响应数据类型
 */
public class ApiCallback<T> implements Callback<T> {
    private static final String TAG = "ApiCallback";

    private final MutableLiveData<ApiResponse<T>> responseLiveData;
    private final MutableLiveData<String> errorMessageLiveData;
    private final MutableLiveData<Boolean> loadingLiveData;
    private final boolean showLoading;

    /**
     * 构造函数
     * @param responseLiveData 响应LiveData
     * @param errorMessageLiveData 错误信息LiveData
     * @param loadingLiveData 加载状态LiveData
     * @param showLoading 是否显示加载状态
     */
    public ApiCallback(
            MutableLiveData<ApiResponse<T>> responseLiveData,
            MutableLiveData<String> errorMessageLiveData,
            MutableLiveData<Boolean> loadingLiveData,
            boolean showLoading) {
        this.responseLiveData = responseLiveData;
        this.errorMessageLiveData = errorMessageLiveData;
        this.loadingLiveData = loadingLiveData;
        this.showLoading = showLoading;

        // 设置加载状态
        if (showLoading && loadingLiveData != null) {
            loadingLiveData.postValue(true);
        }

        // 设置初始加载状态
        if (responseLiveData != null) {
            responseLiveData.postValue(ApiResponse.loading());
        }
    }

    /**
     * 简化构造函数
     * @param responseLiveData 响应LiveData
     * @param errorMessageLiveData 错误信息LiveData
     */
    public ApiCallback(
            MutableLiveData<ApiResponse<T>> responseLiveData,
            MutableLiveData<String> errorMessageLiveData) {
        this(responseLiveData, errorMessageLiveData, null, false);
    }

    @Override
    public void onResponse(@NonNull Call<T> call, @NonNull Response<T> response) {
        // 结束加载状态
        if (showLoading && loadingLiveData != null) {
            loadingLiveData.postValue(false);
        }

        // 创建API响应
        ApiResponse<T> apiResponse = ApiResponse.create(response);

        // 更新响应LiveData
        if (responseLiveData != null) {
            responseLiveData.postValue(apiResponse);
        }

        // 处理错误消息
        if (apiResponse.isError() && errorMessageLiveData != null) {
            // 特殊处理301错误（需要登录）
            if (apiResponse.getCode() == 301) {
                String enhancedMessage = apiResponse.getMessage() + "\n可能原因：1. 未登录 2. Cookie已过期 3. 缓存问题";
                errorMessageLiveData.postValue(enhancedMessage);
                Log.w(TAG, "301错误 - 需要登录: " + call.request().url());
                Log.w(TAG, "建议解决方案: 1. 登录账号 2. 添加时间戳参数 3. 等待2分钟后重试");
            } else {
                errorMessageLiveData.postValue(apiResponse.getMessage());
            }
        }

        // 调用回调方法
        if (apiResponse.isSuccessful()) {
            onSuccess(apiResponse.getData());
        } else {
            onError(apiResponse.getError() != null ?
                    apiResponse.getError() :
                    new Exception(apiResponse.getMessage()));

            // 记录错误日志
            Log.e(TAG, "API响应错误: " + apiResponse);
        }
    }

    @Override
    public void onFailure(@NonNull Call<T> call, @NonNull Throwable t) {
        // 结束加载状态
        if (showLoading && loadingLiveData != null) {
            loadingLiveData.postValue(false);
        }

        // 创建网络错误响应
        ApiResponse<T> apiResponse = ApiResponse.networkError(t);

        // 更新响应LiveData
        if (responseLiveData != null) {
            responseLiveData.postValue(apiResponse);
        }

        // 更新错误消息
        if (errorMessageLiveData != null) {
            errorMessageLiveData.postValue(apiResponse.getMessage());
        }

        // 调用错误回调
        onError(t);

        // 记录错误日志
        Log.e(TAG, "API调用失败", t);
    }

    /**
     * 成功回调，子类可以重写此方法
     * @param response 响应数据
     */
    protected void onSuccess(T response) {
        // 子类可以重写此方法
    }

    /**
     * 错误回调，子类可以重写此方法
     * @param t 错误
     */
    protected void onError(Throwable t) {
        // 子类可以重写此方法
    }

    /**
     * 简化的API回调接口
     * @param <T> 响应数据类型
     */
    public interface SimpleCallback<T> {
        /**
         * 成功回调
         * @param response 响应数据
         */
        void onSuccess(T response);

        /**
         * 错误回调
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }
}
