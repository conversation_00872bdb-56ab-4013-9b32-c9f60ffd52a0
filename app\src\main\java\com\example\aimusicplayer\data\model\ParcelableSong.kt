package com.example.aimusicplayer.data.model

import android.os.Parcelable
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import com.example.aimusicplayer.data.db.entity.SongEntity
import kotlinx.parcelize.Parcelize

/**
 * 可序列化的歌曲模型
 * 用于在Fragment/Activity之间传递歌曲数据
 */
@Parcelize
data class ParcelableSong(
    // 歌曲类型:本地/网络
    val type: Int = TYPE_ONLINE,

    // 歌曲ID
    val songId: Long = 0,

    // 音乐标题
    val title: String = "",

    // 艺术家
    val artist: String = "",

    // 艺术家ID
    val artistId: Long = 0,

    // 专辑
    val album: String = "",

    // 专辑ID
    val albumId: Long = 0,

    // 专辑封面
    val albumCover: String = "",

    // 持续时间
    val duration: Long = 0,

    // 播放地址
    var uri: String = "",

    // [本地]文件路径
    var path: String = "",

    // [本地]文件名
    val fileName: String = "",

    // [本地]文件大小
    val fileSize: Long = 0,

    // 是否收藏
    val isFavorite: Boolean = false,

    // 上次播放时间
    val lastPlayedTime: Long = 0,
    
    // 唯一ID
    val uniqueId: String = ""
) : Parcelable {

    /**
     * 是否为本地歌曲
     */
    fun isLocal() = type == TYPE_LOCAL

    /**
     * 转换为MediaItem
     */
    fun toMediaItem(): MediaItem {
        val metadata = MediaMetadata.Builder()
            .setTitle(title)
            .setArtist(artist)
            .setAlbumTitle(album)
            .setArtworkUri(android.net.Uri.parse(albumCover))
            .setMediaType(MediaMetadata.MEDIA_TYPE_MUSIC)
            .setIsBrowsable(false)
            .setIsPlayable(true)
            .build()

        return MediaItem.Builder()
            .setMediaId(uniqueId)
            .setMediaMetadata(metadata)
            .setUri(if (isLocal()) path else uri)
            .build()
    }

    /**
     * 转换为SongEntity
     */
    fun toSongEntity(): SongEntity {
        return SongEntity(
            type = type,
            songId = songId,
            title = title,
            artist = artist,
            artistId = artistId,
            album = album,
            albumId = albumId,
            albumCover = albumCover,
            duration = duration,
            uri = uri,
            path = path,
            fileName = fileName,
            fileSize = fileSize,
            isFavorite = isFavorite,
            lastPlayedTime = lastPlayedTime
        ).apply {
            this.uniqueId = <EMAIL>
        }
    }

    companion object {
        const val TYPE_LOCAL = 0
        const val TYPE_ONLINE = 1

        /**
         * 从SongEntity转换
         */
        fun fromSongEntity(entity: SongEntity): ParcelableSong {
            return ParcelableSong(
                type = entity.type,
                songId = entity.songId,
                title = entity.title,
                artist = entity.artist,
                artistId = entity.artistId,
                album = entity.album,
                albumId = entity.albumId,
                albumCover = entity.albumCover,
                duration = entity.duration,
                uri = entity.uri,
                path = entity.path,
                fileName = entity.fileName,
                fileSize = entity.fileSize,
                isFavorite = entity.isFavorite,
                lastPlayedTime = entity.lastPlayedTime,
                uniqueId = entity.uniqueId
            )
        }

        /**
         * 从MediaItem转换
         */
        fun fromMediaItem(mediaItem: MediaItem): ParcelableSong {
            val metadata = mediaItem.mediaMetadata
            val isLocal = mediaItem.localConfiguration?.uri?.scheme == "file"
            val type = if (isLocal) TYPE_LOCAL else TYPE_ONLINE
            val songId = mediaItem.mediaId.split("_").lastOrNull()?.toLongOrNull() ?: 0

            return ParcelableSong(
                type = type,
                songId = songId,
                title = metadata.title?.toString() ?: "",
                artist = metadata.artist?.toString() ?: "",
                album = metadata.albumTitle?.toString() ?: "",
                albumCover = metadata.artworkUri?.toString() ?: "",
                duration = metadata.extras?.getLong("duration") ?: 0,
                uri = mediaItem.requestMetadata.mediaUri?.toString() ?: "",
                path = if (isLocal) mediaItem.localConfiguration?.uri?.path ?: "" else "",
                uniqueId = mediaItem.mediaId
            )
        }
    }
}
