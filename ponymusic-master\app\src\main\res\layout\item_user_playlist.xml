<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/ivCover"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_default_cover" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="名称"
            android:textColor="@color/common_text_h1_color"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/tvCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="54首, by xxx"
            android:textColor="@color/common_text_h2_color"
            android:textSize="12sp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:scaleType="centerInside"
        android:src="@drawable/ic_more"
        app:tint="@color/common_text_h2_color" />
</LinearLayout>