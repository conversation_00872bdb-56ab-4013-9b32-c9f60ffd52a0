<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <top.wangchenyan.common.widget.TitleLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tlTitleText="手机号登录" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="60dp"
            android:hint="请输入手机号"
            android:textColorHint="@color/common_text_h2_color">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="phone"
                android:singleLine="true"
                android:textColor="@color/common_text_h1_color"
                android:textSize="16dp" />
        </com.google.android.material.textfield.TextInputLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入短信验证码"
                android:textColorHint="@color/common_text_h2_color">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPhoneCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingEnd="100dp"
                    android:singleLine="true"
                    android:textColor="@color/common_text_h1_color"
                    android:textSize="16dp" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/tvSendCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|bottom"
                android:layout_marginBottom="8dp"
                android:padding="4dp"
                android:text="获取验证码"
                android:textColor="@color/color_theme_h2"
                android:textSize="15dp" />
        </FrameLayout>

        <Button
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="50dp"
            android:backgroundTint="@color/color_theme_grey_300"
            android:text="登录"
            android:textColor="@color/white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvQrcodeLogin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="36dp"
            android:padding="4dp"
            android:text="扫码登录"
            android:textColor="@color/common_theme_color"
            android:textSize="14dp" />
    </LinearLayout>
</LinearLayout>