package com.example.aimusicplayer.utils

import android.util.Log
import android.util.LruCache
import com.example.aimusicplayer.model.LyricInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable
import java.util.concurrent.TimeUnit

/**
 * 歌词缓存工具类
 * 提供内存缓存和磁盘缓存功能
 */
class LyricCache(private val cacheDir: File) {

    // 内存缓存
    private val memoryCache = LruCache<Long, LyricInfo>(100)

    // 磁盘缓存目录
    private val lyricCacheDir: File by lazy {
        File(cacheDir, "lyrics").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    /**
     * 获取歌词
     * 先从内存缓存获取，如果没有则从磁盘缓存获取
     * @param songId 歌曲ID
     * @return 歌词信息，如果没有缓存则返回null
     */
    suspend fun getLyric(songId: Long): LyricInfo? = withContext(Dispatchers.IO) {
        // 先从内存缓存获取
        var lyricInfo = memoryCache.get(songId)

        // 如果内存缓存没有，则从磁盘缓存获取
        if (lyricInfo == null) {
            lyricInfo = readFromDisk(songId)

            // 如果从磁盘获取到了，则放入内存缓存
            if (lyricInfo != null) {
                memoryCache.put(songId, lyricInfo)
            }
        }

        return@withContext lyricInfo
    }

    /**
     * 保存歌词
     * 同时保存到内存缓存和磁盘缓存
     * @param songId 歌曲ID
     * @param lyricInfo 歌词信息
     */
    suspend fun saveLyric(songId: Long, lyricInfo: LyricInfo) = withContext(Dispatchers.IO) {
        // 保存到内存缓存
        memoryCache.put(songId, lyricInfo)

        // 保存到磁盘缓存
        saveToDisk(songId, lyricInfo)
    }

    /**
     * 清除歌词缓存
     * @param songId 歌曲ID，如果为null则清除所有缓存
     */
    suspend fun clearCache(songId: Long? = null) = withContext(Dispatchers.IO) {
        if (songId != null) {
            // 清除指定歌曲的缓存
            memoryCache.remove(songId)
            val file = File(lyricCacheDir, "$songId.lyric")
            if (file.exists()) {
                file.delete()
            } else {
                // 文件不存在，无需操作
            }
        } else {
            // 清除所有缓存
            memoryCache.evictAll()
            lyricCacheDir.listFiles()?.forEach { it.delete() }
        }
    }

    /**
     * 清理过期缓存
     * 删除超过30天未使用的歌词缓存
     */
    suspend fun cleanExpiredCache() = withContext(Dispatchers.IO) {
        try {
            val now = System.currentTimeMillis()
            val expirationTime = now - TimeUnit.DAYS.toMillis(30) // 30天过期

            var expiredCount = 0
            lyricCacheDir.listFiles()?.forEach { file ->
                if (file.lastModified() < expirationTime) {
                    if (file.delete()) {
                        expiredCount++
                    }
                }
            }

            if (expiredCount > 0) {
                Log.d("LyricCache", "已清理 $expiredCount 个过期歌词缓存文件")
            } else {
                Log.d("LyricCache", "没有过期的歌词缓存文件需要清理")
            }
        } catch (e: Exception) {
            Log.e("LyricCache", "清理过期歌词缓存失败", e)
        }
    }

    /**
     * 从磁盘读取歌词
     * @param songId 歌曲ID
     * @return 歌词信息，如果没有缓存则返回null
     */
    private fun readFromDisk(songId: Long): LyricInfo? {
        val file = File(lyricCacheDir, "$songId.lyric")
        if (!file.exists()) {
            return null
        }

        try {
            ObjectInputStream(FileInputStream(file)).use { input ->
                val serializedLyric = input.readObject() as SerializedLyric
                return serializedLyric.toLyricInfo()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果读取失败，删除可能损坏的文件
            file.delete()
            return null
        }
    }

    /**
     * 保存歌词到磁盘
     * @param songId 歌曲ID
     * @param lyricInfo 歌词信息
     */
    private fun saveToDisk(songId: Long, lyricInfo: LyricInfo) {
        val file = File(lyricCacheDir, "$songId.lyric")

        try {
            ObjectOutputStream(FileOutputStream(file)).use { output ->
                val serializedLyric = SerializedLyric.fromLyricInfo(lyricInfo)
                output.writeObject(serializedLyric)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果保存失败，删除可能损坏的文件
            if (file.exists()) {
                file.delete()
            }
        }
    }

    /**
     * 序列化的歌词信息
     * 用于保存到磁盘
     */
    private data class SerializedLyric(
        val title: String?,
        val artist: String?,
        val album: String?,
        val lyricist: String?,
        val composer: String?,
        val entries: List<SerializedLyricEntry>,
        val hasTranslation: Boolean
    ) : Serializable {

        /**
         * 转换为LyricInfo
         */
        fun toLyricInfo(): LyricInfo {
            val lyricInfo = LyricInfo()
            lyricInfo.title = title
            lyricInfo.artist = artist
            lyricInfo.album = album
            lyricInfo.lyricist = lyricist
            lyricInfo.composer = composer

            val lyricEntries = entries.map { it.toLyricEntry() }
            lyricInfo.setEntries(lyricEntries)

            return lyricInfo
        }

        companion object {
            private const val serialVersionUID = 1L

            /**
             * 从LyricInfo创建SerializedLyric
             */
            fun fromLyricInfo(lyricInfo: LyricInfo): SerializedLyric {
                val entries = lyricInfo.getEntries().map {
                    SerializedLyricEntry(
                        it.time,
                        it.text,
                        it.translatedText
                    )
                }

                return SerializedLyric(
                    lyricInfo.title,
                    lyricInfo.artist,
                    lyricInfo.album,
                    lyricInfo.lyricist,
                    lyricInfo.composer,
                    entries,
                    lyricInfo.hasTranslation()
                )
            }
        }
    }

    /**
     * 序列化的歌词条目
     */
    private data class SerializedLyricEntry(
        val time: Long,
        val text: String,
        val translatedText: String?
    ) : Serializable {

        /**
         * 转换为LyricEntry
         */
        fun toLyricEntry(): com.example.aimusicplayer.model.LyricEntry {
            return com.example.aimusicplayer.model.LyricEntry(time, text, translatedText)
        }

        companion object {
            private const val serialVersionUID = 1L
        }
    }
}
