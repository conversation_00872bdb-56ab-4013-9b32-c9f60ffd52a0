<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <top.wangchenyan.common.widget.TitleLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tlTitleText="每日推荐" />

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvPlayAll"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:drawableStart="@drawable/ic_play_circle"
            android:drawablePadding="10dp"
            android:drawableTint="@color/common_theme_color"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:text="播放全部"
            android:textColor="@color/common_text_h1_color"
            android:textSize="16dp"
            android:textStyle="bold" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5"
            tools:listitem="@layout/item_recommend_song" />

        <me.wcy.music.widget.PlayBar
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>
</LinearLayout>