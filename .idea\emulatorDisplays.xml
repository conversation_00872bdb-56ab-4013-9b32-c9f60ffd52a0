<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EmulatorDisplays">
    <option name="displayStateByAvdFolder">
      <map>
        <entry key="D:\AndroidSDK\.android\avd\Automotive_1408p_landscape_API_33.avd">
          <value>
            <MultiDisplayState>
              <option name="displayDescriptors">
                <list>
                  <DisplayDescriptor>
                    <option name="height" value="1408" />
                    <option name="width" value="792" />
                  </DisplayDescriptor>
                  <DisplayDescriptor>
                    <option name="displayId" value="6" />
                    <option name="height" value="600" />
                    <option name="width" value="400" />
                  </DisplayDescriptor>
                </list>
              </option>
              <option name="panelState">
                <PanelState>
                  <option name="splitPanel">
                    <SplitPanelState>
                      <option name="proportion" value="0.8999999761581421" />
                      <option name="firstComponent">
                        <PanelState>
                          <option name="displayId" value="0" />
                        </PanelState>
                      </option>
                      <option name="secondComponent">
                        <PanelState>
                          <option name="displayId" value="6" />
                        </PanelState>
                      </option>
                    </SplitPanelState>
                  </option>
                </PanelState>
              </option>
            </MultiDisplayState>
          </value>
        </entry>
        <entry key="D:\AndroidSDK\.android\avd\Automotive_1408p_landscape_with_Google_Play_API_34-ext9.avd">
          <value>
            <MultiDisplayState>
              <option name="displayDescriptors">
                <list>
                  <DisplayDescriptor>
                    <option name="height" value="792" />
                    <option name="width" value="1408" />
                  </DisplayDescriptor>
                  <DisplayDescriptor>
                    <option name="displayId" value="6" />
                    <option name="height" value="792" />
                    <option name="width" value="528" />
                  </DisplayDescriptor>
                </list>
              </option>
              <option name="panelState">
                <PanelState>
                  <option name="splitPanel">
                    <SplitPanelState>
                      <option name="proportion" value="0.8999999761581421" />
                      <option name="firstComponent">
                        <PanelState>
                          <option name="displayId" value="0" />
                        </PanelState>
                      </option>
                      <option name="secondComponent">
                        <PanelState>
                          <option name="displayId" value="6" />
                        </PanelState>
                      </option>
                    </SplitPanelState>
                  </option>
                </PanelState>
              </option>
            </MultiDisplayState>
          </value>
        </entry>
        <entry key="D:\AndroidSDK\.android\avd\Automotive_Distant_Display_with_Google_Play_API_33.avd">
          <value>
            <MultiDisplayState>
              <option name="displayDescriptors">
                <list>
                  <DisplayDescriptor>
                    <option name="height" value="1080" />
                    <option name="width" value="600" />
                  </DisplayDescriptor>
                  <DisplayDescriptor>
                    <option name="displayId" value="6" />
                    <option name="height" value="600" />
                    <option name="width" value="400" />
                  </DisplayDescriptor>
                  <DisplayDescriptor>
                    <option name="displayId" value="7" />
                    <option name="height" value="600" />
                    <option name="width" value="3000" />
                  </DisplayDescriptor>
                </list>
              </option>
              <option name="panelState">
                <PanelState>
                  <option name="splitPanel">
                    <SplitPanelState>
                      <option name="proportion" value="0.860927164554596" />
                      <option name="splitType" value="VERTICAL" />
                      <option name="firstComponent">
                        <PanelState>
                          <option name="splitPanel">
                            <SplitPanelState>
                              <option name="proportion" value="0.8999999761581421" />
                              <option name="firstComponent">
                                <PanelState>
                                  <option name="displayId" value="0" />
                                </PanelState>
                              </option>
                              <option name="secondComponent">
                                <PanelState>
                                  <option name="displayId" value="6" />
                                </PanelState>
                              </option>
                            </SplitPanelState>
                          </option>
                        </PanelState>
                      </option>
                      <option name="secondComponent">
                        <PanelState>
                          <option name="displayId" value="7" />
                        </PanelState>
                      </option>
                    </SplitPanelState>
                  </option>
                </PanelState>
              </option>
            </MultiDisplayState>
          </value>
        </entry>
        <entry key="D:\AndroidSDK\.android\avd\Automotive_Large_Portrait_API_33.avd">
          <value>
            <MultiDisplayState>
              <option name="displayDescriptors">
                <list>
                  <DisplayDescriptor>
                    <option name="height" value="1706" />
                    <option name="width" value="1380" />
                  </DisplayDescriptor>
                  <DisplayDescriptor>
                    <option name="displayId" value="6" />
                    <option name="height" value="600" />
                    <option name="width" value="400" />
                  </DisplayDescriptor>
                </list>
              </option>
              <option name="panelState">
                <PanelState>
                  <option name="splitPanel">
                    <SplitPanelState>
                      <option name="proportion" value="0.7752808928489685" />
                      <option name="firstComponent">
                        <PanelState>
                          <option name="displayId" value="0" />
                        </PanelState>
                      </option>
                      <option name="secondComponent">
                        <PanelState>
                          <option name="displayId" value="6" />
                        </PanelState>
                      </option>
                    </SplitPanelState>
                  </option>
                </PanelState>
              </option>
            </MultiDisplayState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>