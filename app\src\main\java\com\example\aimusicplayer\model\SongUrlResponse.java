package com.example.aimusicplayer.model;

import java.util.List;

public class SongUrlResponse {
    
    private int code;
    private List<Data> data;
    
    public int getCode() {
        return code;
    }
    
    public List<Data> getData() {
        return data;
    }
    
    public static class Data {
        private long id;
        private String url;
        private int br; // 码率
        private int size; // 大小，单位字节
        private String type; // 文件类型，如 mp3
        private int code; // 状态码
        
        public long getId() {
            return id;
        }
        
        public String getUrl() {
            return url;
        }
        
        public int getBitRate() {
            return br;
        }
        
        public int getSize() {
            return size;
        }
        
        public String getType() {
            return type;
        }
        
        public int getCode() {
            return code;
        }
    }
} 