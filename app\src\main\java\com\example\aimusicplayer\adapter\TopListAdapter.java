package com.example.aimusicplayer.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.model.TopListResponse;
import com.example.aimusicplayer.utils.ImageUtils;

import java.util.List;

public class TopListAdapter extends RecyclerView.Adapter<TopListAdapter.TopListViewHolder> {

    private List<TopListResponse.PlayList> playlists;
    private OnTopListClickListener listener;

    public TopListAdapter(List<TopListResponse.PlayList> playlists, OnTopListClickListener listener) {
        this.playlists = playlists;
        this.listener = listener;
    }

    @NonNull
    @Override
    public TopListViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_top_list, parent, false);
        return new TopListViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TopListViewHolder holder, int position) {
        TopListResponse.PlayList playlist = playlists.get(position);

        holder.nameTextView.setText(playlist.getName());
        holder.descTextView.setText(playlist.getDescription());
        holder.songCountTextView.setText(playlist.getSongCount() + "首");

        // 加载封面图片
        ImageUtils.loadImage(
            holder.itemView.getContext(),
            playlist.getCoverImgUrl(),
            holder.coverImageView,
            R.drawable.default_cover,
            R.drawable.default_cover
        );

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onTopListClick(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return playlists != null ? playlists.size() : 0;
    }

    public void updateData(List<TopListResponse.PlayList> newPlaylists) {
        this.playlists = newPlaylists;
        notifyDataSetChanged();
    }

    static class TopListViewHolder extends RecyclerView.ViewHolder {
        ImageView coverImageView;
        TextView nameTextView;
        TextView descTextView;
        TextView songCountTextView;

        TopListViewHolder(View itemView) {
            super(itemView);
            coverImageView = itemView.findViewById(R.id.topListCoverImageView);
            nameTextView = itemView.findViewById(R.id.topListNameTextView);
            descTextView = itemView.findViewById(R.id.topListDescTextView);
            songCountTextView = itemView.findViewById(R.id.topListSongCountTextView);
        }
    }

    public interface OnTopListClickListener {
        void onTopListClick(int position);
    }
}