package com.example.aimusicplayer.utils

import android.os.Bundle
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.NavDirections
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.example.aimusicplayer.R

/**
 * 导航工具类
 * 提供导航相关的扩展函数
 */
object NavigationUtils {
    
    /**
     * 安全导航扩展函数
     * 避免快速点击导致的导航崩溃
     */
    fun NavController.safeNavigate(directions: NavDirections) {
        try {
            navigate(directions)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 安全导航扩展函数
     * 避免快速点击导致的导航崩溃
     */
    fun NavController.safeNavigate(@IdRes resId: Int, args: Bundle? = null, navOptions: NavOptions? = null) {
        try {
            navigate(resId, args, navOptions)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Fragment扩展函数，安全导航
     */
    fun Fragment.safeNavigate(directions: NavDirections) {
        try {
            findNavController().navigate(directions)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Fragment扩展函数，安全导航
     */
    fun Fragment.safeNavigate(@IdRes resId: Int, args: Bundle? = null, navOptions: NavOptions? = null) {
        try {
            findNavController().navigate(resId, args, navOptions)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 创建标准导航选项
     */
    fun standardNavOptions() = NavOptions.Builder()
        .setEnterAnim(R.anim.slide_in_right)
        .setExitAnim(R.anim.slide_out_left)
        .setPopEnterAnim(R.anim.slide_in_left)
        .setPopExitAnim(R.anim.slide_out_right)
        .build()
    
    /**
     * 创建底部弹出导航选项
     */
    fun bottomSheetNavOptions() = NavOptions.Builder()
        .setEnterAnim(R.anim.slide_in_up)
        .setExitAnim(R.anim.slide_out_down)
        .setPopEnterAnim(R.anim.slide_in_down)
        .setPopExitAnim(R.anim.slide_out_up)
        .build()
}
