package com.example.aimusicplayer.ui.adapter

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.Reply
import com.example.aimusicplayer.databinding.ItemReplyBinding
import com.example.aimusicplayer.utils.ImageUtils
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * 回复适配器
 * 优化后的版本，添加了更多动画效果和交互体验
 */
class ReplyAdapter(
    private val onLikeClick: ((Reply) -> Unit)? = null,
    private val onReplyClick: ((Reply) -> Unit)? = null
) : ListAdapter<Reply, ReplyAdapter.ReplyViewHolder>(ReplyDiffCallback()) {

    // 最后一个动画位置，用于控制列表项动画
    private var lastAnimatedPosition = -1

    // 是否启用动画
    private var animationsEnabled = true

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReplyViewHolder {
        val binding = ItemReplyBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ReplyViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ReplyViewHolder, position: Int) {
        holder.bind(getItem(position))

        // 应用列表项动画
        if (animationsEnabled && position > lastAnimatedPosition) {
            // 加载动画
            val animation = AnimationUtils.loadAnimation(
                holder.itemView.context,
                R.anim.item_animation_comment_fade_in
            )

            // 设置延迟
            animation.startTime = System.currentTimeMillis() + position * 30L

            // 应用动画
            holder.itemView.startAnimation(animation)

            // 更新最后动画位置
            lastAnimatedPosition = position
        }
    }

    inner class ReplyViewHolder(
        private val binding: ItemReplyBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(reply: Reply) {
            // 设置用户信息
            binding.textUsername.text = reply.username

            // 使用优化的图片加载方式
            ImageUtils.load(
                binding.imageAvatar.context,
                reply.avatarUrl,
                binding.imageAvatar,
                R.drawable.default_avatar,
                R.drawable.default_avatar,
                isCircle = true,
                crossFade = true
            )

            // 设置回复内容
            binding.textReplyContent.text = reply.content

            // 设置回复时间 - 使用更友好的相对时间
            binding.textReplyTime.text = getRelativeTimeString(reply.createTime)

            // 设置点赞相关UI和事件
            setupLikeUI(reply)

            // 设置回复点击事件
            binding.textReplyToReply.setOnClickListener {
                // 添加触觉反馈
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val vibrator = it.context.getSystemService(Vibrator::class.java)
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                }

                // 添加动画效果
                val scaleDown = AnimatorSet().apply {
                    playTogether(
                        ObjectAnimator.ofFloat(it, "scaleX", 1f, 0.9f, 1f),
                        ObjectAnimator.ofFloat(it, "scaleY", 1f, 0.9f, 1f)
                    )
                    duration = 200
                    interpolator = DecelerateInterpolator()
                }
                scaleDown.start()

                // 调用回复回调
                onReplyClick?.invoke(reply)
            }
        }

        /**
         * 设置点赞相关UI和事件
         */
        private fun setupLikeUI(reply: Reply) {
            // 设置点赞数
            binding.textReplyLikeCount.text = reply.likeCount.toString()

            // 设置点赞状态 - 添加颜色变化
            if (reply.liked) {
                binding.imageReplyLike.setImageResource(R.drawable.ic_like_filled)
                binding.imageReplyLike.setColorFilter(
                    ContextCompat.getColor(binding.imageReplyLike.context, R.color.colorAccent)
                )
                binding.textReplyLikeCount.setTextColor(
                    ContextCompat.getColor(binding.textReplyLikeCount.context, R.color.colorAccent)
                )
            } else {
                binding.imageReplyLike.setImageResource(R.drawable.ic_like_outline)
                binding.imageReplyLike.clearColorFilter()
                binding.textReplyLikeCount.setTextColor(
                    ContextCompat.getColor(binding.textReplyLikeCount.context, R.color.text_secondary)
                )
            }

            // 设置点赞点击事件 - 添加动画和触觉反馈
            binding.layoutReplyLike.setOnClickListener {
                // 添加触觉反馈
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val vibrator = it.context.getSystemService(Vibrator::class.java)
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                }

                // 添加动画效果
                val scaleDown = AnimatorSet().apply {
                    playTogether(
                        ObjectAnimator.ofFloat(it, "scaleX", 1f, 0.9f, 1f),
                        ObjectAnimator.ofFloat(it, "scaleY", 1f, 0.9f, 1f)
                    )
                    duration = 200
                    interpolator = DecelerateInterpolator()
                }
                scaleDown.start()

                // 调用点赞回调
                onLikeClick?.invoke(reply)
            }
        }

        /**
         * 获取相对时间字符串
         * 例如：刚刚、5分钟前、1小时前、昨天、3天前、2023-05-20
         */
        private fun getRelativeTimeString(date: java.util.Date): String {
            val now = System.currentTimeMillis()
            val time = date.time
            val diff = now - time

            return when {
                diff < TimeUnit.MINUTES.toMillis(1) -> "刚刚"
                diff < TimeUnit.HOURS.toMillis(1) -> "${diff / TimeUnit.MINUTES.toMillis(1)}分钟前"
                diff < TimeUnit.DAYS.toMillis(1) -> "${diff / TimeUnit.HOURS.toMillis(1)}小时前"
                diff < TimeUnit.DAYS.toMillis(2) -> "昨天"
                diff < TimeUnit.DAYS.toMillis(7) -> "${diff / TimeUnit.DAYS.toMillis(1)}天前"
                else -> {
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    dateFormat.format(date)
                }
            }
        }
    }

    class ReplyDiffCallback : DiffUtil.ItemCallback<Reply>() {
        override fun areItemsTheSame(oldItem: Reply, newItem: Reply): Boolean {
            return oldItem.replyId == newItem.replyId
        }

        override fun areContentsTheSame(oldItem: Reply, newItem: Reply): Boolean {
            return oldItem == newItem
        }
    }
}
