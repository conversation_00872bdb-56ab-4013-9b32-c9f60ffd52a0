package com.example.aimusicplayer.ui.adapter

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.databinding.ItemCommentBinding
import com.example.aimusicplayer.utils.ImageUtils
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * 评论适配器
 * 优化后的版本，添加了更多动画效果和交互体验
 */
class CommentAdapter(
    private val onLikeClick: (Comment) -> Unit,
    private val onReplyClick: ((Comment) -> Unit)? = null,
    private val onMoreClick: ((Comment) -> Unit)? = null
) : ListAdapter<Comment, CommentAdapter.CommentViewHolder>(CommentDiffCallback()) {

    // 最后一个动画位置，用于控制列表项动画
    private var lastAnimatedPosition = -1

    // 是否启用动画
    private var animationsEnabled = true

    // 是否是第一次加载
    private var isFirstLoad = true

    // 处理器，用于延迟动画
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CommentViewHolder {
        val binding = ItemCommentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CommentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CommentViewHolder, position: Int) {
        holder.bind(getItem(position))

        // 应用列表项动画
        if (animationsEnabled && position > lastAnimatedPosition) {
            // 计算延迟时间，使动画错开
            val delay = if (isFirstLoad) position * 50L else 0L

            // 加载动画
            val animation = AnimationUtils.loadAnimation(
                holder.itemView.context,
                R.anim.item_animation_comment_fade_in
            )

            // 设置延迟
            animation.startTime = System.currentTimeMillis() + delay

            // 应用动画
            holder.itemView.startAnimation(animation)

            // 更新最后动画位置
            lastAnimatedPosition = position
        }
    }

    inner class CommentViewHolder(
        private val binding: ItemCommentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        // 回复列表是否展开
        private var isRepliesExpanded = false

        fun bind(comment: Comment) {
            // 设置用户信息
            binding.textCommentUsername.text = comment.username

            // 使用优化的图片加载方式
            ImageUtils.load(
                binding.imageCommentAvatar.context,
                comment.avatarUrl,
                binding.imageCommentAvatar,
                R.drawable.default_avatar,
                R.drawable.default_avatar,
                isCircle = true,
                crossFade = true
            )

            // 设置评论内容
            binding.textCommentContent.text = comment.content

            // 设置评论时间 - 使用更友好的相对时间
            binding.textCommentTime.text = getRelativeTimeString(comment.createTime)

            // 设置点赞数
            binding.textLikeCount.text = comment.likeCount.toString()

            // 设置点赞状态 - 添加颜色变化
            if (comment.liked) {
                binding.imageLike.setImageResource(R.drawable.ic_like_filled)
                binding.imageLike.setColorFilter(
                    ContextCompat.getColor(binding.imageLike.context, R.color.colorAccent)
                )
                binding.textLikeCount.setTextColor(
                    ContextCompat.getColor(binding.textLikeCount.context, R.color.colorAccent)
                )
            } else {
                binding.imageLike.setImageResource(R.drawable.ic_like_outline)
                binding.imageLike.clearColorFilter()
                binding.textLikeCount.setTextColor(
                    ContextCompat.getColor(binding.textLikeCount.context, R.color.text_secondary)
                )
            }

            // 设置回复数
            if (comment.replyCount > 0) {
                binding.textReplyCount.text = "${comment.replyCount}回复"
            } else {
                binding.textReplyCount.text = "回复"
            }

            // 设置点赞点击事件 - 添加动画和触觉反馈
            binding.layoutLike.setOnClickListener {
                // 添加触觉反馈
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val vibrator = it.context.getSystemService(Vibrator::class.java)
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                }

                // 添加动画效果
                val scaleDown = AnimatorSet().apply {
                    playTogether(
                        ObjectAnimator.ofFloat(binding.layoutLike, "scaleX", 1f, 0.9f, 1f),
                        ObjectAnimator.ofFloat(binding.layoutLike, "scaleY", 1f, 0.9f, 1f)
                    )
                    duration = 200
                    interpolator = DecelerateInterpolator()
                }
                scaleDown.start()

                // 调用点赞回调
                onLikeClick(comment)
            }

            // 设置回复点击事件
            binding.layoutReply.setOnClickListener {
                // 添加触觉反馈
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val vibrator = it.context.getSystemService(Vibrator::class.java)
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                }

                // 添加动画效果
                val scaleDown = AnimatorSet().apply {
                    playTogether(
                        ObjectAnimator.ofFloat(binding.layoutReply, "scaleX", 1f, 0.9f, 1f),
                        ObjectAnimator.ofFloat(binding.layoutReply, "scaleY", 1f, 0.9f, 1f)
                    )
                    duration = 200
                    interpolator = DecelerateInterpolator()
                }
                scaleDown.start()

                // 如果有回复，切换回复列表展开状态
                if (comment.replies.isNotEmpty()) {
                    toggleReplies(comment)
                } else {
                    // 调用回复回调
                    onReplyClick?.invoke(comment)
                }
            }

            // 设置更多操作点击事件
            binding.layoutMore.setOnClickListener {
                // 添加触觉反馈
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val vibrator = it.context.getSystemService(Vibrator::class.java)
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                }

                // 添加动画效果
                val scaleDown = AnimatorSet().apply {
                    playTogether(
                        ObjectAnimator.ofFloat(binding.layoutMore, "scaleX", 1f, 0.9f, 1f),
                        ObjectAnimator.ofFloat(binding.layoutMore, "scaleY", 1f, 0.9f, 1f)
                    )
                    duration = 200
                    interpolator = DecelerateInterpolator()
                }
                scaleDown.start()

                // 调用更多操作回调
                onMoreClick?.invoke(comment)
            }

            // 初始化回复列表
            if (comment.replies.isNotEmpty() && isRepliesExpanded) {
                setupRepliesList(comment)
            } else {
                binding.recyclerViewReplies.visibility = View.GONE
            }
        }

        /**
         * 切换回复列表展开状态
         */
        private fun toggleReplies(comment: Comment) {
            if (comment.replies.isEmpty()) return

            isRepliesExpanded = !isRepliesExpanded

            if (isRepliesExpanded) {
                // 展开回复列表
                setupRepliesList(comment)
                binding.recyclerViewReplies.visibility = View.VISIBLE

                // 添加展开动画
                binding.recyclerViewReplies.alpha = 0f
                binding.recyclerViewReplies.animate()
                    .alpha(1f)
                    .setDuration(300)
                    .start()
            } else {
                // 收起回复列表
                binding.recyclerViewReplies.animate()
                    .alpha(0f)
                    .setDuration(200)
                    .withEndAction {
                        binding.recyclerViewReplies.visibility = View.GONE
                    }
                    .start()
            }
        }

        /**
         * 设置回复列表
         */
        private fun setupRepliesList(comment: Comment) {
            if (binding.recyclerViewReplies.adapter == null) {
                binding.recyclerViewReplies.layoutManager = LinearLayoutManager(binding.recyclerViewReplies.context)
                val replyAdapter = ReplyAdapter()
                binding.recyclerViewReplies.adapter = replyAdapter
            }

            (binding.recyclerViewReplies.adapter as ReplyAdapter).submitList(comment.replies)
        }

        /**
         * 获取相对时间字符串
         * 例如：刚刚、5分钟前、1小时前、昨天、3天前、2023-05-20
         */
        private fun getRelativeTimeString(date: java.util.Date): String {
            val now = System.currentTimeMillis()
            val time = date.time
            val diff = now - time

            return when {
                diff < TimeUnit.MINUTES.toMillis(1) -> "刚刚"
                diff < TimeUnit.HOURS.toMillis(1) -> "${diff / TimeUnit.MINUTES.toMillis(1)}分钟前"
                diff < TimeUnit.DAYS.toMillis(1) -> "${diff / TimeUnit.HOURS.toMillis(1)}小时前"
                diff < TimeUnit.DAYS.toMillis(2) -> "昨天"
                diff < TimeUnit.DAYS.toMillis(7) -> "${diff / TimeUnit.DAYS.toMillis(1)}天前"
                else -> {
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    dateFormat.format(date)
                }
            }
        }
    }

    class CommentDiffCallback : DiffUtil.ItemCallback<Comment>() {
        override fun areItemsTheSame(oldItem: Comment, newItem: Comment): Boolean {
            return oldItem.commentId == newItem.commentId
        }

        override fun areContentsTheSame(oldItem: Comment, newItem: Comment): Boolean {
            return oldItem == newItem
        }
    }
}
