package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 评论响应
 * 用于解析评论API的响应
 */
data class CommentResponse(
    @SerializedName("code")
    val code: Int = 0,

    @SerializedName("message")
    val message: String? = null,

    @SerializedName("total")
    val total: Int = 0,

    @SerializedName("more")
    val hasMore: Boolean = false,

    @SerializedName("hotComments")
    val hotComments: List<CommentDto>? = null,

    @SerializedName("comments")
    val comments: List<CommentDto>? = null,

    @SerializedName("topComments")
    val topComments: List<CommentDto>? = null
)

/**
 * 评论DTO
 * 用于API响应解析，会被转换为Comment模型
 */
data class CommentDto(
    @SerializedName("commentId")
    val commentId: Long,

    @SerializedName("user")
    val user: UserDto,

    @SerializedName("content")
    val content: String,

    @SerializedName("time")
    val time: Long,

    @SerializedName("likedCount")
    val likedCount: Int,

    @SerializedName("liked")
    val liked: Boolean,

    @SerializedName("replyCount")
    val replyCount: Int = 0,

    @SerializedName("beReplied")
    val replies: List<ReplyDto>? = null
) {
    /**
     * 转换为Comment模型
     */
    fun toComment(): Comment {
        return Comment(
            commentId = commentId,
            userId = user.userId,
            username = user.nickname,
            avatarUrl = user.avatarUrl,
            content = content,
            createTime = java.util.Date(time),
            likeCount = likedCount,
            liked = liked,
            replyCount = replyCount,
            replies = replies?.map { it.toReply(commentId) } ?: emptyList()
        )
    }
}

/**
 * 回复DTO
 * 用于API响应解析，会被转换为Reply模型
 */
data class ReplyDto(
    @SerializedName("replyId")
    val replyId: Long,

    @SerializedName("user")
    val user: UserDto,

    @SerializedName("content")
    val content: String,

    @SerializedName("time")
    val time: Long,

    @SerializedName("likedCount")
    val likedCount: Int,

    @SerializedName("liked")
    val liked: Boolean
) {
    /**
     * 转换为Reply模型
     */
    fun toReply(commentId: Long): Reply {
        return Reply(
            replyId = replyId,
            commentId = commentId,
            userId = user.userId,
            username = user.nickname,
            avatarUrl = user.avatarUrl,
            content = content,
            createTime = java.util.Date(time),
            likeCount = likedCount,
            liked = liked
        )
    }
}

/**
 * 用户DTO
 * 用于API响应解析
 */
data class UserDto(
    @SerializedName("userId")
    val userId: Long,

    @SerializedName("nickname")
    val nickname: String,

    @SerializedName("avatarUrl")
    val avatarUrl: String
)
