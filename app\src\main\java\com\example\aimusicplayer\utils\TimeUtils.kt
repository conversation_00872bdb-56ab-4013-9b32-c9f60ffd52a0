package com.example.aimusicplayer.utils

/**
 * 时间工具类
 */
object TimeUtils {
    /**
     * 格式化时间
     * @param timeMs 时间（毫秒）
     * @return 格式化后的时间字符串，如 "03:45"
     */
    fun formatTime(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 格式化时间（带小时）
     * @param timeMs 时间（毫秒）
     * @return 格式化后的时间字符串，如 "01:23:45"
     */
    fun formatTimeWithHour(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }
}
