package com.example.aimusicplayer.model;

import java.util.List;

/**
 * 每日推荐歌曲响应模型
 */
public class RecommendSongsResponse {
    private int code;
    private Data data;
    
    public int getCode() {
        return code;
    }
    
    public Data getData() {
        return data;
    }
    
    public static class Data {
        private List<SongDetailResponse.Song> dailySongs;
        private List<RecommendReason> recommendReasons;
        
        public List<SongDetailResponse.Song> getDailySongs() {
            return dailySongs;
        }
        
        public List<RecommendReason> getRecommendReasons() {
            return recommendReasons;
        }
    }
    
    public static class RecommendReason {
        private long songId;
        private String reason;
        
        public long getSongId() {
            return songId;
        }
        
        public String getReason() {
            return reason;
        }
    }
}
