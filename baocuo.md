Executing tasks: [:app:assembleDebug] in project C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1

Configuration on demand is an incubating feature.
> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:checkKotlinGradlePluginConfigurationErrors
> Task :app:dataBindingMergeDependencyArtifactsDebug UP-TO-DATE
> Task :app:generateDebugResValues UP-TO-DATE
> Task :app:generateDebugResources UP-TO-DATE
> Task :app:mergeDebugResources UP-TO-DATE
> Task :app:packageDebugResources UP-TO-DATE
> Task :app:parseDebugLocalResources UP-TO-DATE
> Task :app:dataBindingGenBaseClassesDebug UP-TO-DATE
> Task :app:generateSafeArgsDebug UP-TO-DATE
> Task :app:checkDebugAarMetadata UP-TO-DATE
> Task :app:mapDebugSourceSetPaths UP-TO-DATE
> Task :app:createDebugCompatibleScreenManifests UP-TO-DATE
> Task :app:extractDeepLinksDebug UP-TO-DATE
> Task :app:processDebugMainManifest UP-TO-DATE
> Task :app:processDebugManifest UP-TO-DATE
> Task :app:processDebugManifestForPackage UP-TO-DATE
> Task :app:processDebugResources UP-TO-DATE
> Task :app:javaPreCompileDebug UP-TO-DATE
> Task :app:mergeDebugShaders UP-TO-DATE
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :app:mergeDebugAssets UP-TO-DATE
> Task :app:compressDebugAssets UP-TO-DATE
> Task :app:desugarDebugFileDependencies UP-TO-DATE
> Task :app:checkDebugDuplicateClasses UP-TO-DATE
> Task :app:mergeExtDexDebug UP-TO-DATE
> Task :app:mergeLibDexDebug UP-TO-DATE
> Task :app:mergeDebugJniLibFolders UP-TO-DATE
> Task :app:mergeDebugNativeLibs UP-TO-DATE
> Task :app:stripDebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningDebug UP-TO-DATE
> Task :app:writeDebugAppMetadata UP-TO-DATE
> Task :app:writeDebugSigningConfigVersions UP-TO-DATE
> Task :app:kspDebugKotlin

> Task :app:compileDebugKotlin
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/db/AppDatabase.kt:69:34 The corresponding parameter in the supertype 'Migration' is named 'db'. This may cause problems when calling this function with named arguments.
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/CommentRepository.kt:176:9 Parameter 'commentId' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:114:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:127:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:141:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:152:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:420:39 Unnecessary safe call on a non-null receiver of type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:421:72 Elvis operator (?:) always returns the left operand of non-nullable type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:442:39 Unnecessary safe call on a non-null receiver of type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:443:72 Elvis operator (?:) always returns the left operand of non-nullable type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/UserRepository.kt:540:57 Parameter 'subCount' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:456:32 Elvis operator (?:) always returns the left operand of non-nullable type Long
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:648:47 Parameter 'limit' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/error/GlobalErrorHandler.kt:172:55 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/service/UnifiedPlaybackService.kt:383:37 Parameter 'error' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/comment/CommentFragment.kt:213:74 'VIBRATOR_SERVICE: String' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:71:40 'FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:72:40 'FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:77:30 'SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:78:22 'SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:79:22 'SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:80:22 'SYSTEM_UI_FLAG_LAYOUT_STABLE: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:81:22 'SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:82:22 'SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:83:19 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:107:34 'SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:108:26 'SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:109:26 'SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:110:23 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:122:13 Variable 'progressBar' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:159:19 Enum argument can be null in Java, but exhaustive when contains no null branch
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:555:13 'overridePendingTransition(Int, Int): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:586:13 Variable 'etCaptcha' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/LyricView.kt:228:13 Variable 'visibleLineCount' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/LyricView.kt:250:49 Unnecessary non-null assertion (!!) on a non-null receiver of type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:235:13 Variable 'background' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:284:87 'VIBRATOR_SERVICE: String' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt:171:51 'overridePendingTransition(Int, Int): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:7:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:8:29 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:9:29 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:10:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:40:40 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:40:53 'create(Context!): RenderScript!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:133:31 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:133:42 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:134:32 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:134:43 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:26 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:46 'create(RenderScript!, Element!): ScriptIntrinsicBlur!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:67 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:75 'U8_4(RenderScript!): Element!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:138:20 'setRadius(Float): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:139:20 'setInput(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:140:20 'forEach(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:143:26 'copyTo(Bitmap!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:146:25 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:147:26 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:148:20 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:165:22 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtCache.kt:193:17 Condition 'bitmap != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtCache.kt:232:21 Condition 'palette != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtCache.kt:393:35 Parameter 'colorInfo' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumRotationController.kt:445:13 Variable 'animatorSet' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:6:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:7:29 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:8:29 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:9:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:109:18 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:109:31 'create(Context!): RenderScript!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:49 'create(RenderScript!, Element!): ScriptIntrinsicBlur!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:60 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:68 'U8_4(RenderScript!): Element!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:112:28 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:112:39 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:113:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:113:40 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:115:23 'setRadius(Float): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:116:23 'setInput(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:117:23 'forEach(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:119:23 'copyTo(Bitmap!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:121:12 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt:162:61 'VIBRATOR_SERVICE: String' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt:167:23 'vibrate(Long): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/CacheManager.kt:269:9 This is a delicate API and its use requires care. Make sure you fully read and understand documentation of the declaration that is marked as a delicate API.
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/DiffCallbacks.kt:56:16 Condition 'oldItem.mediaId != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/DiffCallbacks.kt:56:43 Condition 'newItem.mediaId != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:16:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:17:29 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:18:29 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:19:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:118:13 Variable 'rect' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:163:18 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:163:31 'create(Context!): RenderScript!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:26 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:46 'create(RenderScript!, Element!): ScriptIntrinsicBlur!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:57 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:65 'U8_4(RenderScript!): Element!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:165:21 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:165:32 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:166:22 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:166:33 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:167:20 'setRadius(Float): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:168:20 'setInput(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:169:20 'forEach(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:170:16 'copyTo(Bitmap!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:171:12 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:511:26 Unchecked cast: RequestBuilder<Drawable!> to RequestBuilder<Bitmap>
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:521:26 Unchecked cast: RequestBuilder<Drawable!> to RequestBuilder<Bitmap>
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:750:39 Parameter 'defaultColor' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/NetworkUtils.kt:35:67 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/NetworkUtils.kt:60:63 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/NetworkUtils.kt:85:67 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/PerformanceUtils.kt:65:41 'getWindowInsetsController(View): WindowInsetsControllerCompat?' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/PlaylistCache.kt:73:50 'get(String!): Any?' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:107:36 Parameter 'forceRefresh' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:210:21 Variable 'result' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:214:35 Parameter 'voiceResult' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:259:37 Parameter 'command' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:83:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:127:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:153:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:259:13 Variable 'lastError' is assigned but never accessed
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:269:17 The value 'e' assigned to 'var lastError: Throwable? defined in com.example.aimusicplayer.viewmodel.FlowViewModel.apiFlow.`<anonymous>`' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:316:13 Variable 'lastError' is assigned but never accessed
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:349:17 The value 'e' assigned to 'var lastError: Throwable? defined in com.example.aimusicplayer.viewmodel.FlowViewModel.apiResponseFlow.`<anonymous>`' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/LoginViewModel.kt:211:44 Variable 'username' initializer is redundant
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/LoginViewModel.kt:284:36 Variable 'username' initializer is redundant
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/LoginViewModel.kt:338:59 Parameter 'avatarUrl' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:182:16 Condition 'username != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:304:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:154:17 'when' is exhaustive so 'else' is redundant here

> Task :app:compileDebugJavaWithJavac
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\PlaylistAdapter.java:17: 错误: 找不到符号
import com.example.aimusicplayer.utils.DiffCallbacks;
                                      ^
  符号:   类 DiffCallbacks
  位置: 程序包 com.example.aimusicplayer.utils
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\IntelligenceListResponse.java:50: 错误: 找不到符号
            private List<Artist> artists;
                         ^
  符号:   类 Artist
  位置: 类 SongInfo
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\IntelligenceListResponse.java:53: 错误: 找不到符号
            private Album album;
                    ^
  符号:   类 Album
  位置: 类 SongInfo
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\IntelligenceListResponse.java:66: 错误: 找不到符号
            public List<Artist> getArtists() {
                        ^
  符号:   类 Artist
  位置: 类 SongInfo
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\IntelligenceListResponse.java:70: 错误: 找不到符号
            public Album getAlbum() {
                   ^
  符号:   类 Album
  位置: 类 SongInfo
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:16: 错误: 程序包com.example.aimusicplayer.util不存在
import com.example.aimusicplayer.util.ButtonAnimationUtils;
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:29: 错误: 程序包com.example.aimusicplayer.util不存在
import com.example.aimusicplayer.util.PermissionUtils;
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\ActivityMainBinding.java:17: 错误: 程序包com.example.aimusicplayer.view不存在
import com.example.aimusicplayer.view.LottieLoadingView;
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\ActivityMainBinding.java:33: 错误: 找不到符号
  public final LottieLoadingView loadingView;
               ^
  符号:   类 LottieLoadingView
  位置: 类 ActivityMainBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\ActivityMainBinding.java:78: 错误: 找不到符号
      @NonNull FrameLayout fragmentContainer, @NonNull LottieLoadingView loadingView,
                                                       ^
  符号:   类 LottieLoadingView
  位置: 类 ActivityMainBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogCommentBinding.java:20: 错误: 程序包com.example.aimusicplayer.view不存在
import com.example.aimusicplayer.view.LottieLoadingView;
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogCommentBinding.java:42: 错误: 找不到符号
  public final LottieLoadingView loadingViewComment;
               ^
  符号:   类 LottieLoadingView
  位置: 类 DialogCommentBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogCommentBinding.java:64: 错误: 找不到符号
      @NonNull FrameLayout layoutLoadMore, @NonNull LottieLoadingView loadingViewComment,
                                                    ^
  符号:   类 LottieLoadingView
  位置: 类 DialogCommentBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogIntelligenceBinding.java:16: 错误: 程序包com.example.aimusicplayer.view不存在
import com.example.aimusicplayer.view.LottieLoadingView;
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogIntelligenceBinding.java:29: 错误: 找不到符号
  public final LottieLoadingView loadingViewIntelligence;
               ^
  符号:   类 LottieLoadingView
  位置: 类 DialogIntelligenceBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogIntelligenceBinding.java:41: 错误: 找不到符号
      @NonNull ImageButton btnCloseIntelligence, @NonNull LottieLoadingView loadingViewIntelligence,
                                                          ^
  符号:   类 LottieLoadingView
  位置: 类 DialogIntelligenceBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\FragmentPlayerBinding.java:18: 错误: 程序包com.example.aimusicplayer.view不存在
import com.example.aimusicplayer.view.LottieLoadingView;
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\FragmentPlayerBinding.java:71: 错误: 找不到符号
  public final LottieLoadingView loadingView;
               ^
  符号:   类 LottieLoadingView
  位置: 类 FragmentPlayerBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\FragmentPlayerBinding.java:116: 错误: 找不到符号
      @NonNull LinearLayout controlContainer, @NonNull LottieLoadingView loadingView,
                                                       ^
  符号:   类 LottieLoadingView
  位置: 类 FragmentPlayerBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\PlaylistAdapter.java:104: 错误: 程序包DiffCallbacks不存在
                new DiffCallbacks.PlaylistDiffCallback(this.playlists, newPlaylists));
                                 ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\ApiManager.java:325: 错误: 无法从最终RetryInterceptor进行继承
        RetryInterceptor retryInterceptor = new RetryInterceptor(MAX_RETRY_COUNT, RETRY_INTERVAL) {
                                                ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\ApiManager.java:326: 错误: 方法不会覆盖或实现超类型的方法
            @Override
            ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\CookieInterceptor.java:42: 错误: 找不到符号
                    MusicApplication app = (MusicApplication) MusicApplication.getContext();
                                                                              ^
  符号:   方法 getContext()
  位置: 类 MusicApplication
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:33: 错误: 不兼容的类型: Song无法转换为String
        playerController.play(song);
                              ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:42: 错误: 无法将接口 PlayerController中的方法 play应用到给定类型;
        playerController.play(songs, startIndex);
                        ^
  需要: String
  找到:    List<Song>,int
  原因: 实际参数列表和形式参数列表长度不同
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:49: 错误: 找不到符号
        playerController.togglePlayPause();
                        ^
  符号:   方法 togglePlayPause()
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:56: 错误: 无法将接口 PlayerController中的方法 play应用到给定类型;
        playerController.play();
                        ^
  需要: String
  找到:    没有参数
  原因: 实际参数列表和形式参数列表长度不同
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:63: 错误: 找不到符号
        playerController.pause();
                        ^
  符号:   方法 pause()
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:84: 错误: 找不到符号
        playerController.previous();
                        ^
  符号:   方法 previous()
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:92: 错误: 不兼容的类型: 从long转换到int可能会有损失
        playerController.seekTo(position);
                                ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:100: 错误: 不兼容的类型: int无法转换为PlayMode
        playerController.setPlayMode(mode);
                                     ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:107: 错误: 找不到符号
        playerController.togglePlayMode();
                        ^
  符号:   方法 togglePlayMode()
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:115: 错误: 不兼容的类型: StateFlow<PlayState>无法转换为LiveData<PlayState>
        return playerController.getPlayState();
                                            ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:123: 错误: 不兼容的类型: StateFlow<PlayMode>无法转换为int
        return playerController.getPlayMode();
                                           ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:147: 错误: 对getPlaylist的引用不明确
        return playerController.getPlaylist();
                               ^
  PlayerController 中的方法 getPlaylist() 和 PlayerController 中的方法 getPlaylist() 都匹配
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:147: 错误: 不兼容的类型: LiveData<List<MediaItem>>无法转换为List<MediaItem>
        return playerController.getPlaylist();
                                           ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:163: 错误: 找不到符号
        return playerController.getPlayer();
                               ^
  符号:   方法 getPlayer()
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:171: 错误: 找不到符号
        playerController.addToPlaylist(song);
                        ^
  符号:   方法 addToPlaylist(Song)
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:179: 错误: 找不到符号
        playerController.addToPlaylist(songs);
                        ^
  符号:   方法 addToPlaylist(List<Song>)
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:187: 错误: 找不到符号
        playerController.removeFromPlaylist(index);
                        ^
  符号:   方法 removeFromPlaylist(int)
  位置: 类型为PlayerController的变量 playerController
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:104: 错误: 无法将接口 Factory中的方法 from应用到给定类型;
            viewModel = new ViewModelProvider(this, ViewModelProvider.Factory.from(MainViewModel.class)).get(MainViewModel.class);
                                                                             ^
  需要: ViewModelInitializer<?>[]
  找到:    Class<MainViewModel>
  原因: varargs 不匹配; Class<MainViewModel>无法转换为ViewModelInitializer<?>
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:179: 错误: 无法从静态上下文中引用非静态 方法 standardNavOptions()
                NavOptions navOptions = NavigationUtils.standardNavOptions();
                                                       ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:185: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navPlayer, navPlayerIndicator);
                                                                     ^
  符号:   变量 navPlayer
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:190: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navLibrary, navLibraryIndicator);
                                                                     ^
  符号:   变量 navLibrary
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:195: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navDiscovery, navDiscoveryIndicator);
                                                                     ^
  符号:   变量 navDiscovery
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:200: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navDriving, navDrivingIndicator);
                                                                     ^
  符号:   变量 navDriving
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:206: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navProfile, navProfileIndicator);
                                                                     ^
  符号:   变量 navProfile
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:211: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navSettings, navSettingsIndicator);
                                                                     ^
  符号:   变量 navSettings
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:269: 错误: 找不到符号
            ButtonAnimationUtils.addTouchFeedback(button);
            ^
  符号:   变量 ButtonAnimationUtils
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:280: 错误: 不兼容的类型: View无法转换为LinearLayout
        viewModel.initializeSidebarController(sidebarNav, btnMenuRight, fragmentContainer, navButtons);
                                              ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:291: 错误: 程序包com.example.aimusicplayer.view不存在
            com.example.aimusicplayer.view.LottieLoadingView loadingView = binding.loadingView;
                                          ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:298: 错误: 无法取消引用void
            viewModel.checkLoginStatus().observe(this, loginStatus -> {
                                        ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:345: 错误: 无法取消引用void
        viewModel.getUserDetail(userId).observe(this, userDetail -> {
                                       ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:398: 错误: 找不到符号
        ((android.widget.ImageView)navPlayer).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navPlayer
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:399: 错误: 找不到符号
        ((android.widget.ImageView)navLibrary).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navLibrary
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:400: 错误: 找不到符号
        ((android.widget.ImageView)navDiscovery).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navDiscovery
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:401: 错误: 找不到符号
        ((android.widget.ImageView)navDriving).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navDriving
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:402: 错误: 找不到符号
        ((android.widget.ImageView)navProfile).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navProfile
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:403: 错误: 找不到符号
        ((android.widget.ImageView)navSettings).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navSettings
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:434: 错误: 找不到符号
            String[] musicPermissions = PermissionUtils.getMusicPlaybackPermissions();
                                        ^
  符号:   变量 PermissionUtils
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:437: 错误: 找不到符号
            if (!PermissionUtils.hasPermissions(this, musicPermissions)) {
                 ^
  符号:   变量 PermissionUtils
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:438: 错误: 找不到符号
                PermissionUtils.requestPermissions(this, PERMISSIONS_REQUEST_CODE, musicPermissions);
                ^
  符号:   变量 PermissionUtils
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:442: 错误: 找不到符号
            String[] voicePermissions = PermissionUtils.getVoicePermissions();
                                        ^
  符号:   变量 PermissionUtils
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:443: 错误: 找不到符号
            if (!PermissionUtils.hasPermissions(this, voicePermissions)) {
                 ^
  符号:   变量 PermissionUtils
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\ActivityMainBinding.java:146: 错误: 找不到符号
      LottieLoadingView loadingView = ViewBindings.findChildViewById(rootView, id);
      ^
  符号:   类 LottieLoadingView
  位置: 类 ActivityMainBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\splash\SplashActivity.java:108: 错误: 找不到符号
            viewModel.startSplashCountdown();
                     ^
  符号:   方法 startSplashCountdown()
  位置: 类型为SplashViewModel的变量 viewModel
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:101: 错误: 无法将类 LyricEntry中的构造器 LyricEntry应用到给定类型;
                    entries.add(new LyricEntry(time, text));
                                ^
  需要: long,String,String
  找到:    Long,String
  原因: 实际参数列表和形式参数列表长度不同
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogCommentBinding.java:134: 错误: 找不到符号
      LottieLoadingView loadingViewComment = ViewBindings.findChildViewById(rootView, id);
      ^
  符号:   类 LottieLoadingView
  位置: 类 DialogCommentBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\DialogIntelligenceBinding.java:86: 错误: 找不到符号
      LottieLoadingView loadingViewIntelligence = ViewBindings.findChildViewById(rootView, id);
      ^
  符号:   类 LottieLoadingView
  位置: 类 DialogIntelligenceBinding
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\data_binding_base_class_source_out\debug\out\com\example\aimusicplayer\databinding\FragmentPlayerBinding.java:265: 错误: 找不到符号
      LottieLoadingView loadingView = ViewBindings.findChildViewById(rootView, id);
      ^
  符号:   类 LottieLoadingView
  位置: 类 FragmentPlayerBinding
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些消息已经过简化; 请使用 -Xdiags:verbose 重新编译以获得完整输出
70 个错误

> Task :app:compileDebugJavaWithJavac FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugJavaWithJavac'.
> Compilation failed; see the compiler error output for details.

* Try:
> Run with --info option to get more log output.
> Run with --scan to get full insights.

BUILD FAILED in 35s
34 actionable tasks: 4 executed, 30 up-to-date
