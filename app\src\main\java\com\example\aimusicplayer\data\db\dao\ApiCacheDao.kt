package com.example.aimusicplayer.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.example.aimusicplayer.data.db.entity.ApiCacheEntity

/**
 * API缓存DAO接口
 */
@Dao
interface ApiCacheDao {
    /**
     * 插入缓存
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(cache: ApiCacheEntity)
    
    /**
     * 根据缓存键获取缓存
     */
    @Query("SELECT * FROM api_cache WHERE cache_key = :cacheKey")
    suspend fun getCache(cacheKey: String): ApiCacheEntity?
    
    /**
     * 根据缓存键删除缓存
     */
    @Query("DELETE FROM api_cache WHERE cache_key = :cacheKey")
    suspend fun deleteCache(cacheKey: String)
    
    /**
     * 清空所有缓存
     */
    @Query("DELETE FROM api_cache")
    suspend fun clearAll()
    
    /**
     * 清除过期缓存
     */
    @Query("DELETE FROM api_cache WHERE (cache_time + expiration_time) < :currentTime")
    suspend fun clearExpiredCache(currentTime: Long = System.currentTimeMillis())
    
    /**
     * 根据缓存类型清除缓存
     */
    @Query("DELETE FROM api_cache WHERE cache_type = :cacheType")
    suspend fun clearCacheByType(cacheType: String)
    
    /**
     * 获取所有缓存
     */
    @Query("SELECT * FROM api_cache")
    suspend fun getAllCache(): List<ApiCacheEntity>
    
    /**
     * 获取缓存数量
     */
    @Query("SELECT COUNT(*) FROM api_cache")
    suspend fun getCacheCount(): Int
    
    /**
     * 获取缓存大小（字节）
     */
    @Query("SELECT SUM(LENGTH(data)) FROM api_cache")
    suspend fun getCacheSize(): Long?
}
