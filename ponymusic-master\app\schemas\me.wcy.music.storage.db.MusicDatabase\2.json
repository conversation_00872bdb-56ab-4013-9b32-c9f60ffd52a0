{"formatVersion": 1, "database": {"version": 2, "identityHash": "4cdd292ca9f7d6fb016ef05faea1d0a5", "entities": [{"tableName": "play_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`type` INTEGER NOT NULL, `song_id` INTEGER NOT NULL, `title` TEXT NOT NULL, `artist` TEXT NOT NULL, `artist_id` INTEGER NOT NULL, `album` TEXT NOT NULL, `album_id` INTEGER NOT NULL, `album_cover` TEXT NOT NULL, `duration` INTEGER NOT NULL, `uri` TEXT NOT NULL DEFAULT '', `path` TEXT NOT NULL, `file_name` TEXT NOT NULL, `file_size` INTEGER NOT NULL, `unique_id` TEXT NOT NULL, PRIMARY KEY(`unique_id`))", "fields": [{"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "song_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artist", "columnName": "artist", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artist_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "album", "columnName": "album", "affinity": "TEXT", "notNull": true}, {"fieldPath": "albumId", "columnName": "album_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "albumCover", "columnName": "album_cover", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uri", "columnName": "uri", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileName", "columnName": "file_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileSize", "columnName": "file_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uniqueId", "columnName": "unique_id", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["unique_id"]}, "indices": [{"name": "index_play_list_title", "unique": false, "columnNames": ["title"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_play_list_title` ON `${TABLE_NAME}` (`title`)"}, {"name": "index_play_list_artist", "unique": false, "columnNames": ["artist"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_play_list_artist` ON `${TABLE_NAME}` (`artist`)"}, {"name": "index_play_list_album", "unique": false, "columnNames": ["album"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_play_list_album` ON `${TABLE_NAME}` (`album`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '4cdd292ca9f7d6fb016ef05faea1d0a5')"]}}