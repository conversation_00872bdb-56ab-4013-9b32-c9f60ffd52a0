package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 专辑数据模型
 * 用于统一项目中的专辑数据结构
 */
data class Album(
    var id: String = "",
    var name: String = "",
    var artistId: String = "",
    var artistName: String = "",
    var coverUrl: String = "",
    
    @SerializedName("picUrl")
    var picUrl: String? = null,
    
    var description: String = "",
    var songCount: Int = 0,
    var publishTime: String = "",
    var songs: MutableList<Song> = mutableListOf(),
    var artists: List<Artist>? = null
) {
    /**
     * 获取图片URL
     * 兼容方法，优先返回picUrl，如果为空则返回coverUrl
     */
    fun getPicUrl(): String {
        return picUrl ?: coverUrl
    }
    
    /**
     * 设置图片URL
     * 同时设置coverUrl以保持兼容性
     */
    fun setPicUrl(url: String) {
        picUrl = url
        // 同时设置coverUrl以保持兼容性
        if (coverUrl.isEmpty()) {
            coverUrl = url
        }
    }
    
    /**
     * 添加歌曲
     */
    fun addSong(song: Song) {
        songs.add(song)
    }
    
    /**
     * 从Java版本的Album创建
     */
    companion object {
        @JvmStatic
        fun fromJavaAlbum(album: com.example.aimusicplayer.model.Album): Album {
            return Album(
                id = album.id ?: "",
                name = album.name ?: "",
                artistId = album.artistId ?: "",
                artistName = album.artistName ?: "",
                coverUrl = album.coverUrl ?: "",
                picUrl = album.picUrl,
                description = album.description ?: "",
                songCount = album.songCount,
                publishTime = album.publishTime ?: "",
                songs = album.songs?.map { Song.fromJavaSong(it) }?.toMutableList() ?: mutableListOf(),
                artists = album.artists?.map { Artist.fromJavaArtist(it) }
            )
        }
    }
    
    /**
     * 转换为Java版本的Album
     */
    fun toJavaAlbum(): com.example.aimusicplayer.model.Album {
        val javaAlbum = com.example.aimusicplayer.model.Album()
        javaAlbum.id = id
        javaAlbum.name = name
        javaAlbum.artistId = artistId
        javaAlbum.artistName = artistName
        javaAlbum.coverUrl = coverUrl
        javaAlbum.picUrl = picUrl
        javaAlbum.description = description
        javaAlbum.songCount = songCount
        javaAlbum.publishTime = publishTime
        javaAlbum.songs = songs.map { it.toJavaSong() } as List<com.example.aimusicplayer.model.SongDetailResponse.Song>
        javaAlbum.artists = artists?.map { it.toJavaArtist() }
        return javaAlbum
    }
}
