package com.example.aimusicplayer.data.repository

import android.util.Log
import com.example.aimusicplayer.data.source.ApiService
import com.example.aimusicplayer.data.cache.ApiCacheManager
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.CommentResponse
import com.example.aimusicplayer.utils.NetworkResult
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 评论仓库
 * 负责获取和管理评论数据
 */
@Singleton
class CommentRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "CommentRepository"

        // 缓存键前缀
        private const val CACHE_KEY_SONG_COMMENTS = "song_comments_"
        private const val CACHE_KEY_ALBUM_COMMENTS = "album_comments_"
        private const val CACHE_KEY_PLAYLIST_COMMENTS = "playlist_comments_"

        // 缓存过期时间（10分钟）
        private const val COMMENTS_CACHE_EXPIRATION = 10 * 60 * 1000L
    }

    /**
     * 获取歌曲评论
     * @param id 歌曲ID
     * @param limit 每页数量
     * @param offset 偏移量
     * @param forceRefresh 是否强制刷新
     * @return Flow<NetworkResult<CommentResponse>>
     */
    fun getSongComments(
        id: Long,
        limit: Int = 20,
        offset: Int = 0,
        forceRefresh: Boolean = false
    ): Flow<NetworkResult<CommentResponse>> {
        val cacheKey = "$CACHE_KEY_SONG_COMMENTS${id}_${limit}_${offset}"

        return cachedApiCall(
            cacheKey = cacheKey,
            forceRefresh = forceRefresh,
            cacheExpiration = COMMENTS_CACHE_EXPIRATION,
            cacheType = ApiCacheManager.CACHE_TYPE_COMMENT
        ) {
            val response = apiService.getSongComments(id, limit, offset)
            if (response.code == 200) {
                response
            } else {
                throw Exception("获取歌曲评论失败: ${response.message}")
            }
        }
    }

    /**
     * 获取专辑评论
     * @param id 专辑ID
     * @param limit 每页数量
     * @param offset 偏移量
     * @param forceRefresh 是否强制刷新
     * @return Flow<NetworkResult<CommentResponse>>
     */
    fun getAlbumComments(
        id: Long,
        limit: Int = 20,
        offset: Int = 0,
        forceRefresh: Boolean = false
    ): Flow<NetworkResult<CommentResponse>> {
        val cacheKey = "$CACHE_KEY_ALBUM_COMMENTS${id}_${limit}_${offset}"

        return cachedApiCall(
            cacheKey = cacheKey,
            forceRefresh = forceRefresh,
            cacheExpiration = COMMENTS_CACHE_EXPIRATION,
            cacheType = ApiCacheManager.CACHE_TYPE_COMMENT
        ) {
            val response = apiService.getAlbumComments(id, limit, offset)
            if (response.code == 200) {
                response
            } else {
                throw Exception("获取专辑评论失败: ${response.message}")
            }
        }
    }

    /**
     * 获取歌单评论
     * @param id 歌单ID
     * @param limit 每页数量
     * @param offset 偏移量
     * @param forceRefresh 是否强制刷新
     * @return Flow<NetworkResult<CommentResponse>>
     */
    fun getPlaylistComments(
        id: Long,
        limit: Int = 20,
        offset: Int = 0,
        forceRefresh: Boolean = false
    ): Flow<NetworkResult<CommentResponse>> {
        val cacheKey = "$CACHE_KEY_PLAYLIST_COMMENTS${id}_${limit}_${offset}"

        return cachedApiCall(
            cacheKey = cacheKey,
            forceRefresh = forceRefresh,
            cacheExpiration = COMMENTS_CACHE_EXPIRATION,
            cacheType = ApiCacheManager.CACHE_TYPE_COMMENT
        ) {
            val response = apiService.getPlaylistComments(id, limit, offset)
            if (response.code == 200) {
                response
            } else {
                throw Exception("获取歌单评论失败: ${response.message}")
            }
        }
    }

    /**
     * 发送评论
     * @param type 评论类型（0: 歌曲, 1: MV, 2: 歌单, 3: 专辑, 4: 电台, 5: 视频, 6: 动态）
     * @param id 资源ID
     * @param content 评论内容
     * @param commentId 回复的评论ID（如果是回复评论）
     * @return Flow<NetworkResult<Any>>
     */
    fun sendComment(
        type: Int,
        id: Long,
        content: String,
        commentId: Long? = null
    ): Flow<NetworkResult<Any>> {
        return apiFlow {
            val response = if (commentId != null) {
                // 回复评论
                apiService.replyComment(id, commentId, content, type)
            } else {
                // 发送评论
                apiService.sendComment(id, content, type)
            }

            if (response.code == 200) {
                // 清除相关缓存
                when (type) {
                    0 -> apiCacheManager.deleteCache("$CACHE_KEY_SONG_COMMENTS$id")
                    2 -> apiCacheManager.deleteCache("$CACHE_KEY_PLAYLIST_COMMENTS$id")
                    3 -> apiCacheManager.deleteCache("$CACHE_KEY_ALBUM_COMMENTS$id")
                }

                response
            } else {
                throw Exception("发送评论失败: ${response.message}")
            }
        }
    }

    /**
     * 点赞评论
     * @param type 评论类型（0: 歌曲, 1: MV, 2: 歌单, 3: 专辑, 4: 电台, 5: 视频, 6: 动态）
     * @param id 资源ID
     * @param commentId 评论ID
     * @param like 是否点赞（true: 点赞, false: 取消点赞）
     * @return Flow<NetworkResult<Any>>
     */
    fun likeComment(
        type: Int,
        id: Long,
        commentId: Long,
        like: Boolean
    ): Flow<NetworkResult<Any>> {
        return apiFlow {
            val response = apiService.likeComment(type.toLong(), id, if(like) 1 else 0)

            if (response.code == 200) {
                // 清除相关缓存
                when (type) {
                    0 -> apiCacheManager.deleteCache("$CACHE_KEY_SONG_COMMENTS$id")
                    2 -> apiCacheManager.deleteCache("$CACHE_KEY_PLAYLIST_COMMENTS$id")
                    3 -> apiCacheManager.deleteCache("$CACHE_KEY_ALBUM_COMMENTS$id")
                }

                response
            } else {
                throw Exception("点赞评论失败: ${response.message}")
            }
        }
    }
}
