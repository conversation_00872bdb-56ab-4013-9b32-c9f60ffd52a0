package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.palette.graphics.Palette
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 专辑封面处理器
 * 负责加载专辑封面并提取颜色
 * 使用Flow管理状态
 * 优化版本：使用缓存机制避免重复处理
 */
@Singleton
class AlbumArtProcessor @Inject constructor() {
    companion object {
        private const val TAG = "AlbumArtProcessor"
    }

    // 提取的颜色 - 使用StateFlow
    private val _dominantColorFlow = MutableStateFlow(Color.BLACK)
    val dominantColorFlow: StateFlow<Int> = _dominantColorFlow.asStateFlow()

    private val _vibrantColorFlow = MutableStateFlow(Color.parseColor("#3376FF"))
    val vibrantColorFlow: StateFlow<Int> = _vibrantColorFlow.asStateFlow()

    private val _textColorFlow = MutableStateFlow(Color.WHITE)
    val textColorFlow: StateFlow<Int> = _textColorFlow.asStateFlow()

    private val _albumArtBitmapFlow = MutableStateFlow<Bitmap?>(null)
    val albumArtBitmapFlow: StateFlow<Bitmap?> = _albumArtBitmapFlow.asStateFlow()

    private val _isProcessingFlow = MutableStateFlow(false)
    val isProcessingFlow: StateFlow<Boolean> = _isProcessingFlow.asStateFlow()

    // 兼容LiveData
    val dominantColor: LiveData<Int> = MutableLiveData(_dominantColorFlow.value)
    val vibrantColor: LiveData<Int> = MutableLiveData(_vibrantColorFlow.value)
    val textColor: LiveData<Int> = MutableLiveData(_textColorFlow.value)
    val albumArtBitmap: LiveData<Bitmap?> = MutableLiveData(_albumArtBitmapFlow.value)
    val isProcessing: LiveData<Boolean> = MutableLiveData(_isProcessingFlow.value)

    // 当前处理的URL
    private var currentUrl: String? = null

    /**
     * 加载专辑封面并提取颜色
     * 优化版本：使用缓存机制避免重复处理
     * @param context 上下文
     * @param coverUrl 封面URL
     */
    suspend fun processAlbumArt(context: Context, coverUrl: String?) {
        if (coverUrl.isNullOrEmpty()) {
            Log.e(TAG, "封面URL为空")
            _dominantColorFlow.value = Color.BLACK
            _vibrantColorFlow.value = Color.parseColor("#3376FF")
            _textColorFlow.value = Color.WHITE
            return
        }

        // 如果是相同的URL，不重复处理
        if (coverUrl == currentUrl && _albumArtBitmapFlow.value != null) {
            Log.d(TAG, "相同的URL，不重复处理: $coverUrl")
            return
        }

        currentUrl = coverUrl
        _isProcessingFlow.value = true

        // 尝试从缓存获取专辑封面
        val cachedAlbumArt = AlbumArtCache.getInstance().getAlbumArt(coverUrl)
        if (cachedAlbumArt != null) {
            Log.d(TAG, "从缓存获取专辑封面: $coverUrl")
            _albumArtBitmapFlow.value = cachedAlbumArt

            // 尝试从缓存获取颜色信息
            val colorInfo = AlbumArtCache.getInstance().getColorInfo(coverUrl)
            if (colorInfo != null) {
                Log.d(TAG, "从缓存获取颜色信息: $coverUrl")
                _dominantColorFlow.value = colorInfo.dominantColor
                _vibrantColorFlow.value = colorInfo.vibrantColor
                _textColorFlow.value = colorInfo.textColor
                _isProcessingFlow.value = false
                return
            }

            // 如果有封面但没有颜色信息，从封面提取颜色
            extractColorsFromBitmap(cachedAlbumArt, coverUrl)
            return
        }

        // 使用Glide加载图片并提取颜色
        withContext(Dispatchers.IO) {
            try {
                Glide.with(context)
                    .asBitmap()
                    .load(coverUrl)
                    .listener(object : RequestListener<Bitmap> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Bitmap?>, // Changed to Target<Bitmap?>
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.e(TAG, "加载专辑封面失败", e)
                            _isProcessingFlow.value = false
                            return false
                        }

                        override fun onResourceReady(
                            resource: Bitmap,
                            model: Any, // Changed to Any
                            target: Target<Bitmap?>, // Changed to Target<Bitmap?>
                            dataSource: DataSource, // Changed to DataSource
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "专辑封面加载成功: $coverUrl")
                            // 保存专辑封面到缓存
                            AlbumArtCache.getInstance().putAlbumArt(coverUrl, resource)

                            // 更新StateFlow
                            _albumArtBitmapFlow.value = resource

                            // 提取颜色
                            CoroutineScope(Dispatchers.Main).launch {
                                extractColorsFromBitmap(resource, coverUrl)
                            }
                            return false
                        }
                    })
                    .submit()
            } catch (e: Exception) {
                Log.e(TAG, "加载专辑封面失败", e)
                _isProcessingFlow.value = false
            }
        }
    }

    /**
     * 从位图提取颜色
     * @param bitmap 位图
     * @param url 封面URL，用于缓存
     */
    private suspend fun extractColorsFromBitmap(bitmap: Bitmap, url: String) {
        withContext(Dispatchers.Default) {
            try {
                // 使用Palette库从图片中提取颜色
                val palette = Palette.Builder(bitmap)
                    .maximumColorCount(24) // 提取更多颜色，获得更准确的结果
                    .generate()

                // 使用缓存管理器提取颜色信息
                val colorInfo = AlbumArtCache.extractColorInfo(palette)

                // 更新StateFlow
                _dominantColorFlow.value = colorInfo.dominantColor
                _vibrantColorFlow.value = colorInfo.vibrantColor
                _textColorFlow.value = colorInfo.textColor

                // 保存颜色信息到缓存
                AlbumArtCache.getInstance().putColorInfo(url, colorInfo)
            } catch (e: Exception) {
                Log.e(TAG, "提取颜色失败", e)
            } finally {
                _isProcessingFlow.value = false
            }
        }
    }
}
