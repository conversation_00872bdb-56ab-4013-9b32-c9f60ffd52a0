package com.example.aimusicplayer.model;

/**
 * 用户模型类
 * 存储用户基本信息
 */
public class User {
    private String userId;
    private String username;
    private String avatarUrl;
    private String token;      // 用户登录令牌
    private boolean isVip;
    private int level;
    private String signature;
    private int followeds; // 粉丝数
    private int follows;   // 关注数

    public User() {
    }

    public User(String userId, String username, String avatarUrl) {
        this.userId = userId;
        this.username = username;
        this.avatarUrl = avatarUrl;
    }

    public User(String userId, String username, String token, String avatarUrl) {
        this.userId = userId;
        this.username = username;
        this.token = token;
        this.avatarUrl = avatarUrl;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public int getFolloweds() {
        return followeds;
    }

    public void setFolloweds(int followeds) {
        this.followeds = followeds;
    }

    public int getFollows() {
        return follows;
    }

    public void setFollows(int follows) {
        this.follows = follows;
    }

    @Override
    public String toString() {
        return "User{" +
                "userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", token='" + token + '\'' +
                ", isVip=" + isVip +
                ", level=" + level +
                ", signature='" + signature + '\'' +
                ", followeds=" + followeds +
                ", follows=" + follows +
                '}';
    }
}
