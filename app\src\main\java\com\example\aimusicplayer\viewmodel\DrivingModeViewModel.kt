package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.data.repository.SettingsRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 驾驶模式的ViewModel
 * 负责处理驾驶模式的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class DrivingModeViewModel @Inject constructor(
    application: Application,
    private val musicRepository: MusicRepository,
    private val settingsRepository: SettingsRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "DrivingModeViewModel"
    }

    // 是否自动开启语音的StateFlow
    private val _isAutoVoiceEnabledFlow = MutableStateFlow<Boolean>(false)
    val isAutoVoiceEnabledFlow: StateFlow<Boolean> = _isAutoVoiceEnabledFlow.asStateFlow()
    val isAutoVoiceEnabled: LiveData<Boolean> = isAutoVoiceEnabledFlow.asLiveData() // 兼容LiveData

    // 是否正在语音模式的StateFlow
    private val _isVoiceModeActiveFlow = MutableStateFlow<Boolean>(false)
    val isVoiceModeActiveFlow: StateFlow<Boolean> = _isVoiceModeActiveFlow.asStateFlow()
    val isVoiceModeActive: LiveData<Boolean> = isVoiceModeActiveFlow.asLiveData() // 兼容LiveData

    // 推荐歌曲列表的StateFlow
    private val _recommendedSongListFlow = MutableStateFlow<List<Song>>(emptyList())
    val recommendedSongListFlow: StateFlow<List<Song>> = _recommendedSongListFlow.asStateFlow()
    val recommendedSongList: LiveData<List<Song>> = recommendedSongListFlow.asLiveData() // 兼容LiveData

    // 语音识别结果的StateFlow
    private val _voiceRecognitionResultFlow = MutableStateFlow<String>("")
    val voiceRecognitionResultFlow: StateFlow<String> = _voiceRecognitionResultFlow.asStateFlow()
    val voiceRecognitionResult: LiveData<String> = voiceRecognitionResultFlow.asLiveData() // 兼容LiveData

    // 语音命令的StateFlow
    private val _voiceCommandFlow = MutableStateFlow<String>("")
    val voiceCommandFlow: StateFlow<String> = _voiceCommandFlow.asStateFlow()
    val voiceCommand: LiveData<String> = voiceCommandFlow.asLiveData() // 兼容LiveData

    init {
        // 加载设置
        loadSettings()
    }

    /**
     * 加载设置
     */
    private fun loadSettings() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载设置失败", e)
                handleError(e, "加载设置失败: ${e.message}")
            }
        ) {
            try {
                // 从SettingsRepository加载设置
                val autoVoice = settingsRepository.isAutoVoiceInDrivingEnabled()
                _isAutoVoiceEnabledFlow.value = autoVoice

                // 如果自动开启语音，则激活语音模式
                if (autoVoice) {
                    activateVoiceMode()
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载设置失败", e)
                throw e
            }
        }
    }

    /**
     * 激活语音模式
     */
    fun activateVoiceMode() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "激活语音模式失败", e)
                handleError(e, "激活语音模式失败: ${e.message}")
            }
        ) {
            try {
                // TODO: 激活语音模式
                _isVoiceModeActiveFlow.value = true
            } catch (e: Exception) {
                Log.e(TAG, "激活语音模式失败", e)
                throw e
            }
        }
    }

    /**
     * 停用语音模式
     */
    fun deactivateVoiceMode() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "停用语音模式失败", e)
                handleError(e, "停用语音模式失败: ${e.message}")
            }
        ) {
            try {
                // TODO: 停用语音模式
                _isVoiceModeActiveFlow.value = false
            } catch (e: Exception) {
                Log.e(TAG, "停用语音模式失败", e)
                throw e
            }
        }
    }

    /**
     * 加载推荐歌曲
     */
    fun loadRecommendedSongs() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载推荐歌曲失败", e)
                handleError(e, "加载推荐歌曲失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载推荐歌曲
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getRecommendedSongs()
                }

                when (val networkResult = result) {
                    is NetworkResult.Success -> {
                        _recommendedSongListFlow.value = networkResult.data
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(networkResult.message), networkResult.message)
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 处理语音识别结果
     * @param result 语音识别结果
     */
    fun processVoiceRecognitionResult(result: String) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "处理语音识别结果失败", e)
                handleError(e, "处理语音识别结果失败: ${e.message}")
            }
        ) {
            try {
                _voiceRecognitionResultFlow.value = result

                // 解析语音命令
                val command = parseVoiceCommand(result)
                _voiceCommandFlow.value = command

                // 执行语音命令
                executeVoiceCommand(command)
            } catch (e: Exception) {
                Log.e(TAG, "处理语音识别结果失败", e)
                throw e
            }
        }
    }

    /**
     * 解析语音命令
     * @param voiceResult 语音识别结果
     * @return 解析后的命令
     */
    private fun parseVoiceCommand(voiceResult: String): String {
        // TODO: 实现语音命令解析逻辑
        return when {
            voiceResult.contains("播放") -> "PLAY"
            voiceResult.contains("暂停") -> "PAUSE"
            voiceResult.contains("下一首") -> "NEXT"
            voiceResult.contains("上一首") -> "PREVIOUS"
            voiceResult.contains("音量") && voiceResult.contains("大") -> "VOLUME_UP"
            voiceResult.contains("音量") && voiceResult.contains("小") -> "VOLUME_DOWN"
            else -> "UNKNOWN"
        }
    }

    /**
     * 执行语音命令
     * @param command 命令
     */
    private fun executeVoiceCommand(command: String) {
        // TODO: 实现语音命令执行逻辑
        when (command) {
            "PLAY" -> {
                // 播放音乐
            }
            "PAUSE" -> {
                // 暂停音乐
            }
            "NEXT" -> {
                // 下一首
            }
            "PREVIOUS" -> {
                // 上一首
            }
            "VOLUME_UP" -> {
                // 增大音量
            }
            "VOLUME_DOWN" -> {
                // 减小音量
            }
            else -> {
                // 未知命令
            }
        }
    }
}
