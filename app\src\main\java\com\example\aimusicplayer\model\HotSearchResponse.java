package com.example.aimusicplayer.model;

import com.example.aimusicplayer.api.ApiResponse;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 热搜列表响应
 * 使用MVVM架构
 */
public class HotSearchResponse extends ApiResponse<HotSearchResponse.Data> {

    public static class Data {
        @SerializedName("data")
        private List<HotSearch> hotSearches;

        public List<HotSearch> getHotSearches() {
            return hotSearches;
        }

        public void setHotSearches(List<HotSearch> hotSearches) {
            this.hotSearches = hotSearches;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "hotSearches=" + hotSearches +
                    '}';
        }
    }

    public static class HotSearch {
        @SerializedName("searchWord")
        private String searchWord;

        @SerializedName("score")
        private int score;

        @SerializedName("content")
        private String content;

        @SerializedName("iconUrl")
        private String iconUrl;

        public String getSearchWord() {
            return searchWord;
        }

        public void setSearchWord(String searchWord) {
            this.searchWord = searchWord;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getIconUrl() {
            return iconUrl;
        }

        public void setIconUrl(String iconUrl) {
            this.iconUrl = iconUrl;
        }

        @Override
        public String toString() {
            return "HotSearch{" +
                    "searchWord='" + searchWord + '\'' +
                    ", score=" + score +
                    ", content='" + content + '\'' +
                    ", iconUrl='" + iconUrl + '\'' +
                    '}';
        }
    }
}