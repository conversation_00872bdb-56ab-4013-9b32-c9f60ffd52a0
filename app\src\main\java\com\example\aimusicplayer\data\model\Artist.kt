package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 艺术家模型类
 * 用于统一项目中的艺术家数据结构
 */
data class Artist(
    var id: Long = 0,
    var name: String = "",
    
    @SerializedName("tns")
    var translations: List<String>? = null,
    
    @SerializedName("alias")
    var aliases: List<String>? = null
) {
    /**
     * 获取艺术家名称
     * 如果名称为空，返回"未知艺术家"
     */
    fun getName(): String {
        return name.ifEmpty { "未知艺术家" }
    }
    
    /**
     * 从Java版本的Artist创建
     */
    companion object {
        @JvmStatic
        fun fromJavaArtist(artist: com.example.aimusicplayer.model.Artist): Artist {
            return Artist(
                id = artist.id,
                name = artist.name ?: "",
                translations = artist.translations,
                aliases = artist.aliases
            )
        }
    }
    
    /**
     * 转换为Java版本的Artist
     */
    fun toJavaArtist(): com.example.aimusicplayer.model.Artist {
        val javaArtist = com.example.aimusicplayer.model.Artist()
        javaArtist.id = id
        javaArtist.name = name
        javaArtist.translations = translations
        javaArtist.aliases = aliases
        return javaArtist
    }
}
