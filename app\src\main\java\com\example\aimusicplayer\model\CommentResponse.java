package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 评论响应
 */
public class CommentResponse {

    @SerializedName("code")
    private int code;

    @SerializedName("total")
    private int total;

    @SerializedName("comments")
    private List<Comment> comments;

    @SerializedName("hotComments")
    private List<Comment> hotComments;

    @SerializedName("more")
    private boolean more;

    public int getCode() {
        return code;
    }

    public int getTotal() {
        return total;
    }

    public List<Comment> getComments() {
        return comments;
    }

    public List<Comment> getHotComments() {
        return hotComments;
    }

    public boolean isMore() {
        return more;
    }

    /**
     * 评论
     */
    public static class Comment {

        @SerializedName("commentId")
        private long commentId;

        @SerializedName("user")
        private User user;

        @SerializedName("content")
        private String content;

        @SerializedName("time")
        private long time;

        @SerializedName("timeStr")
        private String timeStr;

        @SerializedName("likedCount")
        private int likedCount;

        @SerializedName("liked")
        private boolean liked;

        @SerializedName("beReplied")
        private List<Reply> beReplied;

        public long getCommentId() {
            return commentId;
        }

        public void setCommentId(long commentId) {
            this.commentId = commentId;
        }

        public User getUser() {
            return user;
        }

        public void setUser(User user) {
            this.user = user;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public String getTimeStr() {
            return timeStr;
        }

        public void setTimeStr(String timeStr) {
            this.timeStr = timeStr;
        }

        public int getLikedCount() {
            return likedCount;
        }

        public void setLikedCount(int likedCount) {
            this.likedCount = likedCount;
        }

        public boolean isLiked() {
            return liked;
        }

        public void setLiked(boolean liked) {
            this.liked = liked;
        }

        public List<Reply> getBeReplied() {
            return beReplied;
        }

        public void setBeReplied(List<Reply> beReplied) {
            this.beReplied = beReplied;
        }

        /**
         * 回复
         */
        public static class Reply {

            @SerializedName("user")
            private User user;

            @SerializedName("content")
            private String content;

            public User getUser() {
                return user;
            }

            public String getContent() {
                return content;
            }
        }

        /**
         * 用户
         */
        public static class User {

            @SerializedName("userId")
            private long userId;

            @SerializedName("nickname")
            private String nickname;

            @SerializedName("avatarUrl")
            private String avatarUrl;

            public long getUserId() {
                return userId;
            }

            public void setUserId(long userId) {
                this.userId = userId;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }
        }
    }
}
