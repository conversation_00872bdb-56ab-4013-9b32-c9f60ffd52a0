package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 歌单数据模型
 */
data class PlayList(
    var id: String = "",
    var name: String = "",

    @SerializedName("coverImgUrl")
    var coverImgUrl: String = "",

    var description: String = "",
    var creatorId: String = "",
    var creatorName: String = "",
    var songCount: Int = 0,
    var playCount: Int = 0,
    var subscribed: Boolean = false,
    var songs: MutableList<Song> = mutableListOf()
) {
    /**
     * 获取封面URL
     */
    fun getCoverUrl(): String {
        return coverImgUrl
    }

    /**
     * 设置封面URL
     */
    fun setCoverUrl(url: String) {
        coverImgUrl = url
    }

    /**
     * 从Java版本的Playlist创建
     */
    companion object {
        @JvmStatic
        fun fromJavaPlaylist(playlist: com.example.aimusicplayer.model.Playlist): PlayList {
            return PlayList(
                id = playlist.id ?: "",
                name = playlist.name ?: "",
                coverImgUrl = playlist.coverUrl ?: "",
                description = playlist.description ?: "",
                creatorId = playlist.creatorId ?: "",
                creatorName = playlist.creatorName ?: "",
                songCount = playlist.songCount,
                playCount = playlist.playCount,
                subscribed = playlist.isSubscribed,
                songs = playlist.songs?.map { Song.fromJavaSong(it) }?.toMutableList() ?: mutableListOf()
            )
        }
    }

    /**
     * 转换为Java版本的Playlist
     */
    fun toJavaPlaylist(): com.example.aimusicplayer.model.Playlist {
        val javaPlaylist = com.example.aimusicplayer.model.Playlist()
        javaPlaylist.id = id
        javaPlaylist.name = name
        javaPlaylist.coverUrl = coverImgUrl
        javaPlaylist.description = description
        javaPlaylist.creatorId = creatorId
        javaPlaylist.creatorName = creatorName
        javaPlaylist.songCount = songCount
        javaPlaylist.playCount = playCount
        javaPlaylist.isSubscribed = subscribed
        javaPlaylist.songs = songs.map { it.toJavaSong() }.toList() as List<com.example.aimusicplayer.model.SongDetailResponse.Song>
        return javaPlaylist
    }
}
