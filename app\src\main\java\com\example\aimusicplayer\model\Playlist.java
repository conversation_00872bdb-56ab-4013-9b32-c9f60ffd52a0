package com.example.aimusicplayer.model;

import java.util.List;

/**
 * 播放列表模型
 * 用于兼容旧代码
 */
public class Playlist {
    private String id;
    private String name;
    private String coverUrl;
    private String description;
    private String creatorId;
    private String creatorName;
    private int trackCount;
    private int playCount;
    private List<SongDetailResponse.Song> tracks;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public int getTrackCount() {
        return trackCount;
    }

    public void setTrackCount(int trackCount) {
        this.trackCount = trackCount;
    }

    public int getPlayCount() {
        return playCount;
    }

    public void setPlayCount(int playCount) {
        this.playCount = playCount;
    }

    public List<SongDetailResponse.Song> getTracks() {
        return tracks;
    }

    public void setTracks(List<SongDetailResponse.Song> tracks) {
        this.tracks = tracks;
    }
}
