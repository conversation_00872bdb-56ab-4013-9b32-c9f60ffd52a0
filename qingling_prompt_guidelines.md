轻聆智能车载音乐播放器 - AI辅助开发指令文档 (Java版 - 最终版)
零、项目概述与核心架构建议
项目目标：
基于提供的 car.html 横屏原型、api.txt 中的网易云音乐API以及 baidu.txt 中的百度语音技术SDK，开发一款功能完善、体验流畅的安卓智能车载音乐播放器“轻聆”（纯Java开发）。重点关注驾驶模式的优化和AI语音交互功能的实现。开发应遵循“轻聆智能车载音乐播放器开发规范”中定义的核心架构原则。
核心架构建议：
单一播放核心服务 (MusicPlaybackService - 源自开发规范):
务必实现一个统一的后台播放服务 (MusicPlaybackService.java 继承 android.app.Service)，设为前台服务以保证后台播放。
该服务独占播放器实例 (使用 ExoPlayer 实现媒体播放功能)。
管理播放状态、播放队列、当前歌曲信息、播放进度、播放/暂停状态、播放模式（顺序、随机、单曲循环、列表循环）和音量等。
核心可观察状态 (使用 Android Architecture Components MutableLiveData / LiveData - Java适用):
// 在 MusicPlaybackService.java 中定义
// 使用 public getter 方法暴露 LiveData，MutableLiveData保持 private 或 protected
private final MutableLiveData<Song> currentPlayingSong = new MutableLiveData<>(null);
private final MutableLiveData<PlaybackState> playbackState = new MutableLiveData<>(PlaybackState.IDLE); // PlaybackState: Java Enum
private final MutableLiveData<Long> playbackPosition = new MutableLiveData<>(0L);
private final MutableLiveData<Long> currentSongDuration = new MutableLiveData<>(0L);
private final MutableLiveData<PlayMode> currentPlayMode = new MutableLiveData<>(PlayMode.SEQUENCE); // PlayMode: Java Enum
private final MutableLiveData<List<Song>> currentPlaylist = new MutableLiveData<>(new ArrayList<>());
private final MutableLiveData<PlaybackError> playbackError = new MutableLiveData<>(null); // PlaybackError: Java POJO or Enum

public LiveData<Song> getCurrentPlayingSong() { return currentPlayingSong; }
public LiveData<PlaybackState> getPlaybackState() { return playbackState; }
public LiveData<Long> getPlaybackPosition() { return playbackPosition; }
public LiveData<Long> getCurrentSongDuration() { return currentSongDuration; }
public LiveData<PlayMode> getCurrentPlayMode() { return currentPlayMode; }
public LiveData<List<Song>> getCurrentPlaylist() { return currentPlaylist; }
public LiveData<PlaybackError> getPlaybackError() { return playbackError; }


实现前台服务并创建 MediaStyle 通知，集成 MediaSessionCompat。
处理音频焦点 (AudioManager.OnAudioFocusChangeListener)、耳机拔插 (ACTION_AUDIO_BECOMING_NOISY) 和蓝牙控制。
确保在不同播放情景 (本地/在线) 间无缝切换。
主架构框架 (源自开发规范):
采用单Activity (MainActivity.java) + 多Fragment架构。
MainActivity 作为容器，使用 DrawerLayout 实现侧边菜单或固定侧边栏。
用 NavigationComponent 管理Fragment切换。
视图模型 (ViewModel - 源自开发规范):
定义清晰的Java视图模型，例如 PlaybackViewModel.java (继承 androidx.lifecycle.ViewModel)，它将代理 MusicPlaybackService 的状态供UI观察。
// PlaybackViewModel.java
public class PlaybackViewModel extends ViewModel {
    private MusicPlaybackService musicPlaybackService; // 通过绑定或依赖注入获取

    // LiveData 代理 Service 的状态
    public LiveData<Song> currentSong;
    public LiveData<Boolean> isPlaying;
    public LiveData<Long> playbackPositionLiveData; // Renamed to avoid conflict with method
    public LiveData<Long> currentSongDurationLiveData; // Renamed
    public LiveData<PlayMode> currentPlayModeLiveData; // Renamed
    // ... 其他 LiveData

    // 构造函数或初始化方法中设置 musicPlaybackService 并初始化 LiveData
    // 实际项目中，Service实例通常通过依赖注入（如Hilt）或ServiceConnection获取
    public PlaybackViewModel(MusicPlaybackService service) {
        this.musicPlaybackService = service;
        if (service != null) {
            this.currentSong = service.getCurrentPlayingSong();
            this.isPlaying = Transformations.map(service.getPlaybackState(), state -> state == PlaybackState.PLAYING);
            this.playbackPositionLiveData = service.getPlaybackPosition();
            this.currentSongDurationLiveData = service.getCurrentSongDuration();
            this.currentPlayModeLiveData = service.getCurrentPlayMode();
        } else {
            // Handle null service case, perhaps initialize with empty/default LiveData
            this.currentSong = new MutableLiveData<>(null);
            this.isPlaying = new MutableLiveData<>(false);
            this.playbackPositionLiveData = new MutableLiveData<>(0L);
            this.currentSongDurationLiveData = new MutableLiveData<>(0L);
            this.currentPlayModeLiveData = new MutableLiveData<>(PlayMode.SEQUENCE);
        }
    }

    // 播放控制方法，内部调用 musicPlaybackService 的方法
    // 使用 ExecutorService 处理后台任务，Handler 更新UI或 LiveData.postValue()
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    public void playSong(Song song) {
        if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.play(song));
        }
    }
    public void playPlaylist(List<Song> playlist, int startIndex) {
         if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.play(playlist, startIndex));
        }
    }
    public void togglePlayPause() {
        if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.togglePlayPause());
        }
    }
    public void seekTo(long positionMs) {
        if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.seekTo(positionMs));
        }
    }
    public void playNext() {
        if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.playNextTrack());
        }
    }
    public void playPrevious() {
        if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.playPreviousTrack());
        }
    }
    public void setPlayMode(PlayMode mode) {
        if (musicPlaybackService != null) {
            executorService.execute(() -> musicPlaybackService.setPlayMode(mode));
        }
    }
    // ... 更多控制方法
}


UI组件（Fragment/Activity）应作为这些状态的观察者，被动接收更新并刷新UI。用户操作通过ViewModel发送指令给播放服务。
模块化设计:
根据 car.html 的页面划分，将各个主要功能模块实现为独立的Java Fragment。
API封装与调用 (源自开发规范):
基于 api.txt 中的网易云音乐API信息，使用Retrofit创建统一的 NeteaseApiService Java接口。
设计统一的Java类 Result<T> 处理API结果，包含成功和失败状态。
实现统一的错误处理和（可选的）重试机制。
AI语音集成:
根据 baidu.txt 中的百度语音SDK文档，并严格遵循您源码中已有的语音技术实现逻辑，集成ASR（语音识别）和TTS（语音合成）功能 (Java SDK)。参考开发规范第四节“语音交互系统”的具体要求。
安全性:
API密钥（网易云、百度AI的AppID, API Key, Secret Key等）严禁硬编码在Java代码中。应使用 local.properties 结合 build.gradle 读取，或通过安全的服务器配置下发。参考您源码中 assets/auth.properties 的使用方式。
数据缓存与持久化 (源自开发规范):
使用 Room 数据库 (Java注解和DAO) 缓存本地音乐信息、用户歌单等。
实现 Repository 层 (Java类) 统一管理数据访问（网络和本地）。
设计缓存策略，减少频繁网络请求。
使用 DataStore (Preference DataStore API，Java兼容) 替代 SharedPreferences 保存用户设置和登录凭证。
用户体验:
驾驶模式优先: 确保驾驶模式界面元素大、间距足、对比度高、交互简洁，语音交互作为主要操作方式。
性能: 优化列表加载、图片加载（使用Glide (Java API) 高效加载图片并缓存 - 开发规范）、数据缓存。
错误处理: 对网络请求失败、API返回错误、权限缺失等情况做友好提示（如 car.html 登录流程中提到的“弹窗提示错误”）。
状态持久化: 使用DataStore保存。
时间与歌词同步: 所有涉及播放时间、进度和歌词显示的界面，都必须从单一播放核心（通过ViewModel的LiveData）获取数据并保持同步更新。
一、应用启动与初始化 (App Startup & Initialization)
目标: 实现应用的启动流程，包括欢迎页展示、权限请求、检查登录状态并导航到相应界面。
核心组件/文件: SplashActivity.java, activity_splash.xml (XML布局), MusicApplication.java (全局Application类), AuthRepository.java(用于检查登录状态的Java接口/类), NeteaseApiService.java (Retrofit接口)。
开发规范参考: 核心架构原则 (权限请求，DataStore使用)。
AI Prompt Doc 参考: Section I (UI风格，导航逻辑)。
具体实现提示:
// AndroidManifest.xml 中设置 SplashActivity 为启动 Activity。
// SplashActivity 主题应为全屏无ActionBar (参考您源码中的 styles.xml 或 themes.xml)。
// 1. 欢迎页UI (activity_splash.xml):
//    - 遵循您源码中已有的启动页风格。
//    - UI元素: 应用Logo (ImageView id: logo_image_view), 应用名称 (TextView id: app_name_text_view text: "轻聆"), Slogan (TextView id: slogan_text_view text: "您的专属智能车载音乐伴侣")。
// 2. 初始化操作 (SplashActivity.java - onCreate 或 onResume 方法):
//    - (可选) 应用更新检查，全局配置加载。
//    - 必要权限请求: 网络, 存储 (READ_MEDIA_AUDIO for Android 10+), 麦克风 (RECORD_AUDIO)。使用 Android 6.0+ 运行时权限机制 (Java实现)。
// 3. 检查登录状态 (权限获取成功后，使用 AuthRepository):
//    - 调用 NeteaseApiService 的 /login/status 接口 (Retrofit Java调用)。
//    - 根据结果判断是否已登录。
//    - 登录凭证 (Cookie) 通过 AuthRepository 从 DataStore 读取/保存 (Java API)。
// 4. 导航逻辑 (SplashActivity.java):
//    - 欢迎页展示固定时长 (例如2-3秒，使用 Handler.postDelayed) 或初始化完成后。
//    - 已登录: Intent 跳转到 MainActivity.java。
//    - 未登录: Intent 跳转到 LoginActivity.java。
//    - finish() SplashActivity。
// 5. 全局初始化 (MusicApplication.java 继承 android.app.Application):
//    - 初始化网络库 (Retrofit, OkHttp - Java配置), 图片加载库 (Glide - Java配置), 百度AI SDK (Java SDK), Room数据库 (Java配置), DataStore等。
二、用户认证与数据服务 (User Authentication & Data Service)
目标: 实现用户登录功能（二维码、手机号、游客），并封装认证逻辑。
核心组件/文件: LoginActivity.java, activity_login.xml, AuthRepository.java (Java接口及实现类), NeteaseApiService.java (Retrofit接口), DataStore (用于凭证存储), UserProfile.java (POJO), QrCheckResponse.java (POJO), Result.java (泛型结果包装类), AuthCallback.java (回调接口)。
开发规范参考: 第二节 (AuthRepository接口定义, Result Java类, DataStore)。
AI Prompt Doc 参考: Section II (具体登录流程, API端点, UI元素参考)。
具体实现提示:
// AuthRepository.java (Java接口)
public interface AuthRepository {
    // 使用如 CompletableFuture<Result<UserProfile>> 或回调机制处理异步
    // 或者在ViewModel/Repository内部使用ExecutorService，暴露LiveData
    void loginWithQrCode(AuthCallback<UserProfile> callback); // 示例回调
    void getQrCodeKey(AuthCallback<String> callback);
    void createQrCodeImage(String key, AuthCallback<String> callback); // base64 QR image
    void checkQrCodeStatus(String key, AuthCallback<QrCheckResponse> callback);
    void loginWithPhone(String phone, String password, AuthCallback<UserProfile> callback);
    void sendCaptcha(String phone, AuthCallback<Boolean> callback);
    void loginWithCaptcha(String phone, String captcha, AuthCallback<UserProfile> callback);
    void loginAsGuest(AuthCallback<UserProfile> callback);
    void refreshLogin(AuthCallback<Boolean> callback);
    void logout(AuthCallback<Boolean> callback);

    LiveData<Boolean> getIsLoggedIn();
    LiveData<UserProfile> getCurrentUser();
}

// UserProfile.java: POJO类 (uid, nickname, avatarUrl, etc.  包含getter/setter)
// public class UserProfile { /* ... fields, getters, setters ... */ }

// QrCheckResponse.java: POJO类 (code: 800-803, cookie, message. 包含getter/setter)
// public class QrCheckResponse { /* ... fields, getters, setters ... */ }

// Result.java: 泛型类
// public class Result<T> {
//    public T data;
//    public Exception error;
//    public boolean isSuccess() { return error == null && data != null; } // Or more specific success criteria
//    private Result(T data, Exception error) { this.data = data; this.error = error; }
//    public static <T> Result<T> success(T data) { return new Result<>(data, null); }
//    public static <T> Result<T> error(Exception error) { return new Result<>(null, error); }
// }

// AuthCallback.java: public interface AuthCallback<T> { void onSuccess(T data); void onError(Exception e); }


// LoginActivity.java UI (activity_login.xml):
// - 遵循您源码中已有的登录页风格。
// - UI元素: "欢迎使用轻聆" (TextView id: welcome_text_view), 手机号输入框 (EditText id: phone_edit_text), 密码输入框 (EditText id: password_edit_text), 验证码输入框 (EditText id: captcha_edit_text), "获取验证码"按钮 (Button id: get_captcha_button), "扫码登录" (Button id: qr_login_button), "手机号登录" (Button id: phone_login_button), "游客登录" (Button id: guest_login_button), 二维码显示区域 (ImageView id: qr_code_image_view)。
// 1. 扫码登录逻辑 (LoginActivity调用AuthRepository):
//    - AuthRepository 实现:
//        - 获取 unikey (/login/qr/key)。
//        - 获取二维码图片 base64 (/login/qr/create?key={unikey}&qrimg=true)。
//        - LoginActivity: 显示二维码到 qr_code_image_view。
//        - AuthRepository: 轮询检查状态 (/login/qr/check?key={unikey}&timestamp={...}) (使用Handler和Runnable进行轮询)。
//            - 处理 800 (过期), 801 (待扫码), 802 (待确认), 803 (成功)。
//            - 成功后保存 Cookie 到 DataStore，更新内部 MutableLiveData并通过postValue()通知。
//    - LoginActivity: 观察 AuthRepository.getIsLoggedIn() LiveData，成功后跳转 MainActivity。
//    - 错误时弹窗提示。离开界面停止轮询。
// 2. 手机号登录逻辑 (LoginActivity调用AuthRepository):
//    - UI: 使用 phone_edit_text, password_edit_text, captcha_edit_text, get_captcha_button, phone_login_button。
//    - 密码登录: 调用 AuthRepository.loginWithPhone()。
//        - AuthRepository 调用 /login/cellphone?phone={...}&password={...}。
//    - 验证码登录:
//        - "获取验证码" (get_captcha_button) 调用 AuthRepository.sendCaptcha() (内部调用 /captcha/sent?phone={...}), UI启动倒计时。
//        - "登录" (phone_login_button 在验证码模式下) 调用 AuthRepository.loginWithCaptcha() (内部调用 /login/cellphone?phone={...}&captcha={...} )。
//    - 成功后保存 Cookie 到 DataStore，更新LiveData，跳转 MainActivity。
//    - 错误时弹窗提示。
// 3. 游客登录逻辑 (LoginActivity调用AuthRepository):
//    - guest_login_button 点击调用 AuthRepository.loginAsGuest() (内部调用 /register/anonimous)。
//    - 成功后保存 Cookie 到 DataStore，更新LiveData，跳转 MainActivity。
//    - 错误时弹窗提示。
// 4. 统一错误处理:
//    - AuthRepository 方法通过回调或 Result 对象传递错误。
//    - LoginActivity 根据结果显示 AlertDialog 或自定义弹窗。
三、主应用框架与导航 (Main App Frame & Navigation)
目标: 创建 MainActivity.java 作为单Activity容器，实现侧边栏导航，使用 NavigationComponent 管理Fragment切换。
核心组件/文件: MainActivity.java, activity_main.xml, layout_sidebar.xml (侧边栏布局XML), navigation_graph.xml (Navigation Component图XML), menu_sidebar.xml (如果使用NavigationView)。
开发规范参考: 核心架构 (单Activity, NavigationComponent, DrawerLayout)。
AI Prompt Doc 参考: Section III (UI元素ID, 导航目标Fragment)。
具体实现提示:
// activity_main.xml:
// - 根布局: androidx.drawerlayout.widget.DrawerLayout (id: drawer_layout)。
// - 内容区: androidx.fragment.app.FragmentContainerView (id: nav_host_fragment) 用于 NavigationComponent。
// - 侧边栏: com.google.android.material.navigation.NavigationView (id: navigation_view app:menu="@menu/menu_sidebar") 或 include layout_sidebar.xml。
// layout_sidebar.xml (如果不用NavigationView，而是自定义布局):
// - 导航项按钮 (e.g., com.google.android.material.button.MaterialButton with icon):
//    - 音乐 (id: nav_player_button, text: "音乐", icon: @drawable/ic_music)
//    - 收藏 (id: nav_favorite_button, text: "收藏", icon: @drawable/ic_favorite)
//    - ... (其他导航项)
// menu_sidebar.xml (如果使用 NavigationView):
//
//
//
// //
// navigation_graph.xml:
// - 定义所有Java Fragment destinations (e.g., <fragment android:id="@+id/nav_player_destination" android:name="your.package.PlayerFragment" ... />)。
// - 设置 PlayerFragment (或其在图中的ID) 为起始页 (app:startDestination="@id/nav_player_destination")。
// - 定义各导航项点击后触发的 navigation actions。
// MainActivity.java:
// - onCreate 方法: 设置 Toolbar (如果使用), 初始化 NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment);。
// - 使用 NavigationUI.setupWithNavController(navigationView, navController); 或手动为自定义侧边栏按钮设置 navController.navigate(R.id.destination_id);。
// - 配置 AppBarConfiguration 以正确处理 DrawerLayout 和 Toolbar 的汉堡包图标。
// - (可选) 实现侧边栏自动隐藏/显示交互逻辑 (如果不是标准 DrawerLayout 行为)。
四、全局播放服务与状态同步 (MusicPlaybackService & PlaybackViewModel)
目标: 设计健壮的后台播放服务 (MusicPlaybackService.java)，统一管理播放逻辑和状态，并通过 PlaybackViewModel.java 以响应式方式 (LiveData) 同步到UI。
核心组件/文件: MusicPlaybackService.java (ExoPlayer, LiveData, 前台服务, MediaStyle通知, MediaSessionCompat, 音频焦点, 耳机事件), PlaybackViewModel.java (代理Service状态, 提供控制方法), Song.java (POJO), PlayMode.java (Java Enum), PlaybackState.java (Java Enum), PlaybackError.java (POJO或Enum)。
开发规范参考: 核心架构 (单一Service, ExoPlayer, LiveData定义, PlaybackViewModel结构, MediaStyle通知, 音频焦点等)。
AI Prompt Doc 参考: Section X (播放队列管理, ExoPlayer事件监听, Service方法)。
具体实现提示 (重点整合开发规范和AI Prompt Doc):
// MusicPlaybackService.java (继承 android.app.Service, 设为前台服务):
// - 播放器实例: SimpleExoPlayer (管理其生命周期)。
// - 核心播放状态 (Java MutableLiveData / LiveData - 来自开发规范):
// ```java
// private final MutableLiveData currentPlayingSong = new MutableLiveData<>(null);
// private final MutableLiveData playbackState = new MutableLiveData<>(PlaybackState.IDLE);
// private final MutableLiveData playbackPosition = new MutableLiveData<>(0L);
// private final MutableLiveData currentSongDuration = new MutableLiveData<>(0L);
// private final MutableLiveData currentPlayMode = new MutableLiveData<>(PlayMode.SEQUENCE);
// private final MutableLiveData<List> currentPlaylist = new MutableLiveData<>(new ArrayList<>());
// private final MutableLiveData playbackError = new MutableLiveData<>(null);
// public LiveData getCurrentPlayingSong() { return currentPlayingSong; }
// public LiveData getPlaybackState() { return playbackState; }
// public LiveData getPlaybackPosition() { return playbackPosition; }
// public LiveData getCurrentSongDuration() { return currentSongDuration; }
// public LiveData getCurrentPlayMode() { return currentPlayMode; }
// public LiveData<List> getCurrentPlaylist() { return currentPlaylist; }
// public LiveData getPlaybackError() { return playbackError; }
// ```
// - 播放队列管理: List<Song>, 添加/移除/清空, 根据播放模式获取下一首/上一首。
// - 播放控制方法 (供ViewModel调用): play(Song song), play(List<Song> playlist, int startIndex), pause(), resume(), stopPlayback(), seekTo(long pos), playNextTrack(), playPreviousTrack(), setPlayMode(PlayMode mode), setVolume(float vol), addSongToQueue(Song song, boolean playNext), removeSongFromQueue(String songId), clearQueue()。 (这些方法内部可能需要切换到播放器线程)
// - ExoPlayer 事件监听: Player.Listener 接口实现 (onPlaybackStateChanged, onMediaItemTransition, onPlayerError等)。定时器 (Handler 和 Runnable) 更新 playbackPosition.postValue()。
// - 音频焦点处理: AudioManager.OnAudioFocusChangeListener。
// - 耳机插拔/蓝牙断开: BroadcastReceiver for ACTION_AUDIO_BECOMING_NOISY (自动暂停)。
// - MediaStyle Notification & MediaSessionCompat: 创建和更新前台服务通知，集成系统媒体控制。
// - 错误处理与网络状态监听。
// PlaybackViewModel.java (继承 androidx.lifecycle.ViewModel):
// - 依赖注入/绑定 MusicPlaybackService。 (通过 ServiceConnection 在 Activity/Application层面获取Service实例，然后传递给ViewModel工厂)
// - 暴露 LiveData 给UI (代理 MusicPlaybackService 的 LiveData)。
// ```java
// public LiveData currentSong;
// public LiveData isPlaying;
// public LiveData playbackPositionLiveData;
// public LiveData currentSongDurationLiveData;
// public LiveData currentPlayModeLiveData;
// // ... 其他 LiveData
// // ViewModelFactory is recommended for passing parameters to ViewModel constructor
// public PlaybackViewModel(MusicPlaybackService service) {
// this.musicPlaybackService = service;
// if (service != null) {
// this.currentSong = musicPlaybackService.getCurrentPlayingSong();
// this.isPlaying = Transformations.map(musicPlaybackService.getPlaybackState(), state -> state == PlaybackState.PLAYING);
// this.playbackPositionLiveData = musicPlaybackService.getPlaybackPosition();
// this.currentSongDurationLiveData = musicPlaybackService.getCurrentSongDuration();
// this.currentPlayModeLiveData = musicPlaybackService.getCurrentPlayMode();
// } else {
// // Initialize with defaults if service is null during construction
// this.currentSong = new MutableLiveData<>(null);
// this.isPlaying = new MutableLiveData<>(false);
// this.playbackPositionLiveData = new MutableLiveData<>(0L);
// this.currentSongDurationLiveData = new MutableLiveData<>(0L);
// this.currentPlayModeLiveData = new MutableLiveData<>(PlayMode.SEQUENCE);
// }
// }
// // - **提供清晰的播放控制方法给UI调用 (内部调用 `MusicPlaybackService` 的方法，使用 `ExecutorService` 处理后台调用)。** // java
// private final ExecutorService executor = Executors.newSingleThreadExecutor();
// public void togglePlayPause() {
// if (musicPlaybackService != null) {
// executor.execute(() -> musicPlaybackService.togglePlayPause());
// }
// }
// // ... other control methods
// ```
// UI层 (Java Fragments/Activities):
// - 观察 PlaybackViewModel 的 LiveData 更新UI (使用 observe 方法)。
// - 用户交互通过 PlaybackViewModel 控制播放服务。
// - 强调: 所有UI必须被动响应来自ViewModel的状态更新。
// Song.java (POJO):
// public class Song { /* String id, title, artist, album, albumArtUrl, songUrl, durationMs, etc. Getters/Setters */ }
// PlayMode.java (Enum):
// public enum PlayMode { SEQUENCE, SHUFFLE, REPEAT_ONE, REPEAT_ALL }
// PlaybackState.java (Enum):
// public enum PlaybackState { IDLE, PLAYING, PAUSED, BUFFERING, ERROR, COMPLETED }
// PlaybackError.java (POJO or Enum for error details):
// public class PlaybackError { /* String errorCode, String message; Getters/Setters */ }
五、主播放界面 (PlayerFragment)
目标: 实现核心播放界面，与 PlaybackViewModel.java 紧密同步。
核心组件/文件: PlayerFragment.java, fragment_player.xml (主播放界面布局), LyricView.java (自定义Java View), PlaybackViewModel.java。
开发规范参考: 第三节 (PlayerFragment UI模块要求)。
AI Prompt Doc 参考: Section IV (UI元素ID, 布局参考 car.html, 歌词和控制逻辑)。
具体实现提示:
// fragment_player.xml:
// - 整体布局参考 car.html 主界面结构。
// - 左侧区域 (.album-container):
//     - ImageView (id: album_art_main) 显示专辑封面。
//     - TextView (id: song_title_main) 显示歌名。
//     - TextView (id: artist_name_main) 显示歌手名。
// - 右侧歌词与控制区域 (.lyrics-container):
//     - your.package.LyricView (id: lyric_view_main) 显示歌词。
//     - 播放控制面板 (.player-control-panel):
//         - ImageButton (id: btn_playlist_main, 图标: ic_playlist 或 fas fa-list) 查看播放列表。
//         - ImageButton (id: btn_prev_main, 图标: ic_previous 或 fas fa-step-backward) 上一首。
//         - ImageButton (id: btn_play_pause_main, 图标: ic_play/ic_pause 或 fas fa-play-circle/fa-pause-circle) 播放/暂停。
//         - ImageButton (id: btn_next_main, 图标: ic_next 或 fas fa-step-forward) 下一首。
//         - ImageButton (id: btn_play_mode_main, 图标: ic_shuffle/ic_repeat/ic_repeat_one 或 fas fa-random/fa-sync-alt/fa-redo-alt) 切换播放模式。
//     - SeekBar (id: seekbar_main) 播放进度条。
//     - TextView (id: current_time_main) 显示当前播放时间。
//     - TextView (id: total_time_main) 显示歌曲总时长。
// PlayerFragment.java:
// - 获取 PlaybackViewModel 实例 (使用 ViewModelProvider)。
// - 观察 ViewModel 的 LiveData: currentSong, isPlaying, playbackPositionLiveData, currentSongDurationLiveData, currentPlayModeLiveData 并更新对应UI元素 (e.g., album_art_main.setImageURI(), song_title_main.setText(), btn_play_pause_main.setImageResource(), seekbar_main.setProgress())。
// - 歌词加载与同步: currentSong 变化时获取歌词 (API: /lyric or /lyric/new - Retrofit Java调用), 设置给 lyric_view_main.setLyricContent()。lyric_view_main.updateTime() 根据 playbackPositionLiveData 同步高亮和滚动。
// - 用户交互: 各按钮 (btn_play_pause_main, btn_prev_main, etc.) 点击调用 PlaybackViewModel 的相应方法 (togglePlayPause, playPrevious, playNext, setPlayMode)。seekbar_main 的 OnSeekBarChangeListener 调用 viewModel.seekTo()。btn_playlist_main 点击显示播放队列 (e.g., PlaylistQueueDialogFragment.java)。
// - 播放模式图标/Tooltip更新 根据 currentPlayModeLiveData 的值。
// LyricView.java (自定义View):
// - 继承 View 或 androidx.appcompat.widget.AppCompatTextView。
// - 负责解析LRC歌词文件/字符串。
// - 根据当前播放时间高亮当前行歌词。
// - 实现歌词平滑滚动。
// - 提供方法 setLyricContent(String lrcText) 和 updateTime(long currentTimeMs)。
六、我的音乐库 (MusicLibraryFragment)
目标: 管理用户收藏和本地音乐，支持分类查看和播放。
核心组件/文件: MusicLibraryFragment.java, fragment_music_library.xml, TabLayout, ViewPager2, 子Fragment (e.g., LibraryFavSongsFragment.java, LibraryLocalMusicFragment.java), Room (本地音乐数据库Java DAO和Entity: SongEntity.java, SongDao.java, AppDatabase.java), YourRepository.java (数据访问Java类), MusicLibraryPagerAdapter.java (Adapter for ViewPager2), item_library_song.xml (RecyclerView项布局)。
开发规范参考: 第三节 (MusicLibraryFragment UI模块要求, Room, Repository)。
AI Prompt Doc 参考: Section V (UI元素ID, 布局参考 car.html, 本地扫描, API调用)。
具体实现提示:
// fragment_music_library.xml:
// - UI元素: "我的音乐库"标题 (TextView id: library_title_text), 搜索框 (EditText id: library_search_input), com.google.android.material.tabs.TabLayout (id: library_tabs - 歌曲, 专辑, 歌手, 歌单, 本地), androidx.viewpager2.widget.ViewPager2 (id: library_viewpager)。
// MusicLibraryFragment.java:
// - 初始化 TabLayout 和 ViewPager2 (使用 MusicLibraryPagerAdapter.java)。
// - MusicLibraryPagerAdapter.java 继承 FragmentStateAdapter，为每个Tab创建对应的Fragment实例。
// - 子Fragments: LibraryFavSongsFragment.java, LibraryFavAlbumsFragment.java, LibraryFavArtistsFragment.java, LibraryFavPlaylistsFragment.java, LibraryLocalMusicFragment.java。
// - 统一 RecyclerView.Adapter 模式 (开发规范 - Java实现)。
// LibraryFavSongsFragment.java (及其他收藏类Fragment):
// - (ViewModel/Repository) 调用 NeteaseApiService (/likelist, /song/detail, etc.) 获取数据。
// - RecyclerView (id: fav_songs_recycler_view)。
// - item_library_song.xml (RecyclerView项布局): ImageView (id: song_item_thumbnail_lib), TextView (id: song_item_title_lib), TextView (id: song_item_artist_lib), ImageButton (id: song_item_action_lib for more options)。
// - 创建对应的 RecyclerView.Adapter (e.g., FavSongAdapter.java)。
// - 点击播放，更多操作 (移除喜欢等)。
// LibraryLocalMusicFragment.java:
// - 请求存储权限。
// - 本地音乐扫描 (MediaStore 或 MediaMetadataRetriever - Java实现) 并存入 Room 数据库。
// - SongEntity.java: Room Entity class for songs.
// - SongDao.java: Room DAO interface with methods like insertAll, getAllSongs, findById, etc.
// - AppDatabase.java: Room Database class, extending androidx.room.RoomDatabase.
// - RecyclerView (id: local_songs_recycler_view) 展示，支持排序/分组。
// - 创建对应的 RecyclerView.Adapter (e.g., LocalSongAdapter.java)。
// - 点击播放，更多操作 (编辑标签, 删除, 匹配在线信息)。
// 搜索逻辑 (在 MusicLibraryFragment 或各子Fragment):
// - library_search_input 的 TextWatcher 触发搜索/筛选。
// - 筛选本地或在线收藏内容。
七、音乐探索 (DiscoveryFragment)
目标: 提供在线音乐发现功能。
核心组件/文件: DiscoveryFragment.java, fragment_discovery.xml, NeteaseApiService.java, YourRepository.java, 各种 RecyclerView.Adapter (e.g., RecommendationAdapter.java, ChartAdapter.java, NewSongAdapter.java), item_discovery_card.xml, item_chart_card.xml, item_new_song_card.xml (RecyclerView项布局), SearchResultActivity.java (或 Fragment), PlaylistDetailFragment.java, AlbumDetailFragment.java。
开发规范参考: 第三节 (DiscoveryFragment UI模块要求, 瀑布流/卡片布局)。
AI Prompt Doc 参考: Section VI (UI元素ID, 布局参考 car.html, 各版块API)。
具体实现提示:
// fragment_discovery.xml:
// - androidx.core.widget.NestedScrollView (id: discovery_scroll_view)。
// - 顶部搜索栏: androidx.appcompat.widget.SearchView (id: discovery_search_view)。
// - 各音乐版块 (.music-section):
//     - 专属推荐: TextView (title), RecyclerView (id: rv_recommendations, horizontal)。
//     - 热门榜单: TextView (title), RecyclerView (id: rv_charts, horizontal or grid)。
//     - 新歌速递: TextView (title), com.google.android.material.tabs.TabLayout (id: tabs_new_songs_discovery), RecyclerView (id: rv_new_songs_discovery, grid)。
//     - 热门专辑: TextView (title), RecyclerView (id: rv_popular_albums, horizontal)。
// - 卡片式布局 (item_discovery_card.xml, item_chart_card.xml, item_new_song_card.xml etc.) for RecyclerView items.
// - item_discovery_card.xml: ImageView (id: music_card_cover), TextView (id: music_card_title), TextView (id: music_card_desc).
// DiscoveryFragment.java:
// - (ViewModel/Repository) 异步加载各版块数据 (API: /recommend/resource, /toplist, /top/song, /album/new etc. - Java Retrofit 调用)。
// - 为每个RecyclerView创建和设置对应的Adapter (e.g., RecommendationAdapter.java, ChartAdapter.java, NewSongAdapter.java)。
// - 搜索逻辑: discovery_search_view 的 OnQueryTextListener 调用 /search 或 /cloudsearch，导航到搜索结果页 (SearchResultActivity.java or SearchResultFragment.java)。
// - 交互: 点击 "查看更多" 导航，点击卡片/歌曲播放或进入详情页 (e.g., PlaylistDetailFragment.java, AlbumDetailFragment.java)。
八、驾驶模式 (DrivingModeFragment)
目标: 优化驾驶场景下的音乐播放体验，以语音交互为主。
核心组件/文件: DrivingModeFragment.java (或 DrivingModeActivity.java), fragment_driving_mode.xml (或 activity_driving_mode.xml), PlaybackViewModel.java, 语音交互模块 (Java实现, e.g., VoiceController.java)。
开发规范参考: 第三节 (DrivingModeFragment UI模块要求, 大按钮, 高对比度, 语音主导)。
AI Prompt Doc 参考: Section VII (UI元素ID, 布局参考 car.html, 语音控制模式)。
具体实现提示:
// fragment_driving_mode.xml:
// - 黑色背景, 大按钮, 高对比度。
// - UI元素: 专辑封面 (ImageView id: driving_album_art_dm), 歌名/歌手 (TextView ids: driving_song_title_dm, driving_artist_name_dm), 进度条 (SeekBar id: driving_seekbar_dm), 播放控制按钮 (上一首 ImageButton id: driving_btn_prev_dm, 播放/暂停 ImageButton id: driving_btn_play_pause_dm - 特大, 下一首 ImageButton id: driving_btn_next_dm)。
// - 快捷操作按钮 (MaterialButtons): 语音开关 (MaterialButton id: driving_btn_voice_toggle_dm, text: "语音", icon: ic_mic/ic_mic_off), 播放列表 (MaterialButton id: driving_btn_playlist_dm, text: "列表", icon: ic_playlist_play), 播放模式 (MaterialButton id: driving_btn_play_mode_dm, text: "顺序", icon: ic_repeat), 音量 (MaterialButton id: driving_btn_volume_dm, text: "音量", icon: ic_volume_up)。
// DrivingModeFragment.java:
// - 同步 PlaybackViewModel 状态并更新UI。
// - 播放控制通过 PlaybackViewModel。
// - 语音控制 (核心):
//    - 默认激活语音唤醒 (若已授权并在设置中开启)。
//    - driving_btn_voice_toggle_dm 手动开关语音唤醒/聆听 (更新图标和状态)。
//    - 集成语音识别、语义理解、语音合成 (参考下一节和开发规范第四节 - Java SDK)。 (可能需要一个 VoiceController.java 帮助类来管理语音SDK的交互)
//    - 根据语音指令执行播放、搜索等操作 (通过 PlaybackViewModel 或 Repository)。
// - 快捷操作实现。 (例如，点击播放列表按钮，弹出一个驾驶模式优化的播放列表对话框 DrivingPlaylistDialogFragment.java)
// - UI优化: 屏幕常亮 (getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);)。
九、语音交互系统 (全局配置)
目标: 定义和实现ASR, TTS, NLU模块 (使用Java SDK)。
核心组件/文件: 百度ASR/TTS Java SDK封装类 (e.g., BaiduAsrHelper.java, BaiduTtsHelper.java), UNIT平台接口或本地NLU模块 (Java实现, e.g., NluParser.java), VoiceCommand.java (POJO for parsed commands), VoiceEventListener.java (interface for callbacks from helpers to UI).
开发规范参考: 第四节 (ASR, TTS, NLU模块要求, 指令结构)。
AI Prompt Doc 参考: 无直接对应，但强调遵循源码中已有逻辑。
具体实现提示:
// 1. 语音识别模块 (ASR - 集成百度ASR Java SDK):
//    - 创建 BaiduAsrHelper.java 来封装SDK初始化、启动识别、停止识别、处理回调等。
//    - 定义 VoiceEventListener.java 接口 (e.g., onAsrResult(String result), onAsrError(String errorMsg), onWakeupSuccess())。
//    - 实现在线识别。
//    - 配置离线命令词。
//    - 实现语音唤醒 (自定义唤醒词 "你好，轻聆")。
//    - 语音交互状态可视化反馈 (通过回调更新UI, e.g., 麦克风动画)。
// 2. 语音合成模块 (TTS - 集成百度TTS Java SDK):
//    - 创建 BaiduTtsHelper.java 来封装SDK初始化、合成并播放语音等。
//    - 支持多种发音人。
//    - 实现语音播报反馈。
//    - 优化提示流程。
// 3. 语义理解流程 (NLU - UNIT平台或本地Java解析):
//    - 创建 NluParser.java。如果使用UNIT，则封装API调用；如果本地解析，则实现解析逻辑。
//    - 定义意图和槽位 (e.g., 意图:播放音乐, 槽位:歌曲名,歌手名)。
//    - VoiceCommand.java: POJO类，包含解析后的意图 (e.g., enum IntentType { PLAY_MUSIC, PAUSE, NEXT_SONG, SEARCH_SONG }) 和槽位值 (Map<String, String> slots)。
//    - 支持指令类别: 播放控制, 内容查询, 应用控制。
十、用户中心 (UserProfileFragment)
目标: 展示用户信息、统计及账户操作。
核心组件/文件: UserProfileFragment.java, fragment_user_profile.xml, AuthRepository.java, NeteaseApiService.java, YourRepository.java, UserViewModel.java (可选，用于封装用户数据逻辑)。
开发规范参考: 无直接对应章节，但涉及用户数据获取和登出。
AI Prompt Doc 参考: Section VIII (UI元素ID, 布局参考 car.html, API调用)。
具体实现提示:
// fragment_user_profile.xml:
// - UI元素: 用户背景 (ImageView id: user_cover_image_profile), 头像 (ImageView id: user_avatar_profile), 昵称 (TextView id: user_name_profile), VIP/等级标签 (TextView id: user_vip_tag, user_level_tag), 签名 (TextView id: user_signature_profile), 统计数据 (收藏 TextView id: stats_songs_value, 歌单数 TextView id: stats_playlists_value etc.), 账户信息列表 (e.g., using multiple TextView pairs for label and value), "退出登录"按钮 (Button id: btn_logout)。
// UserProfileFragment.java:
// - (ViewModel/Repository) 加载用户信息:
//    - 调用 /user/detail, /user/account, /user/subcount, /user/level, /vip/info (Java Retrofit)。
//    - 更新UI。
// - (可选) 统计数据点击导航。
// - 退出登录: btn_logout 点击调用 AuthRepository.logout() (内部清除DataStore凭证, 调用 /logout API), 导航回 LoginActivity。
十一、应用设置 (SettingsFragment)
目标: 提供应用配置选项，使用 DataStore (Java API) 持久化。
核心组件/文件: SettingsFragment.java, fragment_settings.xml, DataStore (Java API for Preferences DataStore), SettingsViewModel.java (可选，管理设置项的读写), SettingsCategoryAdapter.java (if using RecyclerView for categories).
开发规范参考: 第二节 (DataStore使用), 第五/六节 (部分设置项如黑暗模式, 多语言, 版本更新)。
AI Prompt Doc 参考: Section IX (UI元素ID, 布局参考 car.html, 具体设置项)。
具体实现提示:
// fragment_settings.xml:
// - UI元素: "应用设置"标题 (TextView id: settings_title), 左侧分类列表 (RecyclerView id: settings_categories_recycler_view or LinearLayout id: settings_categories_layout), 右侧设置项区域 (FrameLayout id: settings_options_container or multiple LinearLayout groups for options).
// - 设置项控件 (在右侧区域，根据分类动态加载或显示/隐藏):
// - com.google.android.material.switchmaterial.SwitchMaterial (e.g., switch_auto_play, switch_dark_mode).
// - 可点击的 LinearLayout (e.g., setting_item_language) 包含 TextView (for title/description) 和 TextView (for current value), leading to a selection dialog.
// SettingsFragment.java:
// - 分类切换逻辑: 如果使用 RecyclerView for categories, its adapter handles clicks to load/show corresponding settings options in settings_options_container (e.g., by replacing a child Fragment or toggling visibility of option groups).
// - 设置项交互与 DataStore 持久化 (Java API):
//    - 使用 androidx.datastore.preferences.core.PreferencesKeys 定义键 (e.g., PreferencesKeys.putBoolean("AUTO_PLAY")).
//    - 使用 new RxPreferenceDataStoreBuilder(context, "settings_pref_name").build() 或 PreferenceDataStoreFactory.create() 获取 DataStore<Preferences> 实例。
//    - 读取设置: dataStore.data().map(...) (using RxJava or Flow adapters for Java).
//    - 保存设置: dataStore.edit(...).
//    - 通用: 自动播放 (switch_auto_play), 夜间模式/黑暗模式 (switch_dark_mode, 可能需要 AppCompatDelegate.setDefaultNightMode()), 语言选择 (弹出 AlertDialog with choices, 更新应用配置并重启或recreate Activity), 屏幕常亮 (switch_keep_screen_on).
//    - 播放: 默认音质 (弹出 AlertDialog), 移动网络播放/下载 (switch_mobile_data_playback).
//    - 驾驶模式: 蓝牙连接自动进入 (switch_auto_driving_mode_bluetooth), 驾驶模式语音唤醒开关 (switch_driving_mode_voice_wakeup).
//    - 关于: 版本号 (TextView id: about_version_text), 用户协议/隐私政策链接 (打开 WebViewActivity.java), 清除缓存 (Button id: clear_cache_button), 检查更新 (Button id: check_update_button).
// - 读取设置并更新UI控件状态。
十二、系统集成与优化
目标: 提升应用性能、稳定性与用户体验。
核心组件/文件: 应用全局。
开发规范参考: 第五节 (性能优化, 错误处理, 用户体验优化)。
AI Prompt Doc 参考: 无直接对应，但零星提及。
具体实现提示 (全局应用):
// 1. 性能优化:
//    - 懒加载/分页加载 (RecyclerViews - 使用 Paging 3 library with Java if complex, or manual implementation for simpler cases)。
//    - Glide高效加载图片 (Java API)。
//    - RecyclerView优化 (DiffUtil, ViewHolder - Java实现)。
//    - 后台线程处理耗时操作 (ExecutorService 和 Handler 或 LiveData.postValue())。
//    - 优化应用启动时间 (Analyze APK, StrictMode, Profile GPU rendering)。
// 2. 错误处理:
//    - 统一错误提示机制 (Snackbar, Dialog)。
//    - Result<T> (Java类) 处理网络/API错误。
//    - 网络不可用时离线功能 (播放本地缓存)。
//    - 崩溃日志收集 (Firebase Crashlytics - Java SDK)。
// 3. 用户体验优化:
//    - 加载动画/过渡效果 (XML animators, FragmentTransaction animations)。
//    - 支持屏幕旋转/分屏 (正确处理 onConfigurationChanged 和保存/恢复状态)。
十三、测试与发布准备
目标: 确保应用质量，准备发布。
核心组件/文件: JUnit测试类 (src/test/java), AndroidJUnit测试类 (src/androidTest/java), Proguard配置 (proguard-rules.pro), 构建脚本 (build.gradle)。
开发规范参考: 第六节 (单元/集成测试, 发布准备)。
AI Prompt Doc 参考: 无直接对应。
具体实现提示:
// 1. 测试:
//    - 单元测试: MusicPlaybackService, PlaybackViewModel, YourRepository (JUnit, Mockito - Java测试)。
//    - UI测试/集成测试: 关键流程 (Espresso - Java测试)。
// 2. 发布准备:
//    - 版本更新检测 (需要服务器端支持)。
//    - Proguard/R8混淆与优化包大小 (minifyEnabled true, shrinkResources true)。
//    - 多语言字符串资源 (values/strings.xml, values-xx/strings.xml)。
//    - 隐私政策/用户协议 (提供文本，并在应用内可访问)。
十四、实现顺序建议 (指导AI逐步开发)
零、项目概述与核心架构建议 & 四、全局播放服务与状态同步 (MusicPlaybackService, PlaybackViewModel): 搭建核心播放框架 (Java)。
三、主应用框架与导航 (MainActivity, NavigationComponent): 构建基础UI和导航 (Java)。
二、用户认证与数据服务 (AuthRepository, LoginActivity): 实现用户登录 (Java)。
五、主播放界面 (PlayerFragment): 开发主播放UI并与核心服务联动 (Java)。
六、我的音乐库 (MusicLibraryFragment, Room): 添加音乐库功能 (Java, Room)。
七、音乐探索 (DiscoveryFragment): 实现在线发现 (Java)。
九、语音交互系统 (ASR, TTS, NLU封装) & 八、驾驶模式 (DrivingModeFragment): 开发驾驶模式并集成语音控制 (Java SDK)。
十、用户中心 (UserProfileFragment) (Java)。
十一、应用设置 (SettingsFragment, DataStore Java API)。
十二、系统集成与优化 (持续进行)。
十三、测试与发布准备。
