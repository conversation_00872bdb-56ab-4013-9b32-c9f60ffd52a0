package com.example.aimusicplayer.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build

/**
 * 网络工具类
 * 用于检查网络状态和处理网络请求结果
 */
object NetworkUtils {
    
    /**
     * 检查网络是否可用
     * @param context 上下文
     * @return 网络是否可用
     */
    @JvmStatic
    fun isNetworkAvailable(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val activeNetworkInfo = connectivityManager.activeNetworkInfo
            return activeNetworkInfo != null && activeNetworkInfo.isConnected
        }
    }
    
    /**
     * 检查是否是WiFi连接
     * @param context 上下文
     * @return 是否是WiFi连接
     */
    @JvmStatic
    fun isWifiConnected(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val wifiNetworkInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI)
            return wifiNetworkInfo != null && wifiNetworkInfo.isConnected
        }
    }
    
    /**
     * 检查是否是移动网络连接
     * @param context 上下文
     * @return 是否是移动网络连接
     */
    @JvmStatic
    fun isMobileConnected(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        } else {
            @Suppress("DEPRECATION")
            val mobileNetworkInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_MOBILE)
            return mobileNetworkInfo != null && mobileNetworkInfo.isConnected
        }
    }
}
