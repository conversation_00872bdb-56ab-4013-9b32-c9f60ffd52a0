package me.wcy.music.net.datasource

import android.net.Uri
import android.util.Log
import kotlinx.coroutines.runBlocking
import me.wcy.music.discover.DiscoverApi
import me.wcy.music.storage.preference.ConfigPreferences
import top.wangchenyan.common.ext.toast
import top.wangchenyan.common.net.apiCall

/**
 * Created by wangchenyan.top on 2024/3/26.
 */
object OnlineMusicUriFetcher {
    private const val TAG = "OnlineMusicUriFetcher"

    // 音质级别列表，按优先级排序
    private val QUALITY_LEVELS = listOf(
        "standard",    // 标准
        "higher",      // 较高
        "exhigh",      // 极高(HQ)
        "lossless",    // 无损(SQ)
        "hires",       // 高解析度无损(Hi-Res)
        "jyeffect",    // 高清环绕声(Spatial Audio)
        "sky",         // 沉浸环绕声(Surround Audio)
        "jymaster"     // 超清母带(Master)
    )

    fun fetchPlayUrl(uri: Uri): String {
        val songId = uri.getQueryParameter("id")?.toLongOrNull() ?: return uri.toString()
        return runBlocking {
            try {
                // 首先尝试用户设置的音质
                val userQuality = ConfigPreferences.playSoundQuality
                Log.d(TAG, "Fetching URL for song ID: $songId, user quality: $userQuality")

                // 尝试获取用户设置的音质
                val url = tryFetchWithQuality(songId, userQuality)
                if (url.isNotEmpty()) {
                    return@runBlocking url
                }

                // 如果失败，尝试其他音质级别
                Log.d(TAG, "Failed with user quality, trying other quality levels")
                for (quality in QUALITY_LEVELS) {
                    if (quality != userQuality) {
                        val fallbackUrl = tryFetchWithQuality(songId, quality)
                        if (fallbackUrl.isNotEmpty()) {
                            Log.d(TAG, "Successfully fetched URL with quality: $quality")
                            return@runBlocking fallbackUrl
                        }
                    }
                }

                // 如果所有尝试都失败，返回原始URI
                Log.e(TAG, "All quality levels failed, returning original URI")
                return@runBlocking uri.toString()
            } catch (e: Exception) {
                Log.e(TAG, "Exception when fetching song URL", e)
                return@runBlocking uri.toString()
            }
        }
    }

    /**
     * 尝试使用指定的音质获取歌曲URL
     *
     * @param songId 歌曲ID
     * @param quality 音质级别
     * @return 成功返回URL，失败返回空字符串
     */
    private suspend fun tryFetchWithQuality(songId: Long, quality: String): String {
        try {
            Log.d(TAG, "Trying quality: $quality for song ID: $songId")
            val res = apiCall {
                DiscoverApi.get().getSongUrl(songId, quality)
            }

            if (res.isSuccessWithData() && res.getDataOrThrow().isNotEmpty()) {
                val url = res.getDataOrThrow().first().url
                if (url.isNotEmpty()) {
                    Log.d(TAG, "Successfully fetched URL with quality $quality: $url")
                    return url
                } else {
                    Log.d(TAG, "Empty URL returned for quality: $quality")
                }
            } else {
                Log.d(TAG, "Failed to get URL with quality: $quality, response: $res")
            }
            return ""
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching URL with quality: $quality", e)
            return ""
        }
    }
}