<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.player.PlayerActivity">

    <!-- 背景图片 -->
    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <!-- 背景渐变遮罩 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部标题栏 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center_vertical|start"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/cancel"
                android:padding="12dp"
                android:src="@drawable/ic_arrow_down"
                app:tint="@android:color/white" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxWidth="240dp"
                    android:singleLine="true"
                    android:text="歌曲名称"
                    android:textColor="@android:color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_artist"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxWidth="240dp"
                    android:singleLine="true"
                    android:text="歌手名称"
                    android:textColor="#CCCCCC"
                    android:textSize="12sp" />
            </LinearLayout>
        </FrameLayout>

        <!-- 中间内容区域 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- 专辑封面视图 -->
            <com.example.aimusicplayer.ui.widget.AlbumCoverView
                android:id="@+id/album_cover_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center" />

            <!-- 歌词视图 -->
            <ScrollView
                android:id="@+id/sv_lyrics"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_lyrics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:lineSpacingExtra="8dp"
                    android:padding="16dp"
                    android:text="@string/no_lyrics"
                    android:textColor="@android:color/white"
                    android:textSize="16sp" />
            </ScrollView>
        </FrameLayout>

        <!-- 底部控制区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 进度条 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_current_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

                <SeekBar
                    android:id="@+id/seekbar_progress"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:maxHeight="2dp"
                    android:minHeight="2dp"
                    android:progressDrawable="@drawable/bg_playing_playback_progress"
                    android:thumb="@drawable/ic_playing_playback_progress_thumb" />

                <TextView
                    android:id="@+id/tv_total_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- 控制按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/btn_play_mode"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/play_mode_loop"
                    android:src="@drawable/ic_play_mode_level_list"
                    app:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/btn_prev"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/previous"
                    android:src="@drawable/ic_previous"
                    app:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/btn_play_pause"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/play"
                    android:src="@drawable/ic_playing_play_pause_selector"
                    app:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/btn_next"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/next"
                    android:src="@drawable/ic_next"
                    app:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/btn_playlist"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/playlist"
                    android:src="@drawable/ic_playlist"
                    app:tint="@android:color/white" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>