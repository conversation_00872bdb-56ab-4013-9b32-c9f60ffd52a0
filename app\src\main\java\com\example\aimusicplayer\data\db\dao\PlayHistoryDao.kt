package com.example.aimusicplayer.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.example.aimusicplayer.data.db.entity.PlayHistoryEntity
import com.example.aimusicplayer.data.db.entity.SongEntity
import kotlinx.coroutines.flow.Flow

/**
 * 播放历史DAO接口
 */
@Dao
interface PlayHistoryDao {
    /**
     * 插入播放历史
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(history: PlayHistoryEntity)
    
    /**
     * 删除播放历史
     */
    @Delete
    suspend fun delete(history: PlayHistoryEntity)
    
    /**
     * 获取所有播放历史
     */
    @Query("SELECT * FROM play_history ORDER BY play_time DESC")
    fun getAllPlayHistory(): Flow<List<PlayHistoryEntity>>
    
    /**
     * 获取最近播放的歌曲
     */
    @Transaction
    @Query("SELECT s.* FROM songs s INNER JOIN play_history h ON s.unique_id = h.song_unique_id ORDER BY h.play_time DESC LIMIT :limit")
    fun getRecentPlayedSongs(limit: Int = 50): Flow<List<SongEntity>>
    
    /**
     * 获取最近播放的歌曲ID
     */
    @Query("SELECT song_unique_id FROM play_history ORDER BY play_time DESC LIMIT 1")
    suspend fun getLastPlayedSongId(): String?
    
    /**
     * 清空播放历史
     */
    @Query("DELETE FROM play_history")
    suspend fun clearAll()
    
    /**
     * 获取指定歌曲的最后播放位置
     */
    @Query("SELECT play_position FROM play_history WHERE song_unique_id = :songUniqueId ORDER BY play_time DESC LIMIT 1")
    suspend fun getLastPlayPosition(songUniqueId: String): Long?
}
