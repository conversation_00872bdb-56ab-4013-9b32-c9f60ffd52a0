package com.example.aimusicplayer.utils

/**
 * 缓存统计信息
 * 用于记录缓存使用情况
 */
data class CacheStats(
    // 缓存命中次数
    var hitCount: Int = 0,
    
    // 缓存未命中次数
    var missCount: Int = 0,
    
    // 缓存放入次数
    var putCount: Int = 0,
    
    // 缓存淘汰次数
    var evictionCount: Int = 0,
    
    // 缓存过期次数
    var expiredCount: Int = 0
) {
    /**
     * 获取缓存命中率
     * @return 命中率（0-1）
     */
    fun getHitRate(): Float {
        val totalCount = hitCount + missCount
        return if (totalCount > 0) {
            hitCount.toFloat() / totalCount
        } else {
            0f
        }
    }
    
    /**
     * 重置统计信息
     */
    fun reset() {
        hitCount = 0
        missCount = 0
        putCount = 0
        evictionCount = 0
        expiredCount = 0
    }
    
    /**
     * 获取统计信息字符串
     */
    override fun toString(): String {
        return "CacheStats{" +
                "hitCount=" + hitCount +
                ", missCount=" + missCount +
                ", putCount=" + putCount +
                ", evictionCount=" + evictionCount +
                ", expiredCount=" + expiredCount +
                ", hitRate=" + String.format("%.2f", getHitRate() * 100) + "%" +
                '}'
    }
}
