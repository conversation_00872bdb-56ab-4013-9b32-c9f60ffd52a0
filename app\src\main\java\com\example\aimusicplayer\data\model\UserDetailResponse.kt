package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 用户详情响应
 * 对应/user/detail接口返回的数据
 */
data class UserDetailResponse(
    val code: Int,
    val message: String?,
    @SerializedName("profile")
    val userProfile: UserProfile?,
    val level: Int,
    val listenSongs: Int,
    val createTime: Long,
    val createDays: Int,
    val bindings: List<Binding>?,
    val vipType: Int,
    val vipInfo: VipInfo?
) {
    /**
     * 用户资料
     */
    data class UserProfile(
        val userId: Long,
        val nickname: String,
        val avatarUrl: String,
        val backgroundUrl: String?,
        val signature: String?,
        val followeds: Int, // 粉丝数
        val follows: Int,   // 关注数
        val eventCount: Int, // 动态数
        val playlistCount: Int, // 歌单数
        val vipType: Int,
        val gender: Int,    // 性别：0-未知，1-男，2-女
        val birthday: Long, // 生日时间戳
        val city: Int,      // 城市ID
        val province: Int,  // 省份ID
        val djStatus: Int,  // 主播状态
        val accountStatus: Int, // 账号状态
        val followed: Boolean,  // 是否已关注
        val mutual: Boolean,    // 是否互相关注
        val authenticated: Boolean, // 是否认证
        val lastLoginTime: Long,    // 最后登录时间
        val lastLoginIP: String?,   // 最后登录IP
        val remarkName: String?,    // 备注名
        val authStatus: Int,        // 认证状态
        val detailDescription: String?, // 详细描述
        val experts: Map<String, String>?, // 专家领域
        val expertTags: List<String>?,    // 专家标签
        val avatarDetail: AvatarDetail?   // 头像详情
    )

    /**
     * 头像详情
     */
    data class AvatarDetail(
        val userType: Int,
        val identityLevel: Int,
        val identityIconUrl: String?
    )

    /**
     * 绑定信息
     */
    data class Binding(
        val id: Long,
        val type: Int,
        val userId: Long,
        val bindingTime: Long,
        val tokenJsonStr: String?,
        val expiresIn: Int,
        val refreshTime: Long,
        val expired: Boolean,
        val url: String?
    )

    /**
     * VIP信息
     */
    data class VipInfo(
        val associator: Associator?
    ) {
        data class Associator(
            val vipCode: Int,
            val rights: Boolean,
            val expireTime: Long
        )
    }
}
