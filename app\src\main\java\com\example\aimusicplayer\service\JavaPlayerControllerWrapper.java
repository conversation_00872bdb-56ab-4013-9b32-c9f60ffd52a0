package com.example.aimusicplayer.service;

import androidx.lifecycle.LiveData;
import androidx.media3.common.MediaItem;
import androidx.media3.common.Player;

import com.example.aimusicplayer.data.model.Song;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * PlayerController接口的Java包装类
 * 用于Java代码调用Kotlin接口
 */
@Singleton
public class JavaPlayerControllerWrapper {
    
    private final PlayerController playerController;
    
    @Inject
    public JavaPlayerControllerWrapper(PlayerController playerController) {
        this.playerController = playerController;
    }
    
    /**
     * 播放指定歌曲
     * @param song 要播放的歌曲
     */
    public void play(Song song) {
        playerController.play(song);
    }
    
    /**
     * 播放指定歌曲列表
     * @param songs 要播放的歌曲列表
     * @param startIndex 起始索引
     */
    public void play(List<Song> songs, int startIndex) {
        playerController.play(songs, startIndex);
    }
    
    /**
     * 播放/暂停
     */
    public void togglePlayPause() {
        playerController.togglePlayPause();
    }
    
    /**
     * 播放
     */
    public void play() {
        playerController.play();
    }
    
    /**
     * 暂停
     */
    public void pause() {
        playerController.pause();
    }
    
    /**
     * 停止
     */
    public void stop() {
        playerController.stop();
    }
    
    /**
     * 下一首
     */
    public void next() {
        playerController.next();
    }
    
    /**
     * 上一首
     */
    public void previous() {
        playerController.previous();
    }
    
    /**
     * 跳转到指定位置
     * @param position 位置（毫秒）
     */
    public void seekTo(long position) {
        playerController.seekTo(position);
    }
    
    /**
     * 设置播放模式
     * @param mode 播放模式
     */
    public void setPlayMode(int mode) {
        playerController.setPlayMode(mode);
    }
    
    /**
     * 切换播放模式
     */
    public void togglePlayMode() {
        playerController.togglePlayMode();
    }
    
    /**
     * 获取当前播放状态
     * @return 播放状态LiveData
     */
    public LiveData<PlayState> getPlayState() {
        return playerController.getPlayState();
    }
    
    /**
     * 获取当前播放模式
     * @return 播放模式
     */
    public int getPlayMode() {
        return playerController.getPlayMode();
    }
    
    /**
     * 获取当前播放位置
     * @return 播放位置（毫秒）
     */
    public long getCurrentPosition() {
        return playerController.getCurrentPosition();
    }
    
    /**
     * 获取当前歌曲时长
     * @return 歌曲时长（毫秒）
     */
    public long getDuration() {
        return playerController.getDuration();
    }
    
    /**
     * 获取当前播放列表
     * @return 播放列表
     */
    public List<MediaItem> getPlaylist() {
        return playerController.getPlaylist();
    }
    
    /**
     * 获取当前播放索引
     * @return 播放索引
     */
    public int getCurrentIndex() {
        return playerController.getCurrentIndex();
    }
    
    /**
     * 获取播放器实例
     * @return 播放器实例
     */
    public Player getPlayer() {
        return playerController.getPlayer();
    }
    
    /**
     * 添加歌曲到播放列表
     * @param song 要添加的歌曲
     */
    public void addToPlaylist(Song song) {
        playerController.addToPlaylist(song);
    }
    
    /**
     * 添加歌曲列表到播放列表
     * @param songs 要添加的歌曲列表
     */
    public void addToPlaylist(List<Song> songs) {
        playerController.addToPlaylist(songs);
    }
    
    /**
     * 从播放列表中移除歌曲
     * @param index 要移除的歌曲索引
     */
    public void removeFromPlaylist(int index) {
        playerController.removeFromPlaylist(index);
    }
    
    /**
     * 清空播放列表
     */
    public void clearPlaylist() {
        playerController.clearPlaylist();
    }
}
