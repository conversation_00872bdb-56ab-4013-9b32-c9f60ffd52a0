package com.example.aimusicplayer.utils

import android.util.Log
import com.example.aimusicplayer.data.model.LyricInfo as KotlinLyricInfo
import com.example.aimusicplayer.data.model.LyricLine as KotlinLyricLine
import com.example.aimusicplayer.model.LyricInfo
import com.example.aimusicplayer.model.LyricEntry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.regex.Pattern
import java.util.regex.PatternSyntaxException

/**
 * 增强型歌词解析器
 * 提供更高效的歌词解析和处理功能
 */
object EnhancedLyricParser {
    private const val TAG = "EnhancedLyricParser"

    // 歌词时间标签正则表达式
    private val timeTagPattern = Pattern.compile("\\[(\\d{2}):(\\d{2})\\.(\\d{2,3})\\]")

    // 歌词信息标签正则表达式
    private val infoTagPattern = Pattern.compile("\\[(\\w+):(.+?)\\]")

    // 翻译歌词标记
    private const val TRANSLATION_SEPARATOR = "【"

    /**
     * 解析LRC格式歌词
     * @param lrcContent LRC格式歌词内容
     * @return 解析后的歌词信息
     */
    suspend fun parseLrc(lrcContent: String): LyricInfo = withContext(Dispatchers.Default) {
        val lyricInfo = LyricInfo()

        try {
            // 按行分割歌词
            val lines = lrcContent.split("\n", "\r\n")

            // 临时存储歌词行
            val tempEntries = mutableListOf<LyricEntry>()

            // 解析每一行
            for (line in lines) {
                val trimmedLine = line.trim()
                if (trimmedLine.isEmpty()) continue

                // 尝试解析信息标签
                val infoMatcher = infoTagPattern.matcher(trimmedLine)
                if (infoMatcher.matches()) {
                    val key = infoMatcher.group(1)?.lowercase()
                    val value = infoMatcher.group(2)

                    // 设置歌词信息
                    when (key) {
                        "ti" -> lyricInfo.setTitle(value ?: "")
                        "ar" -> lyricInfo.setArtist(value ?: "")
                        "al" -> lyricInfo.setAlbum(value ?: "")
                        "by" -> lyricInfo.setLyricist(value ?: "")
                        "offset" -> {
                            // 处理时间偏移
                        }
                    }
                    continue
                }

                // 尝试解析时间标签
                val timeMatcher = timeTagPattern.matcher(trimmedLine)
                if (timeMatcher.find()) {
                    // 重置匹配器位置
                    timeMatcher.reset()

                    // 提取歌词文本
                    var text = trimmedLine
                    val times = mutableListOf<Long>()

                    // 提取所有时间标签
                    while (timeMatcher.find()) {
                        val min = timeMatcher.group(1)?.toIntOrNull() ?: 0
                        val sec = timeMatcher.group(2)?.toIntOrNull() ?: 0
                        val millis = timeMatcher.group(3)?.let {
                            if (it.length == 2) it.toIntOrNull()?.times(10) ?: 0
                            else it.toIntOrNull() ?: 0
                        } ?: 0

                        // 计算总时间（毫秒）
                        val time = (min * 60 * 1000 + sec * 1000 + millis).toLong()
                        times.add(time)

                        // 移除时间标签
                        text = text.replace("[${timeMatcher.group(0)?.substring(1, timeMatcher.group(0)?.length ?: 0) ?: ""}]", "")
                    }

                    // 处理文本，检查是否有翻译
                    var mainText = text.trim()
                    var translation: String? = null

                    // 检查是否包含翻译分隔符
                    val translationIndex = mainText.indexOf(TRANSLATION_SEPARATOR)
                    if (translationIndex > 0) {
                        translation = mainText.substring(translationIndex).trim()
                        mainText = mainText.substring(0, translationIndex).trim()
                    }

                    // 为每个时间标签创建歌词行
                    for (time in times) {
                        tempEntries.add(LyricEntry(time, mainText, translation))
                    }
                }
            }

            // 按时间排序
            val sortedEntries = tempEntries.sortedBy { it.time }

            // 设置歌词条目
            lyricInfo.setEntries(sortedEntries)

            return@withContext lyricInfo
        } catch (e: Exception) {
            Log.e(TAG, "解析歌词失败", e)
            return@withContext lyricInfo
        }
    }

    /**
     * 解析带有翻译的歌词
     * @param mainLrc 主歌词内容
     * @param translationLrc 翻译歌词内容
     * @return 解析后的歌词信息
     */
    suspend fun parseWithTranslation(mainLrc: String, translationLrc: String): LyricInfo = withContext(Dispatchers.Default) {
        try {
            // 解析主歌词
            val mainLyricInfo = parseLrc(mainLrc)

            // 解析翻译歌词
            val translationLyricInfo = parseLrc(translationLrc)

            // 合并歌词
            mergeLyrics(mainLyricInfo, translationLyricInfo)
        } catch (e: Exception) {
            Log.e(TAG, "解析带翻译歌词失败", e)
            parseLrc(mainLrc)
        }
    }

    /**
     * 合并主歌词和翻译歌词
     * @param mainLyricInfo 主歌词信息
     * @param translationLyricInfo 翻译歌词信息
     * @return 合并后的歌词信息
     */
    private fun mergeLyrics(mainLyricInfo: LyricInfo, translationLyricInfo: LyricInfo): LyricInfo {
        val result = LyricInfo()
        result.setTitle(mainLyricInfo.getTitle() ?: "")
        result.setArtist(mainLyricInfo.getArtist() ?: "")
        result.setAlbum(mainLyricInfo.getAlbum() ?: "")
        result.setLyricist(mainLyricInfo.getLyricist() ?: "")

        // 获取主歌词和翻译歌词的条目
        val mainEntries = mainLyricInfo.getEntries()
        val translationEntries = translationLyricInfo.getEntries()

        // 如果翻译歌词为空，直接返回主歌词
        if (translationEntries.isEmpty()) {
            result.setEntries(mainEntries)
            return result
        }

        // 合并歌词
        val mergedEntries = mutableListOf<LyricEntry>()

        for (mainEntry in mainEntries) {
            // 查找最接近的翻译歌词
            val closestTranslation = findClosestTranslation(mainEntry, translationEntries)

            // 创建新的歌词行
            val mergedLine = LyricEntry(
                mainEntry.getTime(),
                mainEntry.getText() ?: "",
                closestTranslation?.getText()
            )

            mergedEntries.add(mergedLine)
        }

        // 设置合并后的歌词条目
        result.setEntries(mergedEntries)

        return result
    }

    /**
     * 查找最接近的翻译歌词
     * @param mainEntry 主歌词行
     * @param translationEntries 翻译歌词条目列表
     * @return 最接近的翻译歌词行
     */
    private fun findClosestTranslation(mainEntry: LyricEntry, translationEntries: List<LyricEntry>): LyricEntry? {
        if (translationEntries.isEmpty()) return null

        // 查找时间最接近的翻译歌词
        var closestEntry: LyricEntry? = null
        var minTimeDiff = Long.MAX_VALUE

        for (translationEntry in translationEntries) {
            val timeDiff = Math.abs(translationEntry.getTime() - mainEntry.getTime())
            if (timeDiff < minTimeDiff) {
                minTimeDiff = timeDiff
                closestEntry = translationEntry
            }
        }

        // 如果时间差太大（超过2秒），认为没有对应的翻译
        return if (minTimeDiff <= 2000) closestEntry else null
    }

    /**
     * 将歌词信息转换为LRC格式字符串
     * @param lyricInfo 歌词信息
     * @return LRC格式字符串
     */
    fun toLrcString(lyricInfo: LyricInfo): String {
        val sb = StringBuilder()

        // 添加歌词信息
        val title = lyricInfo.getTitle()
        val artist = lyricInfo.getArtist()
        val album = lyricInfo.getAlbum()
        val lyricist = lyricInfo.getLyricist()

        if (!title.isNullOrEmpty()) sb.appendLine("[ti:$title]")
        if (!artist.isNullOrEmpty()) sb.appendLine("[ar:$artist]")
        if (!album.isNullOrEmpty()) sb.appendLine("[al:$album]")
        if (!lyricist.isNullOrEmpty()) sb.appendLine("[by:$lyricist]")

        // 添加歌词条目
        for (entry in lyricInfo.getEntries()) {
            val timeTag = formatTimeTag(entry.getTime())

            if (entry.hasTranslation()) {
                sb.appendLine("$timeTag${entry.getText()} ${TRANSLATION_SEPARATOR}${entry.getTranslatedText()}")
            } else {
                sb.appendLine("$timeTag${entry.getText()}")
            }
        }

        return sb.toString()
    }

    /**
     * 格式化时间标签
     * @param timeMs 时间（毫秒）
     * @return 格式化的时间标签，如[00:12.34]
     */
    private fun formatTimeTag(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        val milliseconds = timeMs % 1000 / 10

        return String.format("[%02d:%02d.%02d]", minutes, seconds, milliseconds)
    }
}
