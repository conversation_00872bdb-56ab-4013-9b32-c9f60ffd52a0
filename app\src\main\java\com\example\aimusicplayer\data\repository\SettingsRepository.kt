package com.example.aimusicplayer.data.repository

import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设置仓库，负责管理应用设置
 * 使用Hilt依赖注入
 * 继承自BaseRepository
 */
@Singleton
class SettingsRepository @Inject constructor(
    private val sharedPreferences: SharedPreferences
) : BaseRepository() {

    companion object {
        private const val TAG = "SettingsRepository"

        private const val KEY_AUTO_PLAY = "auto_play"
        private const val KEY_NIGHT_MODE = "night_mode"
        private const val KEY_AUTO_VOICE_IN_DRIVING = "auto_voice_in_driving"
    }

    // 自动播放设置的StateFlow
    private val _autoPlayEnabledFlow = MutableStateFlow(isAutoPlayEnabled())
    val autoPlayEnabledFlow: Flow<Boolean> = _autoPlayEnabledFlow.asStateFlow()

    // 夜间模式设置的StateFlow
    private val _nightModeEnabledFlow = MutableStateFlow(isNightModeEnabled())
    val nightModeEnabledFlow: Flow<Boolean> = _nightModeEnabledFlow.asStateFlow()

    // 驾驶模式自动语音设置的StateFlow
    private val _autoVoiceInDrivingEnabledFlow = MutableStateFlow(isAutoVoiceInDrivingEnabled())
    val autoVoiceInDrivingEnabledFlow: Flow<Boolean> = _autoVoiceInDrivingEnabledFlow.asStateFlow()

    /**
     * 是否启用自动播放
     * @return 是否启用
     */
    fun isAutoPlayEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_PLAY, false)
    }

    /**
     * 设置自动播放
     * @param enabled 是否启用
     */
    fun setAutoPlayEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_AUTO_PLAY, enabled).apply()
        _autoPlayEnabledFlow.value = enabled
        Log.d(TAG, "设置自动播放: $enabled")
    }

    /**
     * 是否启用夜间模式
     * @return 是否启用
     */
    fun isNightModeEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_NIGHT_MODE, false)
    }

    /**
     * 设置夜间模式
     * @param enabled 是否启用
     */
    fun setNightModeEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_NIGHT_MODE, enabled).apply()
        _nightModeEnabledFlow.value = enabled
        Log.d(TAG, "设置夜间模式: $enabled")
    }

    /**
     * 是否启用驾驶模式自动语音
     * @return 是否启用
     */
    fun isAutoVoiceInDrivingEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_VOICE_IN_DRIVING, false)
    }

    /**
     * 设置驾驶模式自动语音
     * @param enabled 是否启用
     */
    fun setAutoVoiceInDrivingEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_AUTO_VOICE_IN_DRIVING, enabled).apply()
        _autoVoiceInDrivingEnabledFlow.value = enabled
        Log.d(TAG, "设置驾驶模式自动语音: $enabled")
    }

    /**
     * 重置所有设置
     */
    fun resetAllSettings() {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_PLAY, false)
            .putBoolean(KEY_NIGHT_MODE, false)
            .putBoolean(KEY_AUTO_VOICE_IN_DRIVING, false)
            .apply()

        // 更新StateFlow
        _autoPlayEnabledFlow.value = false
        _nightModeEnabledFlow.value = false
        _autoVoiceInDrivingEnabledFlow.value = false

        Log.d(TAG, "重置所有设置")
    }
}
