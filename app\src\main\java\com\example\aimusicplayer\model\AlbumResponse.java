package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 专辑响应模型
 */
public class AlbumResponse {
    private int code;
    private Album album;
    private List<SongDetailResponse.Song> songs;
    
    public int getCode() {
        return code;
    }
    
    public Album getAlbum() {
        return album;
    }
    
    public List<SongDetailResponse.Song> getSongs() {
        return songs;
    }
    
    public static class Album {
        private long id;
        private String name;
        
        @SerializedName("picUrl")
        private String picUrl;
        
        private List<SongDetailResponse.Artist> artists;
        private String description;
        
        public long getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public String getPicUrl() {
            return picUrl;
        }
        
        public List<SongDetailResponse.Artist> getArtists() {
            return artists;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
