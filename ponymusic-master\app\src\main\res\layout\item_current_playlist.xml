<?xml version="1.0" encoding="utf-8"?>
<com.hjq.shape.layout.ShapeLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="8dp"
    app:shape_solidColor="@color/transparent"
    app:shape_solidSelectedColor="@color/common_background_color">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal">

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textSize="15dp"
            app:shape_textColor="@color/common_text_h1_color"
            app:shape_textSelectedColor="@color/common_theme_color"
            tools:text="听不到" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvArtist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textSize="12dp"
            app:shape_textColor="@color/common_text_h2_color"
            app:shape_textSelectedColor="@color/common_theme_color"
            tools:text=" · 五月天" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ivDelete"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:padding="10dp"
        android:scaleType="centerInside"
        android:src="@drawable/ic_close"
        app:tint="@color/common_text_h2_color" />
</com.hjq.shape.layout.ShapeLinearLayout>